#!/usr/bin/env node

const { ethers } = require('ethers');
const chalk = require('chalk');

// Load environment variables
require('dotenv').config();

// Configuration
const config = {
  rpcUrl: process.env.RPC_URL,
  chainId: parseInt(process.env.CHAIN_ID || '11155111'),
  minArbitrageSpread: parseFloat(process.env.MIN_ARBITRAGE_SPREAD || '0.5'),
  minProfitWei: process.env.MIN_PROFIT_WEI || '1000000000000000',
  flashloanTokens: (process.env.FLASHLOAN_TOKENS || 'WETH,USDC').split(','),
  flashloanPrimaryToken: process.env.FLASHLOAN_PRIMARY_TOKEN || 'WETH',
  flashloanTargetTokens: (process.env.FLASHLOAN_TARGET_TOKENS || 'USDC').split(','),
  flashloanDexPairs: (process.env.FLASHLOAN_DEX_PAIRS || 'UNISWAP_V2,UNISWAP_V3').split(',')
};

// Sepolia addresses
const SEPOLIA_ADDRESSES = {
  WETH: '******************************************',
  USDC: '******************************************',
  UNISWAP_V2_FACTORY: '******************************************',
  UNISWAP_V3_FACTORY: '******************************************',
  UNISWAP_V2_ROUTER: '******************************************',
  UNISWAP_V3_ROUTER: '******************************************'
};

// ABIs
const FACTORY_V2_ABI = [
  'function getPair(address tokenA, address tokenB) view returns (address pair)'
];

const FACTORY_V3_ABI = [
  'function getPool(address tokenA, address tokenB, uint24 fee) view returns (address pool)'
];

const PAIR_ABI = [
  'function getReserves() view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
  'function token0() view returns (address)',
  'function token1() view returns (address)',
  'function totalSupply() view returns (uint256)'
];

const POOL_V3_ABI = [
  'function slot0() view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)',
  'function liquidity() view returns (uint128)',
  'function token0() view returns (address)',
  'function token1() view returns (address)',
  'function fee() view returns (uint24)'
];

async function main() {
  console.log(chalk.blue.bold('🔍 Flashloan Opportunity Diagnostic Tool'));
  console.log(chalk.gray('═'.repeat(60)));
  
  // Initialize provider
  const provider = new ethers.JsonRpcProvider(config.rpcUrl);
  
  try {
    // Test connection
    const blockNumber = await provider.getBlockNumber();
    console.log(chalk.green(`✅ Connected to Sepolia (Block: ${blockNumber})`));
  } catch (error) {
    console.log(chalk.red(`❌ Connection failed: ${error.message}`));
    return;
  }
  
  console.log(chalk.yellow.bold('\n📊 Configuration Analysis'));
  console.log(chalk.gray('─'.repeat(40)));
  console.log(`${chalk.cyan('Chain ID:')} ${config.chainId}`);
  console.log(`${chalk.cyan('Min Arbitrage Spread:')} ${config.minArbitrageSpread}%`);
  console.log(`${chalk.cyan('Min Profit:')} ${ethers.formatEther(config.minProfitWei)} ETH`);
  console.log(`${chalk.cyan('Primary Token:')} ${config.flashloanPrimaryToken}`);
  console.log(`${chalk.cyan('Target Tokens:')} ${config.flashloanTargetTokens.join(', ')}`);
  console.log(`${chalk.cyan('DEX Pairs:')} ${config.flashloanDexPairs.join(', ')}`);
  
  // Check pool existence and liquidity
  console.log(chalk.yellow.bold('\n🏊 Pool Analysis'));
  console.log(chalk.gray('─'.repeat(40)));
  
  const tokenA = SEPOLIA_ADDRESSES.WETH;
  const tokenB = SEPOLIA_ADDRESSES.USDC;
  
  console.log(`\n${chalk.white.bold('WETH/USDC Pools:')}`);
  console.log(`Token A (WETH): ${tokenA}`);
  console.log(`Token B (USDC): ${tokenB}`);
  
  // Check Uniswap V2 pool
  try {
    const v2Factory = new ethers.Contract(SEPOLIA_ADDRESSES.UNISWAP_V2_FACTORY, FACTORY_V2_ABI, provider);
    const v2PairAddress = await v2Factory.getPair(tokenA, tokenB);
    
    if (v2PairAddress === ethers.ZeroAddress) {
      console.log(`${chalk.red('❌ Uniswap V2:')} No pool exists`);
    } else {
      console.log(`${chalk.green('✅ Uniswap V2:')} ${v2PairAddress}`);
      
      // Get reserves
      const v2Pair = new ethers.Contract(v2PairAddress, PAIR_ABI, provider);
      const [reserve0, reserve1] = await v2Pair.getReserves();
      const token0 = await v2Pair.token0();
      const totalSupply = await v2Pair.totalSupply();
      
      const isWethToken0 = token0.toLowerCase() === tokenA.toLowerCase();
      const wethReserve = isWethToken0 ? reserve0 : reserve1;
      const usdcReserve = isWethToken0 ? reserve1 : reserve0;
      
      console.log(`   WETH Reserve: ${ethers.formatEther(wethReserve)} WETH`);
      console.log(`   USDC Reserve: ${ethers.formatUnits(usdcReserve, 6)} USDC`);
      console.log(`   Total Supply: ${ethers.formatEther(totalSupply)} LP tokens`);
      
      // Calculate price
      const v2Price = Number(ethers.formatUnits(usdcReserve, 6)) / Number(ethers.formatEther(wethReserve));
      console.log(`   Price: ${v2Price.toFixed(2)} USDC/WETH`);
    }
  } catch (error) {
    console.log(`${chalk.red('❌ Uniswap V2:')} Error - ${error.message}`);
  }
  
  // Check Uniswap V3 pools
  const v3Fees = [500, 3000, 10000]; // 0.05%, 0.3%, 1%
  
  for (const fee of v3Fees) {
    try {
      const v3Factory = new ethers.Contract(SEPOLIA_ADDRESSES.UNISWAP_V3_FACTORY, FACTORY_V3_ABI, provider);
      const v3PoolAddress = await v3Factory.getPool(tokenA, tokenB, fee);
      
      if (v3PoolAddress === ethers.ZeroAddress) {
        console.log(`${chalk.red('❌ Uniswap V3')} (${fee/100}%): No pool exists`);
      } else {
        console.log(`${chalk.green('✅ Uniswap V3')} (${fee/100}%): ${v3PoolAddress}`);
        
        // Get pool data
        const v3Pool = new ethers.Contract(v3PoolAddress, POOL_V3_ABI, provider);
        const [slot0, liquidity] = await Promise.all([
          v3Pool.slot0(),
          v3Pool.liquidity()
        ]);
        
        const sqrtPriceX96 = slot0[0];
        const tick = slot0[1];
        
        // Calculate price from sqrtPriceX96 (corrected calculation)
        // Use BigInt for precision to avoid overflow
        const sqrtPriceX96BigInt = BigInt(sqrtPriceX96.toString());
        const Q96 = BigInt(2) ** BigInt(96);

        // Calculate price = (sqrtPriceX96 / 2^96)^2
        const numerator = sqrtPriceX96BigInt * sqrtPriceX96BigInt;
        const denominator = Q96 * Q96;

        // Convert to number and adjust for decimals
        const price = Number(numerator) / Number(denominator);
        const decimalsAdjustment = 10 ** (6 - 18); // USDC (6) - WETH (18) = -12
        const adjustedPrice = price * decimalsAdjustment;

        // Check token ordering and adjust if needed
        const token0 = await v3Pool.token0();
        const isToken0WETH = token0.toLowerCase() === tokenA.toLowerCase();
        const finalPrice = isToken0WETH ? adjustedPrice : (1 / adjustedPrice);
        
        console.log(`   Liquidity: ${liquidity.toString()}`);
        console.log(`   Tick: ${tick}`);
        console.log(`   Price: ${finalPrice.toFixed(2)} USDC/WETH`);
      }
    } catch (error) {
      console.log(`${chalk.red('❌ Uniswap V3')} (${fee/100}%): Error - ${error.message}`);
    }
  }
  
  // Analyze potential issues
  console.log(chalk.yellow.bold('\n🎯 Opportunity Detection Issues'));
  console.log(chalk.gray('─'.repeat(40)));
  
  const issues = [];
  const suggestions = [];
  
  // Check minimum profit threshold
  const minProfitEth = parseFloat(ethers.formatEther(config.minProfitWei));
  if (minProfitEth > 0.01) {
    issues.push(`Min profit too high: ${minProfitEth} ETH`);
    suggestions.push('Lower MIN_PROFIT_WEI to 100000000000000 (0.0001 ETH)');
  }
  
  // Check arbitrage spread
  if (config.minArbitrageSpread > 1.0) {
    issues.push(`Min arbitrage spread too high: ${config.minArbitrageSpread}%`);
    suggestions.push('Lower MIN_ARBITRAGE_SPREAD to 0.1 or 0.2');
  }
  
  // Check token pairs
  if (config.flashloanTargetTokens.length < 2) {
    issues.push('Limited token pairs for arbitrage');
    suggestions.push('Add more tokens if available on Sepolia');
  }
  
  if (issues.length === 0) {
    console.log(chalk.green('✅ No obvious configuration issues found'));
  } else {
    console.log(chalk.red('❌ Potential Issues:'));
    issues.forEach(issue => console.log(`   • ${issue}`));
    
    console.log(chalk.yellow('\n💡 Suggestions:'));
    suggestions.forEach(suggestion => console.log(`   • ${suggestion}`));
  }
  
  // Check flashloan strategy thresholds
  console.log(chalk.yellow.bold('\n⚙️  Flashloan Strategy Analysis'));
  console.log(chalk.gray('─'.repeat(40)));
  
  console.log(`${chalk.cyan('Testnet MIN_PROFIT_THRESHOLD:')} 0.2% (updated in strategy)`);
  console.log(`${chalk.cyan('Confidence Requirement:')} >= 40% (updated for testnet)`);
  console.log(`${chalk.cyan('Current Min Spread:')} ${config.minArbitrageSpread}%`);

  if (config.minArbitrageSpread < 0.2) {
    console.log(chalk.yellow('⚠️  Strategy threshold (0.2%) is higher than config spread'));
    console.log(chalk.yellow('   Consider raising MIN_ARBITRAGE_SPREAD to 0.2% or higher'));
  } else {
    console.log(chalk.green('✅ Strategy and config thresholds are compatible'));
  }
  
  console.log(chalk.yellow.bold('\n🔧 Recommended Fixes'));
  console.log(chalk.gray('─'.repeat(40)));
  console.log('✅ MIN_PROFIT_WEI already lowered to 0.0001 ETH');
  console.log('✅ MIN_ARBITRAGE_SPREAD already lowered to 0.1%');
  console.log('✅ Flashloan strategy thresholds updated for testnet');
  console.log('✅ Debug logging enabled');
  console.log('');
  console.log('🚀 Next steps:');
  console.log('1. Restart the MEV bot to apply changes');
  console.log('2. Monitor debug logs for opportunity detection');
  console.log('3. Check if pools have sufficient liquidity for arbitrage');
  
  console.log(chalk.green.bold('\n✨ Diagnostic completed!'));
}

main().catch(console.error);
