// Simple test to verify the build works
const { execSync } = require('child_process');

console.log('🧪 Testing MEV Bot build...');

try {
  // Test TypeScript compilation
  console.log('📦 Building TypeScript...');
  execSync('npm run build', { stdio: 'inherit' });
  
  console.log('✅ Build successful!');
  console.log('');
  console.log('🚀 To run the MEV bot:');
  console.log('1. Copy .env.example to .env');
  console.log('2. Configure your environment variables');
  console.log('3. Run: npm run dev (for development)');
  console.log('4. Or: npm start (for production)');
  console.log('');
  console.log('⚠️  Remember to set DRY_RUN=true for testing!');
  
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}
