const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("Minimal FlashloanArbitrage Test", function () {
  let flashloanArbitrage;
  let owner;
  let mockPoolAddressesProvider;
  let mockAavePool;
  let mockUSDC;
  let mockWETH;
  let mockDEXRouter;

  beforeEach(async function () {
    [owner] = await ethers.getSigners();

    // Deploy MockAavePool
    const MockAavePoolFactory = await ethers.getContractFactory("MockAavePool");
    mockAavePool = await MockAavePoolFactory.deploy();
    await mockAavePool.waitForDeployment();

    // Deploy MockPoolAddressesProvider
    const MockPoolAddressesProviderFactory = await ethers.getContractFactory("MockPoolAddressesProvider");
    mockPoolAddressesProvider = await MockPoolAddressesProviderFactory.deploy(mockAavePool.target);
    await mockPoolAddressesProvider.waitForDeployment();

    // Deploy MockERC20 tokens
    const MockERC20Factory = await ethers.getContractFactory("MockERC20");
    mockUSDC = await MockERC20Factory.deploy("Mock USDC", "mUSDC");
    await mockUSDC.waitForDeployment();
    mockWETH = await MockERC20Factory.deploy("Mock WETH", "mWETH");
    await mockWETH.waitForDeployment();

    // Deploy MockDEXRouter
    const MockDEXRouterFactory = await ethers.getContractFactory("MockDEXRouter");
    mockDEXRouter = await MockDEXRouterFactory.deploy();
    await mockDEXRouter.waitForDeployment();

    // Deploy FlashloanArbitrage contract
    const FlashloanArbitrageFactory = await ethers.getContractFactory("FlashloanArbitrage");
    flashloanArbitrage = await FlashloanArbitrageFactory.deploy(mockPoolAddressesProvider.target);
    await flashloanArbitrage.waitForDeployment();

    // Fund the MockAavePool with mock tokens
    await mockUSDC.mint(mockAavePool.target, ethers.parseUnits("10000000", 6)); // 10M mUSDC
    await mockWETH.mint(mockAavePool.target, ethers.parseUnits("10000", 18)); // 10k mWETH

    // Fund the MockDEXRouter with mock tokens (liquidity for swaps)
    await mockUSDC.mint(mockDEXRouter.target, ethers.parseUnits("10000000", 6)); // 10M mUSDC
    await mockWETH.mint(mockDEXRouter.target, ethers.parseUnits("10000", 18)); // 10k mWETH

    // Fund the FlashloanArbitrage contract with enough tokens to repay flashloan
    await mockUSDC.mint(flashloanArbitrage.target, ethers.parseUnits("2000", 6)); // 2000 mUSDC
  });

  describe("Test flashloan without swaps", function () {
    it("Should execute a flashloan with empty swap data (no actual swaps)", async function () {
      const flashloanAmount = ethers.parseUnits("1000", 6); // 1000 mUSDC

      // Create arbitrage parameters with empty swap data
      const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'bytes', 'bytes'],
        [
          mockUSDC.target, // tokenA (flashloaned asset)
          mockWETH.target, // tokenB (intermediate token)
          mockDEXRouter.target, // buyRouter
          mockDEXRouter.target, // sellRouter
          "0x", // empty buySwapData
          "0x"  // empty sellSwapData
        ]
      );

      // Record initial balances
      const initialUSDCBalance = await flashloanArbitrage.getBalance(mockUSDC.target);
      console.log("Initial USDC balance:", ethers.formatUnits(initialUSDCBalance, 6));

      // Execute flashloan arbitrage
      const tx = await flashloanArbitrage.executeFlashloanArbitrage(
        mockUSDC.target,
        flashloanAmount,
        arbitrageParams
      );

      await tx.wait();

      // Check final balances
      const finalUSDCBalance = await flashloanArbitrage.getBalance(mockUSDC.target);
      console.log("Final USDC balance:", ethers.formatUnits(finalUSDCBalance, 6));

      // With empty swap data, the contract should still have funds after repaying the flashloan
      // Started with 2000, flashloaned 1000, repaid 1000, should have 2000 left
      expect(finalUSDCBalance).to.equal(ethers.parseUnits("2000", 6));
    });

    it("Should test direct router interaction", async function () {
      // Test if we can call the router directly
      const amount = ethers.parseUnits("100", 6);
      
      // Give owner some tokens first
      await mockUSDC.mint(owner.address, amount);
      
      // Approve router to spend tokens
      await mockUSDC.connect(owner).approve(mockDEXRouter.target, amount);
      
      // Try calling swapExactTokensForTokens directly
      const tx = await mockDEXRouter.swapExactTokensForTokens(
        amount,
        amount,
        [mockUSDC.target, mockWETH.target],
        owner.address,
        Math.floor(Date.now() / 1000) + 300
      );
      
      await tx.wait();
      console.log("Direct router call succeeded");
    });
  });
});
