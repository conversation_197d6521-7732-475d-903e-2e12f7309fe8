const { ethers } = require('ethers');
const chalk = require('chalk');

// Import the actual arbitrage strategy
require('dotenv').config();

async function testImprovedArbitrage() {
  console.log(chalk.blue.bold('🔍 Improved Arbitrage Detection Test'));
  console.log(chalk.gray('═'.repeat(60)));

  // Import the actual classes (we'll simulate them since we can't easily import TypeScript)
  const rpcUrl = process.env.RPC_URL || 'http://localhost:8545';
  const provider = new ethers.JsonRpcProvider(rpcUrl);
  
  console.log(chalk.yellow.bold('\n📋 Testing Pool Manager Functionality'));
  console.log(chalk.gray('─'.repeat(50)));

  // Test the actual pool loading logic
  const MAINNET_ADDRESSES = {
    WETH: '******************************************',
    USDC: '******************************************',
    UNISWAP_V2_FACTORY: '******************************************',
    UNISWAP_V3_FACTORY: '******************************************'
  };

  // Test V2 pool loading
  console.log(chalk.cyan('Testing V2 pool loading...'));
  try {
    const v2Factory = new ethers.Contract(
      MAINNET_ADDRESSES.UNISWAP_V2_FACTORY,
      ['function getPair(address tokenA, address tokenB) view returns (address pair)'],
      provider
    );
    
    const v2PairAddress = await v2Factory.getPair(MAINNET_ADDRESSES.WETH, MAINNET_ADDRESSES.USDC);
    console.log(`V2 Pair Address: ${v2PairAddress}`);
    
    if (v2PairAddress !== ethers.ZeroAddress) {
      const v2Contract = new ethers.Contract(
        v2PairAddress,
        [
          'function getReserves() view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
          'function token0() view returns (address)',
          'function token1() view returns (address)'
        ],
        provider
      );
      
      const [reserves, token0, token1] = await Promise.all([
        v2Contract.getReserves(),
        v2Contract.token0(),
        v2Contract.token1()
      ]);
      
      console.log(`${chalk.green('✓ V2 Pool loaded successfully')}`);
      console.log(`  Token0: ${token0}`);
      console.log(`  Token1: ${token1}`);
      console.log(`  Reserve0: ${ethers.formatEther(reserves[0])}`);
      console.log(`  Reserve1: ${ethers.formatUnits(reserves[1], 6)}`); // USDC has 6 decimals
      
      // Calculate V2 price
      const isWethToken0 = token0.toLowerCase() === MAINNET_ADDRESSES.WETH.toLowerCase();
      const wethReserve = isWethToken0 ? reserves[0] : reserves[1];
      const usdcReserve = isWethToken0 ? reserves[1] : reserves[0];
      const v2Price = Number(ethers.formatUnits(usdcReserve, 6)) / Number(ethers.formatEther(wethReserve));
      
      console.log(`  V2 Price: ${v2Price.toFixed(2)} USDC per WETH`);
    }
  } catch (error) {
    console.log(chalk.red(`❌ V2 pool loading error: ${error.message}`));
  }

  // Test V3 pool loading
  console.log(chalk.cyan('\nTesting V3 pool loading...'));
  try {
    const v3Factory = new ethers.Contract(
      MAINNET_ADDRESSES.UNISWAP_V3_FACTORY,
      ['function getPool(address tokenA, address tokenB, uint24 fee) view returns (address pool)'],
      provider
    );
    
    const v3PoolAddress = await v3Factory.getPool(MAINNET_ADDRESSES.WETH, MAINNET_ADDRESSES.USDC, 3000);
    console.log(`V3 Pool Address: ${v3PoolAddress}`);
    
    if (v3PoolAddress !== ethers.ZeroAddress) {
      const v3Contract = new ethers.Contract(
        v3PoolAddress,
        [
          'function slot0() view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)',
          'function token0() view returns (address)',
          'function token1() view returns (address)',
          'function liquidity() view returns (uint128)'
        ],
        provider
      );
      
      const [slot0, token0, token1, liquidity] = await Promise.all([
        v3Contract.slot0(),
        v3Contract.token0(),
        v3Contract.token1(),
        v3Contract.liquidity()
      ]);
      
      console.log(`${chalk.green('✓ V3 Pool loaded successfully')}`);
      console.log(`  Token0: ${token0}`);
      console.log(`  Token1: ${token1}`);
      console.log(`  Liquidity: ${liquidity}`);
      console.log(`  Tick: ${slot0.tick}`);
      console.log(`  SqrtPriceX96: ${slot0.sqrtPriceX96}`);
      
      // Calculate V3 price using improved method
      const isWethToken0 = token0.toLowerCase() === MAINNET_ADDRESSES.WETH.toLowerCase();
      const tickNumber = Number(slot0.tick);
      let rawPrice = Math.pow(1.0001, tickNumber);
      
      // If WETH is not token0, we need to invert the price
      if (!isWethToken0) {
        rawPrice = 1 / rawPrice;
      }
      
      // Adjust for decimals (WETH has 18 decimals, USDC has 6)
      // Since we want USDC per WETH, we need to adjust: WETH decimals - USDC decimals
      const decimalsAdjustment = Math.pow(10, 18 - 6); // WETH decimals - USDC decimals
      const v3Price = rawPrice * decimalsAdjustment;
      
      console.log(`  V3 Price (tick-based): ${v3Price.toFixed(2)} USDC per WETH`);
      console.log(`  Raw tick price: ${rawPrice}`);
      console.log(`  Decimals adjustment: ${decimalsAdjustment}`);
      console.log(`  Is WETH token0: ${isWethToken0}`);
    }
  } catch (error) {
    console.log(chalk.red(`❌ V3 pool loading error: ${error.message}`));
  }

  // Test arbitrage opportunity detection
  console.log(chalk.yellow.bold('\n🎯 Arbitrage Opportunity Analysis'));
  console.log(chalk.gray('─'.repeat(50)));

  try {
    // Get both pools
    const v2Factory = new ethers.Contract(
      MAINNET_ADDRESSES.UNISWAP_V2_FACTORY,
      ['function getPair(address tokenA, address tokenB) view returns (address pair)'],
      provider
    );
    
    const v3Factory = new ethers.Contract(
      MAINNET_ADDRESSES.UNISWAP_V3_FACTORY,
      ['function getPool(address tokenA, address tokenB, uint24 fee) view returns (address pool)'],
      provider
    );
    
    const [v2PairAddress, v3PoolAddress] = await Promise.all([
      v2Factory.getPair(MAINNET_ADDRESSES.WETH, MAINNET_ADDRESSES.USDC),
      v3Factory.getPool(MAINNET_ADDRESSES.WETH, MAINNET_ADDRESSES.USDC, 3000)
    ]);
    
    if (v2PairAddress !== ethers.ZeroAddress && v3PoolAddress !== ethers.ZeroAddress) {
      // Get V2 price
      const v2Contract = new ethers.Contract(
        v2PairAddress,
        [
          'function getReserves() view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
          'function token0() view returns (address)'
        ],
        provider
      );
      
      const [v2Reserves, v2Token0] = await Promise.all([
        v2Contract.getReserves(),
        v2Contract.token0()
      ]);
      
      const isWethToken0V2 = v2Token0.toLowerCase() === MAINNET_ADDRESSES.WETH.toLowerCase();
      const wethReserveV2 = isWethToken0V2 ? v2Reserves[0] : v2Reserves[1];
      const usdcReserveV2 = isWethToken0V2 ? v2Reserves[1] : v2Reserves[0];
      const v2Price = Number(ethers.formatUnits(usdcReserveV2, 6)) / Number(ethers.formatEther(wethReserveV2));
      
      // Get V3 price
      const v3Contract = new ethers.Contract(
        v3PoolAddress,
        [
          'function slot0() view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)',
          'function token0() view returns (address)'
        ],
        provider
      );
      
      const [v3Slot0, v3Token0] = await Promise.all([
        v3Contract.slot0(),
        v3Contract.token0()
      ]);
      
      const isWethToken0V3 = v3Token0.toLowerCase() === MAINNET_ADDRESSES.WETH.toLowerCase();
      const tickNumber = Number(v3Slot0.tick);
      let rawPrice = Math.pow(1.0001, tickNumber);
      
      if (!isWethToken0V3) {
        rawPrice = 1 / rawPrice;
      }
      
      const decimalsAdjustment = Math.pow(10, 18 - 6); // WETH decimals - USDC decimals
      const v3Price = rawPrice * decimalsAdjustment;
      
      // Calculate arbitrage opportunity
      const priceDifference = Math.abs(v2Price - v3Price);
      const priceDifferencePercent = (priceDifference / Math.min(v2Price, v3Price)) * 100;
      
      console.log(`${chalk.cyan('V2 Price:')} ${v2Price.toFixed(2)} USDC per WETH`);
      console.log(`${chalk.cyan('V3 Price:')} ${v3Price.toFixed(2)} USDC per WETH`);
      console.log(`${chalk.cyan('Price Difference:')} ${priceDifference.toFixed(2)} USDC (${priceDifferencePercent.toFixed(4)}%)`);
      
      const minProfitThreshold = 0.001; // 0.1%
      if (priceDifferencePercent > minProfitThreshold) {
        console.log(`${chalk.green('🎯 Arbitrage opportunity detected!')} (>${minProfitThreshold}% threshold)`);
        
        const buyFrom = v2Price < v3Price ? 'Uniswap V2' : 'Uniswap V3';
        const sellTo = v2Price < v3Price ? 'Uniswap V3' : 'Uniswap V2';
        
        console.log(`${chalk.cyan('Strategy:')} Buy from ${buyFrom}, sell to ${sellTo}`);
        
        // Estimate potential profit for 1 WETH
        const testAmount = 1; // 1 WETH
        const buyPrice = Math.min(v2Price, v3Price);
        const sellPrice = Math.max(v2Price, v3Price);
        const grossProfit = (sellPrice - buyPrice) * testAmount;
        
        console.log(`${chalk.cyan('Estimated profit for 1 WETH:')} ${grossProfit.toFixed(2)} USDC`);
        console.log(`${chalk.cyan('Estimated profit in ETH:')} ${(grossProfit / v2Price).toFixed(6)} ETH`);
        
      } else {
        console.log(`${chalk.yellow('⚠️  Price difference too small')} (<${minProfitThreshold}%)`);
      }
      
    } else {
      console.log(chalk.red('❌ Could not find both V2 and V3 pools'));
    }
    
  } catch (error) {
    console.log(chalk.red(`❌ Arbitrage analysis error: ${error.message}`));
  }

  console.log(chalk.green.bold('\n✅ Test Complete'));
  console.log(chalk.yellow('\n💡 If the MEV bot still shows 0 opportunities:'));
  console.log('• Check that the improved price calculation is being used');
  console.log('• Verify the minimum profit threshold is reasonable');
  console.log('• Ensure gas cost calculations are not too high');
  console.log('• Check that the PoolManager is loading pools correctly');
}

// Run the test
testImprovedArbitrage().catch(console.error);
