const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("Debug FlashloanArbitrage", function () {
  let flashloanArbitrage;
  let owner;
  let mockPoolAddressesProvider;
  let mockAavePool;
  let mockUSDC;
  let mockWETH;
  let mockDEXRouter;

  beforeEach(async function () {
    [owner] = await ethers.getSigners();

    // Deploy MockAavePool
    const MockAavePoolFactory = await ethers.getContractFactory("MockAavePool");
    mockAavePool = await MockAavePoolFactory.deploy();
    await mockAavePool.waitForDeployment();

    // Deploy MockPoolAddressesProvider
    const MockPoolAddressesProviderFactory = await ethers.getContractFactory("MockPoolAddressesProvider");
    mockPoolAddressesProvider = await MockPoolAddressesProviderFactory.deploy(mockAavePool.target);
    await mockPoolAddressesProvider.waitForDeployment();

    // Deploy MockERC20 tokens
    const MockERC20Factory = await ethers.getContractFactory("MockERC20");
    mockUSDC = await MockERC20Factory.deploy("Mock USDC", "mUSDC");
    await mockUSDC.waitForDeployment();
    mockWETH = await MockERC20Factory.deploy("Mock WETH", "mWETH");
    await mockWETH.waitForDeployment();

    // Deploy MockDEXRouter
    const MockDEXRouterFactory = await ethers.getContractFactory("MockDEXRouter");
    mockDEXRouter = await MockDEXRouterFactory.deploy();
    await mockDEXRouter.waitForDeployment();

    // Deploy FlashloanArbitrage contract
    const FlashloanArbitrageFactory = await ethers.getContractFactory("FlashloanArbitrage");
    flashloanArbitrage = await FlashloanArbitrageFactory.deploy(mockPoolAddressesProvider.target);
    await flashloanArbitrage.waitForDeployment();

    // Fund the MockAavePool with mock tokens
    await mockUSDC.mint(mockAavePool.target, ethers.parseUnits("10000000", 6)); // 10M mUSDC
    await mockWETH.mint(mockAavePool.target, ethers.parseUnits("10000", 18)); // 10k mWETH

    // Fund the MockDEXRouter with mock tokens (liquidity for swaps)
    await mockUSDC.mint(mockDEXRouter.target, ethers.parseUnits("10000000", 6)); // 10M mUSDC
    await mockWETH.mint(mockDEXRouter.target, ethers.parseUnits("10000", 18)); // 10k mWETH

    // Fund the FlashloanArbitrage contract with some initial tokens
    await mockUSDC.mint(flashloanArbitrage.target, ethers.parseUnits("100", 6)); // 100 mUSDC
  });

  describe("Step by step debugging", function () {
    it("Should call flashloan directly from MockAavePool", async function () {
      const flashloanAmount = ethers.parseUnits("1000", 6); // 1000 mUSDC

      // Create simple arbitrage parameters
      const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'bytes', 'bytes'],
        [
          mockUSDC.target, // tokenA (flashloaned asset)
          mockWETH.target, // tokenB (intermediate token)
          mockDEXRouter.target, // buyRouter
          mockDEXRouter.target, // sellRouter
          "0x", // empty buySwapData for now
          "0x"  // empty sellSwapData for now
        ]
      );

      console.log("Contract addresses:");
      console.log("FlashloanArbitrage:", flashloanArbitrage.target);
      console.log("MockAavePool:", mockAavePool.target);
      console.log("MockUSDC:", mockUSDC.target);
      console.log("MockWETH:", mockWETH.target);
      console.log("MockDEXRouter:", mockDEXRouter.target);

      // Check initial balances
      const initialContractUSDC = await mockUSDC.balanceOf(flashloanArbitrage.target);
      const initialPoolUSDC = await mockUSDC.balanceOf(mockAavePool.target);
      
      console.log("Initial contract USDC balance:", ethers.formatUnits(initialContractUSDC, 6));
      console.log("Initial pool USDC balance:", ethers.formatUnits(initialPoolUSDC, 6));

      // Try calling flashloan directly from the pool (simulating what would happen)
      try {
        const tx = await mockAavePool.flashLoanSimple(
          flashloanArbitrage.target, // receiverAddress
          mockUSDC.target, // asset
          flashloanAmount,
          arbitrageParams,
          0 // referralCode
        );
        
        console.log("Direct flashloan call succeeded");
        await tx.wait();
        
        // Check final balances
        const finalContractUSDC = await mockUSDC.balanceOf(flashloanArbitrage.target);
        console.log("Final contract USDC balance:", ethers.formatUnits(finalContractUSDC, 6));
        
      } catch (error) {
        console.log("Direct flashloan call failed:", error.message);
        throw error;
      }
    });

    it("Should test executeFlashloanArbitrage function", async function () {
      const flashloanAmount = ethers.parseUnits("1000", 6); // 1000 mUSDC

      // Create simple arbitrage parameters
      const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'bytes', 'bytes'],
        [
          mockUSDC.target, // tokenA (flashloaned asset)
          mockWETH.target, // tokenB (intermediate token)
          mockDEXRouter.target, // buyRouter
          mockDEXRouter.target, // sellRouter
          "0x", // empty buySwapData for now
          "0x"  // empty sellSwapData for now
        ]
      );

      console.log("Testing executeFlashloanArbitrage...");

      try {
        const tx = await flashloanArbitrage.executeFlashloanArbitrage(
          mockUSDC.target,
          flashloanAmount,
          arbitrageParams
        );
        
        console.log("executeFlashloanArbitrage call succeeded");
        await tx.wait();
        
      } catch (error) {
        console.log("executeFlashloanArbitrage failed:", error.message);
        console.log("Error reason:", error.reason);
        console.log("Error code:", error.code);
        throw error;
      }
    });
  });
});
