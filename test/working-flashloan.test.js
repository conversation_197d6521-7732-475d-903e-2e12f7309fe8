const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("WorkingFlashloanArbitrage Test", function () {
  let workingFlashloanArbitrage;
  let owner;
  let mockPoolAddressesProvider;
  let mockAavePool;
  let mockUSDC;
  let mockWETH;
  let mockDEXRouter;

  beforeEach(async function () {
    [owner] = await ethers.getSigners();

    // Deploy MockAavePool
    const MockAavePoolFactory = await ethers.getContractFactory("MockAavePool");
    mockAavePool = await MockAavePoolFactory.deploy();
    await mockAavePool.waitForDeployment();

    // Deploy MockPoolAddressesProvider
    const MockPoolAddressesProviderFactory = await ethers.getContractFactory("MockPoolAddressesProvider");
    mockPoolAddressesProvider = await MockPoolAddressesProviderFactory.deploy(mockAavePool.target);
    await mockPoolAddressesProvider.waitForDeployment();

    // Deploy MockERC20 tokens
    const MockERC20Factory = await ethers.getContractFactory("MockERC20");
    mockUSDC = await MockERC20Factory.deploy("Mock USDC", "mUSDC");
    await mockUSDC.waitForDeployment();
    mockWETH = await MockERC20Factory.deploy("Mock WETH", "mWETH");
    await mockWETH.waitForDeployment();

    // Deploy MockDEXRouter
    const MockDEXRouterFactory = await ethers.getContractFactory("MockDEXRouter");
    mockDEXRouter = await MockDEXRouterFactory.deploy();
    await mockDEXRouter.waitForDeployment();

    // Deploy WorkingFlashloanArbitrage contract
    const WorkingFlashloanArbitrageFactory = await ethers.getContractFactory("WorkingFlashloanArbitrage");
    workingFlashloanArbitrage = await WorkingFlashloanArbitrageFactory.deploy(mockPoolAddressesProvider.target);
    await workingFlashloanArbitrage.waitForDeployment();

    // Fund the MockAavePool with mock tokens
    await mockUSDC.mint(mockAavePool.target, ethers.parseUnits("10000000", 6)); // 10M mUSDC
    await mockWETH.mint(mockAavePool.target, ethers.parseUnits("10000", 18)); // 10k mWETH

    // Fund the MockDEXRouter with mock tokens (liquidity for swaps)
    await mockUSDC.mint(mockDEXRouter.target, ethers.parseUnits("10000000", 6)); // 10M mUSDC
    await mockWETH.mint(mockDEXRouter.target, ethers.parseUnits("10000", 18)); // 10k mWETH

    // Fund the WorkingFlashloanArbitrage contract with enough tokens to repay flashloan
    await mockUSDC.mint(workingFlashloanArbitrage.target, ethers.parseUnits("2000", 6)); // 2000 mUSDC
  });

  describe("Test flashloan with empty swap data", function () {
    it("Should execute a flashloan with empty swap data successfully", async function () {
      const flashloanAmount = ethers.parseUnits("1000", 6); // 1000 mUSDC

      // Record initial balances
      const initialUSDCBalance = await workingFlashloanArbitrage.getBalance(mockUSDC.target);
      console.log("Initial USDC balance:", ethers.formatUnits(initialUSDCBalance, 6));

      // Execute flashloan arbitrage with empty swap data
      const tx = await workingFlashloanArbitrage.executeFlashloanArbitrage(
        mockUSDC.target,     // asset
        flashloanAmount,     // amount
        mockWETH.target,     // tokenB
        mockDEXRouter.target, // buyRouter
        mockDEXRouter.target, // sellRouter
        "0x",                // empty buySwapData
        "0x"                 // empty sellSwapData
      );

      await tx.wait();

      // Check final balances
      const finalUSDCBalance = await workingFlashloanArbitrage.getBalance(mockUSDC.target);
      console.log("Final USDC balance:", ethers.formatUnits(finalUSDCBalance, 6));

      // With empty swap data, the contract should still have funds after repaying the flashloan
      // Started with 2000, flashloaned 1000, repaid 1000, should have 2000 left
      expect(finalUSDCBalance).to.equal(ethers.parseUnits("2000", 6));
    });

    it("Should execute a flashloan with real swap data", async function () {
      const flashloanAmount = ethers.parseUnits("1000", 6); // 1000 mUSDC

      // Create swap data for MockDEXRouter.swapExactTokensForTokens
      const buySwapData = mockDEXRouter.interface.encodeFunctionData("swapExactTokensForTokens", [
        flashloanAmount,                           // amountIn
        flashloanAmount,                           // amountOutMin (1:1 for simplicity)
        [mockUSDC.target, mockWETH.target],       // path
        workingFlashloanArbitrage.target,         // to
        Math.floor(Date.now() / 1000) + 300       // deadline
      ]);

      // For sell swap, we'll use a placeholder amount that will be updated
      const sellSwapData = mockDEXRouter.interface.encodeFunctionData("swapExactTokensForTokens", [
        flashloanAmount,                           // amountIn (will be updated)
        flashloanAmount,                           // amountOutMin (1:1 for simplicity)
        [mockWETH.target, mockUSDC.target],       // path
        workingFlashloanArbitrage.target,         // to
        Math.floor(Date.now() / 1000) + 300       // deadline
      ]);

      // Record initial balances
      const initialUSDCBalance = await workingFlashloanArbitrage.getBalance(mockUSDC.target);
      console.log("Initial USDC balance:", ethers.formatUnits(initialUSDCBalance, 6));

      // Execute flashloan arbitrage with real swap data
      const tx = await workingFlashloanArbitrage.executeFlashloanArbitrage(
        mockUSDC.target,     // asset
        flashloanAmount,     // amount
        mockWETH.target,     // tokenB
        mockDEXRouter.target, // buyRouter
        mockDEXRouter.target, // sellRouter
        buySwapData,         // buySwapData
        sellSwapData         // sellSwapData
      );

      await tx.wait();

      // Check final balances
      const finalUSDCBalance = await workingFlashloanArbitrage.getBalance(mockUSDC.target);
      console.log("Final USDC balance:", ethers.formatUnits(finalUSDCBalance, 6));

      // With 1:1 swaps, we should have the same balance after repaying the flashloan
      expect(finalUSDCBalance).to.equal(ethers.parseUnits("2000", 6));
    });

    it("Should handle basic contract functions", async function () {
      // Test basic functionality
      expect(await workingFlashloanArbitrage.owner()).to.equal(owner.address);
      expect(await workingFlashloanArbitrage.CHAIN_ID()).to.equal(31337);
      
      const usdcBalance = await workingFlashloanArbitrage.getBalance(mockUSDC.target);
      expect(usdcBalance).to.equal(ethers.parseUnits("2000", 6));
    });
  });
});
