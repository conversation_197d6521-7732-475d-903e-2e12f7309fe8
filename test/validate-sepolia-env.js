#!/usr/bin/env node

/**
 * Sepolia .env Configuration Validator
 * Ensures all required properties are set correctly for Sepolia testnet
 */

const fs = require('fs');
const path = require('path');
const chalk = require('chalk');

console.log(chalk.blue.bold('\n🔍 Sepolia .env Configuration Validator\n'));

// Expected configuration for Sepolia
const SEPOLIA_CONFIG = {
  // Network Configuration
  CHAIN_ID: '11155111',
  RPC_URL: 'should contain sepolia',
  FLASHBOTS_RPC_URL: 'should contain sepolia',
  
  // Required Keys
  PRIVATE_KEY: 'should not be default',
  FLASHBOTS_SIGNER_KEY: 'should not be default',
  
  // MEV Configuration
  MIN_PROFIT_WEI: 'should be set',
  MAX_GAS_PRICE_GWEI: 'should be set',
  MAX_PRIORITY_FEE_GWEI: 'should be set',
  SLIPPAGE_TOLERANCE: 'should be set',
  
  // Mempool Configuration
  MEMPOOL_WEBSOCKET_URL: 'should be set',
  ENABLE_FLASHBOTS_MEMPOOL: 'should be boolean',
  ENABLE_ETHERS_MEMPOOL: 'should be boolean',
  
  // Strategy Configuration
  ENABLE_SANDWICH_ATTACKS: 'should be boolean',
  ENABLE_FRONT_RUNNING: 'should be boolean',
  ENABLE_ARBITRAGE: 'should be boolean',
  ENABLE_FLASHLOAN_ATTACKS: 'should be boolean',
  ENABLE_MULTI_BLOCK_ATTACKS: 'should be boolean',
  
  // Risk Management
  MAX_BLOCKS_AHEAD: 'should be set',
  MAX_POSITION_SIZE_ETH: 'should be set',
  EMERGENCY_STOP: 'should be boolean',
  DRY_RUN: 'should be boolean',
  
  // Logging
  LOG_LEVEL: 'should be set',
  LOG_TO_FILE: 'should be boolean',
  
  // Flashbots
  ENABLE_FLASHBOTS: 'should be boolean',
  FLASHBOTS_RELAY_URL: 'should be set',
  FLASHBOTS_AUTH_KEY: 'can be empty',
  
  // Gas Estimation
  BLOCKNATIVE_API_KEY: 'can be empty',
  ENABLE_BLOCKNATIVE_GAS: 'should be boolean',
  ENABLE_0X_API_GAS: 'should be boolean',
  ENABLE_ETH_GAS_STATION: 'should be boolean',
  FALLBACK_GAS_PRICE_GWEI: 'should be set',
  
  // Flashloan Contracts
  HYBRID_FLASHLOAN_CONTRACT: 'can be empty',
  BALANCER_FLASHLOAN_CONTRACT: 'can be empty',
  AAVE_FLASHLOAN_CONTRACT: 'can be empty',
  
  // Flashloan DEX Configuration
  FLASHLOAN_DEX_PAIRS: 'should be set',
  FLASHLOAN_BUY_DEX: 'should be set',
  FLASHLOAN_SELL_DEX: 'should be set',
  ENABLE_CROSS_DEX_ARBITRAGE: 'should be boolean',
  MIN_ARBITRAGE_SPREAD: 'should be set',
  
  // Flashloan Tokens
  FLASHLOAN_TOKENS: 'should be set',
  FLASHLOAN_PRIMARY_TOKEN: 'should be set',
  FLASHLOAN_TARGET_TOKENS: 'should be set',
  ENABLE_ALL_TOKEN_PAIRS: 'should be boolean',
  MIN_TOKEN_LIQUIDITY_USD: 'should be set',
  
  // Scanning Intervals
  ARBITRAGE_SCAN_INTERVAL_MS: 'should be set',
  FLASHLOAN_SCAN_INTERVAL_MS: 'should be set',
  
  // MEV-Share (should be disabled for Sepolia)
  ENABLE_MEV_SHARE: 'should be false for Sepolia',
  MEV_SHARE_STREAM_URL: 'can be set',
  ENABLE_BACKRUN_STRATEGY: 'should be false for Sepolia',
  MIN_BACKRUN_PROFIT_ETH: 'should be set',
  MAX_GAS_COST_ETH: 'should be set'
};

function validateEnvFile() {
  const envPath = path.join(__dirname, '.env');
  
  if (!fs.existsSync(envPath)) {
    console.log(chalk.red('❌ .env file not found'));
    console.log(chalk.yellow('   Run: cp .env.example .env'));
    return false;
  }

  console.log(chalk.green('✅ .env file found'));
  
  // Read and parse .env file
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envVars = {};
  
  envContent.split('\n').forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#')) {
      const [key, ...valueParts] = line.split('=');
      if (key && valueParts.length > 0) {
        envVars[key.trim()] = valueParts.join('=').trim();
      }
    }
  });

  console.log(chalk.blue('\n🔍 Validating Configuration Properties:\n'));

  let allValid = true;
  let criticalIssues = 0;
  let warnings = 0;

  // Validate each expected property
  Object.entries(SEPOLIA_CONFIG).forEach(([key, requirement]) => {
    const value = envVars[key];
    const hasValue = value !== undefined && value !== '';
    
    let status = '✅';
    let message = '';
    let level = 'info';

    if (!hasValue) {
      if (requirement.includes('can be empty')) {
        status = '⚠️ ';
        message = 'Optional (empty)';
        level = 'warning';
        warnings++;
      } else {
        status = '❌';
        message = 'Missing or empty';
        level = 'error';
        criticalIssues++;
        allValid = false;
      }
    } else {
      // Validate specific requirements
      switch (key) {
        case 'CHAIN_ID':
          if (value !== '11155111') {
            status = '❌';
            message = `Should be 11155111 for Sepolia, got: ${value}`;
            level = 'error';
            criticalIssues++;
            allValid = false;
          } else {
            message = 'Sepolia (correct)';
          }
          break;
          
        case 'RPC_URL':
        case 'FLASHBOTS_RPC_URL':
        case 'MEMPOOL_WEBSOCKET_URL':
          if (!value.toLowerCase().includes('sepolia')) {
            status = '⚠️ ';
            message = 'Should contain "sepolia" for testnet';
            level = 'warning';
            warnings++;
          } else {
            message = 'Sepolia endpoint (correct)';
          }
          break;
          
        case 'PRIVATE_KEY':
        case 'FLASHBOTS_SIGNER_KEY':
          if (value.startsWith('0x000000') || value.length !== 66) {
            status = '❌';
            message = 'Invalid or default key';
            level = 'error';
            criticalIssues++;
            allValid = false;
          } else {
            message = 'Valid key format';
          }
          break;
          
        case 'ENABLE_MEV_SHARE':
        case 'ENABLE_BACKRUN_STRATEGY':
          if (value.toLowerCase() === 'true') {
            status = '⚠️ ';
            message = 'Should be false for Sepolia (MEV-Share is mainnet only)';
            level = 'warning';
            warnings++;
          } else {
            message = 'Disabled (correct for Sepolia)';
          }
          break;
          
        case 'DRY_RUN':
          const dryRunValue = value.split('#')[0].trim().toLowerCase();
          if (dryRunValue !== 'true') {
            status = '⚠️ ';
            message = 'Recommend true for testing';
            level = 'warning';
            warnings++;
          } else {
            message = 'Enabled (safe for testing)';
          }
          break;
          
        default:
          message = `Set to: ${value}`;
      }
    }

    // Display result
    const color = level === 'error' ? chalk.red : 
                  level === 'warning' ? chalk.yellow : chalk.green;
    
    console.log(`${status} ${key.padEnd(30)} ${color(message)}`);
  });

  // Check for extra properties that might be missing
  const expectedKeys = Object.keys(SEPOLIA_CONFIG);
  const actualKeys = Object.keys(envVars);
  const missingKeys = expectedKeys.filter(key => !actualKeys.includes(key));
  
  if (missingKeys.length > 0) {
    console.log(chalk.red('\n❌ Missing Properties:'));
    missingKeys.forEach(key => {
      console.log(chalk.red(`   - ${key}`));
    });
    criticalIssues += missingKeys.length;
    allValid = false;
  }

  // Summary
  console.log(chalk.blue('\n📊 Validation Summary:'));
  
  if (allValid && warnings === 0) {
    console.log(chalk.green('✅ All properties correctly configured for Sepolia!'));
  } else {
    if (criticalIssues > 0) {
      console.log(chalk.red(`❌ ${criticalIssues} critical issue(s) found`));
    }
    if (warnings > 0) {
      console.log(chalk.yellow(`⚠️  ${warnings} warning(s) found`));
    }
  }

  // Recommendations
  console.log(chalk.blue('\n🎯 Sepolia-Specific Recommendations:'));
  console.log('1. Set CHAIN_ID=11155111 (Sepolia)');
  console.log('2. Use Sepolia RPC endpoints');
  console.log('3. Set ENABLE_MEV_SHARE=false (mainnet only)');
  console.log('4. Set DRY_RUN=true for safe testing');
  console.log('5. Lower profit thresholds for testnet');
  console.log('6. Get test ETH from Sepolia faucets');

  return allValid;
}

// Run validation
const isValid = validateEnvFile();

if (isValid) {
  console.log(chalk.green.bold('\n🚀 Configuration ready for Sepolia testing!\n'));
} else {
  console.log(chalk.red.bold('\n🔧 Please fix the issues above before running the bot.\n'));
}

process.exit(isValid ? 0 : 1);
