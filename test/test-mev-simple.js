#!/usr/bin/env node

/**
 * Simple MEV Strategy Test
 * Basic testing for MEV strategies with minimal setup
 */

const { ethers } = require("hardhat");
const chalk = require('chalk');

console.log(chalk.blue.bold('\n🎯 Simple MEV Strategy Test\n'));

// Test configuration
const testConfig = {
  chainId: 31337, // Hardhat
  dryRun: true,
  minProfitEth: 0.001,
  strategies: ['sandwich', 'frontrunning', 'arbitrage']
};

let testResults = {
  sandwich: { opportunities: 0, executed: 0, profit: 0 },
  frontrunning: { opportunities: 0, executed: 0, profit: 0 },
  arbitrage: { opportunities: 0, executed: 0, profit: 0 }
};

async function main() {
  try {
    console.log(chalk.cyan('📋 Simple MEV Test Configuration:'));
    console.log(`   Chain ID: ${testConfig.chainId}`);
    console.log(`   Dry Run: ${testConfig.dryRun}`);
    console.log(`   Min Profit: ${testConfig.minProfitEth} ETH`);
    console.log(`   Strategies: ${testConfig.strategies.join(', ')}`);

    // Step 1: Setup test environment
    await setupSimpleTestEnvironment();

    // Step 2: Initialize strategies
    const strategies = await initializeStrategies();

    // Step 3: Test each strategy
    await testStrategies(strategies);

    // Step 4: Generate report
    generateSimpleReport();

    console.log(chalk.green.bold('\n🎉 Simple MEV Test Completed Successfully!'));

  } catch (error) {
    console.error(chalk.red('❌ Simple MEV test failed:'), error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

async function setupSimpleTestEnvironment() {
  console.log(chalk.yellow('\n🔧 Setting up simple test environment...'));

  try {
    const signers = await ethers.getSigners();
    const deployer = signers[0];
    const victim = signers[1];
    const mevBot = signers[2];

    console.log(`   ✅ Deployer: ${deployer.address}`);
    console.log(`   ✅ Victim: ${victim.address}`);
    console.log(`   ✅ MEV Bot: ${mevBot.address}`);

    // Check balances
    const deployerBalance = await ethers.provider.getBalance(deployer.address);
    const victimBalance = await ethers.provider.getBalance(victim.address);
    const mevBotBalance = await ethers.provider.getBalance(mevBot.address);

    console.log(`   📊 Deployer: ${ethers.formatEther(deployerBalance)} ETH`);
    console.log(`   📊 Victim: ${ethers.formatEther(victimBalance)} ETH`);
    console.log(`   📊 MEV Bot: ${ethers.formatEther(mevBotBalance)} ETH`);

    // Configure environment
    process.env.CHAIN_ID = '31337';
    process.env.DRY_RUN = 'true';
    process.env.MIN_PROFIT_ETH = testConfig.minProfitEth.toString();

    console.log('   ✅ Environment configured');

    return { deployer, victim, mevBot };

  } catch (error) {
    throw new Error(`Environment setup failed: ${error.message}`);
  }
}

async function initializeStrategies() {
  console.log(chalk.yellow('\n🤖 Initializing MEV strategies...'));

  const strategies = {};

  try {
    const provider = ethers.provider;
    const [, , mevBot] = await ethers.getSigners();

    // Initialize Sandwich Strategy
    if (testConfig.strategies.includes('sandwich')) {
      console.log('   🥪 Initializing Sandwich Strategy...');
      const { SandwichStrategy } = require('../dist/strategies/sandwich');
      strategies.sandwich = new SandwichStrategy(provider, mevBot);
      console.log('   ✅ Sandwich Strategy initialized');
    }

    // Initialize Frontrunning Strategy
    if (testConfig.strategies.includes('frontrunning')) {
      console.log('   🏃 Initializing Frontrunning Strategy...');
      const { FrontrunningStrategy } = require('../dist/strategies/frontrunning');
      strategies.frontrunning = new FrontrunningStrategy(provider, mevBot);
      console.log('   ✅ Frontrunning Strategy initialized');
    }

    // Initialize Arbitrage Strategy
    if (testConfig.strategies.includes('arbitrage')) {
      console.log('   🔄 Initializing Arbitrage Strategy...');
      try {
        const { ArbitrageStrategy } = require('../dist/strategies/arbitrage');
        strategies.arbitrage = new ArbitrageStrategy(provider, mevBot);
        console.log('   ✅ Arbitrage Strategy initialized');
      } catch (error) {
        console.log('   ⚠️  Arbitrage Strategy not available, skipping');
      }
    }

    return strategies;

  } catch (error) {
    throw new Error(`Strategy initialization failed: ${error.message}`);
  }
}

async function testStrategies(strategies) {
  console.log(chalk.yellow('\n🧪 Testing MEV strategies...'));

  // Test Sandwich Strategy
  if (strategies.sandwich) {
    await testSandwichStrategy(strategies.sandwich);
  }

  // Test Frontrunning Strategy
  if (strategies.frontrunning) {
    await testFrontrunningStrategy(strategies.frontrunning);
  }

  // Test Arbitrage Strategy
  if (strategies.arbitrage) {
    await testArbitrageStrategy(strategies.arbitrage);
  }

  console.log('   ✅ All strategy tests completed');
}

async function testSandwichStrategy(strategy) {
  console.log(chalk.cyan('\n   🥪 Testing Sandwich Strategy...'));

  try {
    // Create a mock victim transaction
    const [, victim] = await ethers.getSigners();
    const victimTx = {
      hash: '0x1111111111111111111111111111111111111111111111111111111111111111',
      from: victim.address,
      to: "******************************************", // Uniswap V2 Router
      value: ethers.parseEther("0.1"), // Small amount
      data: "0x7ff36ab5", // swapExactETHForTokens
      gasPrice: ethers.parseUnits("30", "gwei"),
      gasLimit: BigInt(200000),
      nonce: 1
    };

    console.log(`      🎯 Testing with ${ethers.formatEther(victimTx.value)} ETH swap`);

    // Analyze sandwich opportunity
    const opportunity = await strategy.analyzeTransaction(victimTx);

    if (opportunity) {
      testResults.sandwich.opportunities++;
      const profitEth = Number(ethers.formatEther(opportunity.estimatedProfit));

      console.log(`      ✅ Sandwich opportunity found!`);
      console.log(`         Estimated Profit: ${profitEth.toFixed(6)} ETH`);
      console.log(`         Confidence: ${opportunity.confidence}%`);

      // Test execution
      const success = await strategy.executeSandwich(opportunity);
      if (success) {
        testResults.sandwich.executed++;
        testResults.sandwich.profit += profitEth;
        console.log(`      ✅ Sandwich execution successful!`);
      } else {
        console.log(`      ❌ Sandwich execution failed`);
      }
    } else {
      console.log(`      ❌ No profitable sandwich opportunity`);
    }

  } catch (error) {
    console.log(`      ❌ Sandwich test error: ${error.message}`);
  }
}

async function testFrontrunningStrategy(strategy) {
  console.log(chalk.cyan('\n   🏃 Testing Frontrunning Strategy...'));

  try {
    // Create a mock high-value transaction
    const [, victim, target] = await ethers.getSigners();
    const victimTx = {
      hash: '0x2222222222222222222222222222222222222222222222222222222222222222',
      from: victim.address,
      to: target.address,
      value: ethers.parseEther("0.5"), // Medium amount
      data: "0x",
      gasPrice: ethers.parseUnits("40", "gwei"),
      gasLimit: BigInt(21000),
      nonce: 2
    };

    console.log(`      🎯 Testing with ${ethers.formatEther(victimTx.value)} ETH transfer`);

    // Analyze frontrunning opportunity
    const opportunity = await strategy.analyzeFrontrunOpportunity(victimTx);

    if (opportunity) {
      testResults.frontrunning.opportunities++;
      const profitEth = Number(ethers.formatEther(opportunity.estimatedProfit));

      console.log(`      ✅ Frontrunning opportunity found!`);
      console.log(`         Estimated Profit: ${profitEth.toFixed(6)} ETH`);
      console.log(`         Confidence: ${opportunity.confidence}%`);

      // Test execution
      const success = await strategy.executeFrontrun(opportunity);
      if (success) {
        testResults.frontrunning.executed++;
        testResults.frontrunning.profit += profitEth;
        console.log(`      ✅ Frontrunning execution successful!`);
      } else {
        console.log(`      ❌ Frontrunning execution failed`);
      }
    } else {
      console.log(`      ❌ No profitable frontrunning opportunity`);
    }

  } catch (error) {
    console.log(`      ❌ Frontrunning test error: ${error.message}`);
  }
}

async function testArbitrageStrategy(strategy) {
  console.log(chalk.cyan('\n   🔄 Testing Arbitrage Strategy...'));

  try {
    // Test arbitrage opportunity scanning
    const opportunities = await strategy.scanForArbitrageOpportunities();
    testResults.arbitrage.opportunities = opportunities.length;

    console.log(`      Found ${opportunities.length} arbitrage opportunities`);

    if (opportunities.length > 0) {
      const opportunity = opportunities[0];
      const profitEth = Number(ethers.formatEther(opportunity.expectedProfit));

      console.log(`      ✅ Arbitrage opportunity found!`);
      console.log(`         Expected Profit: ${profitEth.toFixed(6)} ETH`);
      console.log(`         Confidence: ${opportunity.confidence}%`);

      // Test execution
      const success = await strategy.executeArbitrage(opportunity);
      if (success) {
        testResults.arbitrage.executed++;
        testResults.arbitrage.profit += profitEth;
        console.log(`      ✅ Arbitrage execution successful!`);
      } else {
        console.log(`      ❌ Arbitrage execution failed`);
      }
    }

  } catch (error) {
    console.log(`      ❌ Arbitrage test error: ${error.message}`);
  }
}

function generateSimpleReport() {
  console.log(chalk.green.bold('\n📊 Simple MEV Test Results'));
  console.log('═'.repeat(50));

  const totalOpportunities = Object.values(testResults).reduce((sum, result) => sum + result.opportunities, 0);
  const totalExecuted = Object.values(testResults).reduce((sum, result) => sum + result.executed, 0);
  const totalProfit = Object.values(testResults).reduce((sum, result) => sum + result.profit, 0);

  console.log(chalk.cyan('Strategy Performance:'));
  Object.entries(testResults).forEach(([strategy, results]) => {
    const successRate = results.opportunities > 0 ? (results.executed / results.opportunities * 100).toFixed(1) : '0.0';
    console.log(`  ${strategy.padEnd(12)}: ${results.opportunities} opps | ${results.executed} exec | ${successRate}% success | ${results.profit.toFixed(4)} ETH profit`);
  });

  console.log('\n' + chalk.cyan('Overall Summary:'));
  console.log(`  Total Opportunities: ${totalOpportunities}`);
  console.log(`  Total Executed: ${totalExecuted}`);
  console.log(`  Overall Success Rate: ${totalOpportunities > 0 ? (totalExecuted / totalOpportunities * 100).toFixed(1) : '0.0'}%`);
  console.log(`  Total Profit: ${totalProfit.toFixed(6)} ETH`);

  console.log('\n' + chalk.cyan('Assessment:'));
  if (totalOpportunities === 0) {
    console.log('  ⚠️  No opportunities detected - check strategy configuration');
  } else if (totalExecuted === 0) {
    console.log('  ⚠️  No executions successful - check execution logic');
  } else {
    console.log('  ✅ MEV strategies are functioning correctly');
    console.log(`  💰 Estimated profit: ${totalProfit.toFixed(6)} ETH`);
  }
}

// Handle errors and cleanup
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(chalk.red('❌ Simple MEV test failed:'), error);
    process.exit(1);
  });
