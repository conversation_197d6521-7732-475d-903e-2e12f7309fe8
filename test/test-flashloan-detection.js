#!/usr/bin/env node

const { ethers } = require('ethers');
require('dotenv').config();

// Import the flashloan strategy
const { FlashloanStrategy } = require('./dist/strategies/flashloan.js');

async function main() {
  console.log('🧪 Testing Flashloan Opportunity Detection\n');
  
  try {
    // Initialize provider
    const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);
    
    // Test connection
    const blockNumber = await provider.getBlockNumber();
    console.log(`✅ Connected to Sepolia (Block: ${blockNumber})`);
    
    // Initialize flashloan strategy
    console.log('\n🔄 Initializing flashloan strategy...');
    const flashloanStrategy = new FlashloanStrategy(provider);
    
    // Scan for opportunities
    console.log('\n🔍 Scanning for flashloan opportunities...');
    const opportunities = await flashloanStrategy.scanForFlashloanOpportunities();
    
    console.log(`\n📊 Results:`);
    console.log(`Found ${opportunities.length} flashloan opportunities`);
    
    if (opportunities.length > 0) {
      console.log('\n✅ Opportunities detected:');
      opportunities.forEach((opp, index) => {
        console.log(`\n${index + 1}. Opportunity:`);
        console.log(`   Token: ${opp.flashloanToken.symbol}`);
        console.log(`   Amount: ${ethers.formatUnits(opp.flashloanAmount, opp.flashloanToken.decimals)} ${opp.flashloanToken.symbol}`);
        console.log(`   Expected Profit: ${ethers.formatEther(opp.expectedProfit)} ETH`);
        console.log(`   Confidence: ${opp.confidence}%`);
        console.log(`   Gas Estimate: ${ethers.formatEther(opp.gasEstimate)} ETH`);
      });
    } else {
      console.log('\n❌ No opportunities found');
      console.log('\nPossible reasons:');
      console.log('1. Price differences between DEXs are too small');
      console.log('2. Gas costs exceed potential profits');
      console.log('3. Liquidity is insufficient');
      console.log('4. Confidence thresholds are still too high');
    }
    
  } catch (error) {
    console.error('❌ Error testing flashloan detection:', error.message);
    console.error('Stack:', error.stack);
  }
}

main().catch(console.error);
