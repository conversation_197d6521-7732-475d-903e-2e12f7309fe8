#!/usr/bin/env node

/**
 * Setup Test Liquidity on Sepolia for MEV Bot Testing
 * This script adds liquidity to Uniswap pools to create testable arbitrage opportunities
 */

const { ethers } = require('ethers');
const chalk = require('chalk');

// Load environment
require('dotenv').config();

// Sepolia addresses
const SEPOLIA_ADDRESSES = {
  WETH: '******************************************',
  USDC: '******************************************',
  UNISWAP_V2_ROUTER: '******************************************',
  UNISWAP_V2_FACTORY: '******************************************',
  UNISWAP_V3_ROUTER: '******************************************',
  UNISWAP_V3_FACTORY: '******************************************'
};

// ABIs
const ERC20_ABI = [
  'function balanceOf(address) view returns (uint256)',
  'function transfer(address to, uint256 amount) returns (bool)',
  'function approve(address spender, uint256 amount) returns (bool)',
  'function allowance(address owner, address spender) view returns (uint256)',
  'function decimals() view returns (uint8)',
  'function symbol() view returns (string)',
  'function mint(address to, uint256 amount) returns (bool)', // For test tokens
  'function faucet() returns (bool)' // For test tokens
];

const UNISWAP_V2_ROUTER_ABI = [
  'function addLiquidity(address tokenA, address tokenB, uint amountADesired, uint amountBDesired, uint amountAMin, uint amountBMin, address to, uint deadline) returns (uint amountA, uint amountB, uint liquidity)',
  'function addLiquidityETH(address token, uint amountTokenDesired, uint amountTokenMin, uint amountETHMin, address to, uint deadline) payable returns (uint amountToken, uint amountETH, uint liquidity)',
  'function swapExactTokensForTokens(uint amountIn, uint amountOutMin, address[] path, address to, uint deadline) returns (uint[] amounts)',
  'function swapExactETHForTokens(uint amountOutMin, address[] path, address to, uint deadline) payable returns (uint[] amounts)',
  'function getAmountsOut(uint amountIn, address[] path) view returns (uint[] amounts)'
];

const WETH_ABI = [
  'function deposit() payable',
  'function withdraw(uint256 amount)',
  'function balanceOf(address) view returns (uint256)',
  'function transfer(address to, uint256 amount) returns (bool)',
  'function approve(address spender, uint256 amount) returns (bool)'
];

async function setupTestLiquidity() {
  console.log(chalk.blue.bold('\n🧪 Setting up Test Liquidity on Sepolia\n'));

  // Initialize provider and wallet
  const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);
  const wallet = new ethers.Wallet(process.env.PRIVATE_KEY, provider);

  console.log(`📋 Wallet Address: ${wallet.address}`);

  // Check initial balances
  const ethBalance = await provider.getBalance(wallet.address);
  console.log(`💰 ETH Balance: ${ethers.formatEther(ethBalance)} ETH`);

  if (ethBalance < ethers.parseEther('0.1')) {
    console.log(chalk.red('❌ Insufficient ETH balance. Get test ETH from:'));
    console.log('   • https://sepoliafaucet.com/');
    console.log('   • https://faucet.sepolia.dev/');
    return;
  }

  // Initialize contracts
  const weth = new ethers.Contract(SEPOLIA_ADDRESSES.WETH, WETH_ABI, wallet);
  const usdc = new ethers.Contract(SEPOLIA_ADDRESSES.USDC, ERC20_ABI, wallet);
  const router = new ethers.Contract(SEPOLIA_ADDRESSES.UNISWAP_V2_ROUTER, UNISWAP_V2_ROUTER_ABI, wallet);

  try {
    // Step 1: Get test USDC tokens
    console.log(chalk.yellow('\n📝 Step 1: Getting test USDC tokens...'));
    
    try {
      // Try to call faucet function if available
      const faucetTx = await usdc.faucet();
      await faucetTx.wait();
      console.log('✅ Called USDC faucet');
    } catch (error) {
      console.log('⚠️  USDC faucet not available, trying alternative methods...');
      
      // Alternative: Try to mint tokens (if test token supports it)
      try {
        const mintAmount = ethers.parseUnits('10000', 6); // 10,000 USDC
        const mintTx = await usdc.mint(wallet.address, mintAmount);
        await mintTx.wait();
        console.log('✅ Minted test USDC tokens');
      } catch (mintError) {
        console.log('⚠️  Cannot mint USDC. You may need to get test USDC from a faucet.');
      }
    }

    // Step 2: Convert ETH to WETH
    console.log(chalk.yellow('\n📝 Step 2: Converting ETH to WETH...'));
    const wethAmount = ethers.parseEther('0.05'); // 0.05 ETH
    const wrapTx = await weth.deposit({ value: wethAmount });
    await wrapTx.wait();
    console.log(`✅ Wrapped ${ethers.formatEther(wethAmount)} ETH to WETH`);

    // Check balances
    const wethBalance = await weth.balanceOf(wallet.address);
    const usdcBalance = await usdc.balanceOf(wallet.address);
    
    console.log(`💰 WETH Balance: ${ethers.formatEther(wethBalance)} WETH`);
    console.log(`💰 USDC Balance: ${ethers.formatUnits(usdcBalance, 6)} USDC`);

    if (usdcBalance < ethers.parseUnits('100', 6)) {
      console.log(chalk.red('\n❌ Insufficient USDC balance for liquidity provision.'));
      console.log('Please get test USDC from a faucet or contact the token contract owner.');
      return;
    }

    // Step 3: Approve tokens for router
    console.log(chalk.yellow('\n📝 Step 3: Approving tokens for Uniswap router...'));
    
    const wethApproval = ethers.parseEther('1'); // Approve 1 WETH
    const usdcApproval = ethers.parseUnits('5000', 6); // Approve 5000 USDC

    const wethApproveTx = await weth.approve(SEPOLIA_ADDRESSES.UNISWAP_V2_ROUTER, wethApproval);
    await wethApproveTx.wait();
    console.log('✅ Approved WETH for router');

    const usdcApproveTx = await usdc.approve(SEPOLIA_ADDRESSES.UNISWAP_V2_ROUTER, usdcApproval);
    await usdcApproveTx.wait();
    console.log('✅ Approved USDC for router');

    // Step 4: Add liquidity to Uniswap V2
    console.log(chalk.yellow('\n📝 Step 4: Adding liquidity to Uniswap V2...'));
    
    const liquidityWETH = ethers.parseEther('0.02'); // 0.02 WETH
    const liquidityUSDC = ethers.parseUnits('50', 6); // 50 USDC (price ~2500 USDC/ETH)
    const deadline = Math.floor(Date.now() / 1000) + 1800; // 30 minutes

    const addLiquidityTx = await router.addLiquidity(
      SEPOLIA_ADDRESSES.WETH,
      SEPOLIA_ADDRESSES.USDC,
      liquidityWETH,
      liquidityUSDC,
      ethers.parseEther('0.015'), // Min WETH (25% slippage)
      ethers.parseUnits('37.5', 6), // Min USDC (25% slippage)
      wallet.address,
      deadline
    );

    const receipt = await addLiquidityTx.wait();
    console.log(`✅ Added liquidity to Uniswap V2 (TX: ${receipt.hash})`);

    // Step 5: Create price difference by making a swap
    console.log(chalk.yellow('\n📝 Step 5: Creating price difference with test swap...'));
    
    const swapAmount = ethers.parseUnits('20', 6); // Swap 20 USDC for WETH
    const path = [SEPOLIA_ADDRESSES.USDC, SEPOLIA_ADDRESSES.WETH];
    
    // Get expected output
    const amountsOut = await router.getAmountsOut(swapAmount, path);
    const minAmountOut = (amountsOut[1] * BigInt(95)) / BigInt(100); // 5% slippage

    const swapTx = await router.swapExactTokensForTokens(
      swapAmount,
      minAmountOut,
      path,
      wallet.address,
      deadline
    );

    await swapTx.wait();
    console.log(`✅ Executed test swap: 20 USDC → ${ethers.formatEther(amountsOut[1])} WETH`);

    // Step 6: Check final state
    console.log(chalk.green.bold('\n🎉 Test Liquidity Setup Complete!\n'));
    
    const finalWethBalance = await weth.balanceOf(wallet.address);
    const finalUsdcBalance = await usdc.balanceOf(wallet.address);
    
    console.log('📊 Final Balances:');
    console.log(`   WETH: ${ethers.formatEther(finalWethBalance)} WETH`);
    console.log(`   USDC: ${ethers.formatUnits(finalUsdcBalance, 6)} USDC`);
    
    console.log('\n🎯 Next Steps:');
    console.log('1. Run the MEV bot to detect opportunities');
    console.log('2. The price difference should create arbitrage opportunities');
    console.log('3. Monitor the bot dashboard for detected opportunities');

  } catch (error) {
    console.error(chalk.red('\n❌ Error setting up test liquidity:'));
    console.error(error.message);
    
    if (error.message.includes('insufficient funds')) {
      console.log('\n💡 Suggestions:');
      console.log('• Get more test ETH from Sepolia faucets');
      console.log('• Reduce liquidity amounts in the script');
    }
  }
}

// Run the setup
setupTestLiquidity().catch(console.error);
