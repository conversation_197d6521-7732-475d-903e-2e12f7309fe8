const { ethers } = require('ethers');
const chalk = require('chalk');

// Import configuration
require('dotenv').config();

async function diagnosePoolManager() {
  console.log(chalk.blue.bold('🔍 PoolManager Diagnostic Tool'));
  console.log(chalk.gray('═'.repeat(60)));

  // Configuration check
  console.log(chalk.yellow.bold('\n📋 Configuration Check'));
  console.log(chalk.gray('─'.repeat(40)));
  
  const chainId = process.env.CHAIN_ID || '1';
  const rpcUrl = process.env.RPC_URL || 'http://localhost:8545';
  
  console.log(`${chalk.cyan('Chain ID:')} ${chainId}`);
  console.log(`${chalk.cyan('RPC URL:')} ${rpcUrl}`);
  
  // Network-specific addresses
  const isMainnet = chainId === '1';
  const addresses = isMainnet ? {
    WETH: '******************************************',
    USDC: '******************************************',
    USDT: '******************************************',
    DAI: '******************************************',
    UNISWAP_V2_FACTORY: '******************************************',
    UNISWAP_V3_FACTORY: '******************************************'
  } : {
    WETH: '******************************************',
    USDC: '******************************************',
    USDT: '******************************************',
    DAI: '******************************************',
    UNISWAP_V2_FACTORY: '******************************************',
    UNISWAP_V3_FACTORY: '******************************************'
  };

  console.log(`${chalk.cyan('Network:')} ${isMainnet ? 'Mainnet' : 'Sepolia'}`);

  // RPC Connection Test
  console.log(chalk.yellow.bold('\n🌐 RPC Connection Test'));
  console.log(chalk.gray('─'.repeat(40)));
  
  let provider;
  try {
    provider = new ethers.JsonRpcProvider(rpcUrl);
    const blockNumber = await provider.getBlockNumber();
    console.log(`${chalk.green('✓ RPC Connected')} - Block: ${blockNumber}`);
    
    const network = await provider.getNetwork();
    console.log(`${chalk.cyan('Network Chain ID:')} ${network.chainId}`);
    
    if (network.chainId.toString() !== chainId) {
      console.log(chalk.red(`❌ Chain ID mismatch! Expected: ${chainId}, Got: ${network.chainId}`));
    }
  } catch (error) {
    console.log(chalk.red(`❌ RPC Connection Failed: ${error.message}`));
    return;
  }

  // Factory Contract Tests
  console.log(chalk.yellow.bold('\n🏭 Factory Contract Tests'));
  console.log(chalk.gray('─'.repeat(40)));

  // Test Uniswap V2 Factory
  try {
    const v2Factory = new ethers.Contract(
      addresses.UNISWAP_V2_FACTORY,
      ['function getPair(address tokenA, address tokenB) view returns (address pair)'],
      provider
    );
    
    const wethUsdcPair = await v2Factory.getPair(addresses.WETH, addresses.USDC);
    console.log(`${chalk.green('✓ Uniswap V2 Factory')} - WETH/USDC pair: ${wethUsdcPair}`);
    
    if (wethUsdcPair === ethers.ZeroAddress) {
      console.log(chalk.yellow('⚠️  WETH/USDC pair not found on Uniswap V2'));
    }
  } catch (error) {
    console.log(chalk.red(`❌ Uniswap V2 Factory Error: ${error.message}`));
  }

  // Test Uniswap V3 Factory
  try {
    const v3Factory = new ethers.Contract(
      addresses.UNISWAP_V3_FACTORY,
      ['function getPool(address tokenA, address tokenB, uint24 fee) view returns (address pool)'],
      provider
    );
    
    const wethUsdcPool3000 = await v3Factory.getPool(addresses.WETH, addresses.USDC, 3000);
    const wethUsdcPool500 = await v3Factory.getPool(addresses.WETH, addresses.USDC, 500);
    const wethUsdcPool10000 = await v3Factory.getPool(addresses.WETH, addresses.USDC, 10000);
    
    console.log(`${chalk.green('✓ Uniswap V3 Factory')} - WETH/USDC pools:`);
    console.log(`   0.3% fee: ${wethUsdcPool3000}`);
    console.log(`   0.05% fee: ${wethUsdcPool500}`);
    console.log(`   1% fee: ${wethUsdcPool10000}`);
    
    if (wethUsdcPool3000 === ethers.ZeroAddress && wethUsdcPool500 === ethers.ZeroAddress && wethUsdcPool10000 === ethers.ZeroAddress) {
      console.log(chalk.yellow('⚠️  No WETH/USDC pools found on Uniswap V3'));
    }
  } catch (error) {
    console.log(chalk.red(`❌ Uniswap V3 Factory Error: ${error.message}`));
  }

  // Pool Contract Tests
  console.log(chalk.yellow.bold('\n🏊 Pool Contract Tests'));
  console.log(chalk.gray('─'.repeat(40)));

  // Test a known pool if it exists
  try {
    const v3Factory = new ethers.Contract(
      addresses.UNISWAP_V3_FACTORY,
      ['function getPool(address tokenA, address tokenB, uint24 fee) view returns (address pool)'],
      provider
    );
    
    const poolAddress = await v3Factory.getPool(addresses.WETH, addresses.USDC, 3000);
    
    if (poolAddress !== ethers.ZeroAddress) {
      const poolContract = new ethers.Contract(
        poolAddress,
        [
          'function slot0() view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)',
          'function liquidity() view returns (uint128)',
          'function token0() view returns (address)',
          'function token1() view returns (address)',
          'function fee() view returns (uint24)'
        ],
        provider
      );
      
      const [slot0, liquidity, token0, token1, fee] = await Promise.all([
        poolContract.slot0(),
        poolContract.liquidity(),
        poolContract.token0(),
        poolContract.token1(),
        poolContract.fee()
      ]);
      
      console.log(`${chalk.green('✓ Pool Contract Test')} - ${poolAddress}`);
      console.log(`   Token0: ${token0}`);
      console.log(`   Token1: ${token1}`);
      console.log(`   Fee: ${fee}`);
      console.log(`   Liquidity: ${liquidity}`);
      console.log(`   Current Tick: ${slot0.tick}`);
    } else {
      console.log(chalk.yellow('⚠️  No pools available for testing'));
    }
  } catch (error) {
    console.log(chalk.red(`❌ Pool Contract Error: ${error.message}`));
  }

  // Token Contract Tests
  console.log(chalk.yellow.bold('\n🪙 Token Contract Tests'));
  console.log(chalk.gray('─'.repeat(40)));

  const tokens = [
    { name: 'WETH', address: addresses.WETH },
    { name: 'USDC', address: addresses.USDC },
    { name: 'USDT', address: addresses.USDT },
    { name: 'DAI', address: addresses.DAI }
  ];

  for (const token of tokens) {
    try {
      const tokenContract = new ethers.Contract(
        token.address,
        [
          'function symbol() view returns (string)',
          'function decimals() view returns (uint8)',
          'function name() view returns (string)'
        ],
        provider
      );
      
      const [symbol, decimals, name] = await Promise.all([
        tokenContract.symbol(),
        tokenContract.decimals(),
        tokenContract.name()
      ]);
      
      console.log(`${chalk.green('✓')} ${token.name}: ${symbol} (${decimals} decimals) - ${name}`);
    } catch (error) {
      console.log(chalk.red(`❌ ${token.name} Error: ${error.message}`));
    }
  }

  // Summary and Recommendations
  console.log(chalk.yellow.bold('\n💡 Recommendations'));
  console.log(chalk.gray('─'.repeat(40)));
  
  if (isMainnet) {
    console.log('• Mainnet detected - pools should be available');
    console.log('• Check if your local RPC node is fully synced');
    console.log('• Consider using a backup RPC provider (Alchemy/Infura)');
  } else {
    console.log('• Sepolia testnet has limited liquidity');
    console.log('• Many pools may not exist or have zero liquidity');
    console.log('• Consider testing on mainnet fork instead');
  }
  
  console.log('• Enable debug logging to see detailed pool lookup attempts');
  console.log('• Check that token addresses are correct for your network');
  
  console.log(chalk.green.bold('\n✅ Diagnostic Complete'));
}

// Run the diagnostic
diagnosePoolManager().catch(console.error);
