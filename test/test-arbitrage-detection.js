const { ethers } = require('ethers');
const chalk = require('chalk');

// Import configuration
require('dotenv').config();

async function testArbitrageDetection() {
  console.log(chalk.blue.bold('🔍 Arbitrage Detection Test'));
  console.log(chalk.gray('═'.repeat(60)));

  // Configuration check
  console.log(chalk.yellow.bold('\n📋 Configuration Check'));
  console.log(chalk.gray('─'.repeat(40)));
  
  const chainId = process.env.CHAIN_ID || '1';
  const rpcUrl = process.env.RPC_URL || 'http://localhost:8545';
  
  console.log(`${chalk.cyan('Chain ID:')} ${chainId}`);
  console.log(`${chalk.cyan('RPC URL:')} ${rpcUrl}`);
  
  // Network-specific addresses and tokens
  const isMainnet = chainId === '1';
  const addresses = isMainnet ? {
    WETH: '******************************************',
    USDC: '******************************************',
    USDT: '******************************************',
    DAI: '******************************************',
    UNISWAP_V2_FACTORY: '******************************************',
    UNISWAP_V3_FACTORY: '******************************************'
  } : {
    WETH: '******************************************',
    USDC: '******************************************',
    USDT: '******************************************',
    DAI: '******************************************',
    UNISWAP_V2_FACTORY: '******************************************',
    UNISWAP_V3_FACTORY: '******************************************'
  };

  const tokens = isMainnet ? [
    { address: addresses.WETH, symbol: 'WETH', decimals: 18, name: 'Wrapped Ether' },
    { address: addresses.USDC, symbol: 'USDC', decimals: 6, name: 'USD Coin' },
    { address: addresses.USDT, symbol: 'USDT', decimals: 6, name: 'Tether USD' },
    { address: addresses.DAI, symbol: 'DAI', decimals: 18, name: 'Dai Stablecoin' }
  ] : [
    { address: addresses.WETH, symbol: 'WETH', decimals: 18, name: 'Wrapped Ether' },
    { address: addresses.USDC, symbol: 'USDC', decimals: 6, name: 'USD Coin' },
    { address: addresses.USDT, symbol: 'USDT', decimals: 6, name: 'Tether USD' },
    { address: addresses.DAI, symbol: 'DAI', decimals: 18, name: 'Dai Stablecoin' }
  ];

  console.log(`${chalk.cyan('Network:')} ${isMainnet ? 'Mainnet' : 'Sepolia'}`);
  console.log(`${chalk.cyan('Tokens to test:')} ${tokens.map(t => t.symbol).join(', ')}`);

  // RPC Connection Test
  console.log(chalk.yellow.bold('\n🌐 RPC Connection Test'));
  console.log(chalk.gray('─'.repeat(40)));
  
  let provider;
  try {
    provider = new ethers.JsonRpcProvider(rpcUrl);
    const blockNumber = await provider.getBlockNumber();
    console.log(`${chalk.green('✓ RPC Connected')} - Block: ${blockNumber}`);
  } catch (error) {
    console.log(chalk.red(`❌ RPC Connection Failed: ${error.message}`));
    return;
  }

  // Pool Detection Test
  console.log(chalk.yellow.bold('\n🏊 Pool Detection Test'));
  console.log(chalk.gray('─'.repeat(40)));

  const v2Factory = new ethers.Contract(
    addresses.UNISWAP_V2_FACTORY,
    ['function getPair(address tokenA, address tokenB) view returns (address pair)'],
    provider
  );

  const v3Factory = new ethers.Contract(
    addresses.UNISWAP_V3_FACTORY,
    ['function getPool(address tokenA, address tokenB, uint24 fee) view returns (address pool)'],
    provider
  );

  const poolResults = [];

  // Test all token pairs
  for (let i = 0; i < tokens.length; i++) {
    for (let j = i + 1; j < tokens.length; j++) {
      const token0 = tokens[i];
      const token1 = tokens[j];
      
      console.log(`\n${chalk.cyan('Testing pair:')} ${token0.symbol}/${token1.symbol}`);

      try {
        // Test V2 pool
        const v2Pair = await v2Factory.getPair(token0.address, token1.address);
        const v2Exists = v2Pair !== ethers.ZeroAddress;
        
        // Test V3 pools (multiple fee tiers)
        const v3Pool500 = await v3Factory.getPool(token0.address, token1.address, 500);
        const v3Pool3000 = await v3Factory.getPool(token0.address, token1.address, 3000);
        const v3Pool10000 = await v3Factory.getPool(token0.address, token1.address, 10000);
        
        const v3Pools = [
          { fee: 500, address: v3Pool500, exists: v3Pool500 !== ethers.ZeroAddress },
          { fee: 3000, address: v3Pool3000, exists: v3Pool3000 !== ethers.ZeroAddress },
          { fee: 10000, address: v3Pool10000, exists: v3Pool10000 !== ethers.ZeroAddress }
        ];

        console.log(`   V2: ${v2Exists ? chalk.green('✓ Found') : chalk.red('✗ Not found')} ${v2Exists ? v2Pair : ''}`);
        
        v3Pools.forEach(pool => {
          console.log(`   V3 (${pool.fee/10000}%): ${pool.exists ? chalk.green('✓ Found') : chalk.red('✗ Not found')} ${pool.exists ? pool.address : ''}`);
        });

        // Check if arbitrage is possible (need both V2 and V3 pools)
        const v3Available = v3Pools.some(p => p.exists);
        const arbitragePossible = v2Exists && v3Available;
        
        if (arbitragePossible) {
          console.log(`   ${chalk.green('🎯 Arbitrage possible!')}`);
          poolResults.push({
            pair: `${token0.symbol}/${token1.symbol}`,
            v2: v2Exists,
            v3: v3Available,
            arbitragePossible: true
          });
        } else {
          console.log(`   ${chalk.yellow('⚠️  No arbitrage opportunity')}`);
          poolResults.push({
            pair: `${token0.symbol}/${token1.symbol}`,
            v2: v2Exists,
            v3: v3Available,
            arbitragePossible: false
          });
        }

      } catch (error) {
        console.log(`   ${chalk.red('❌ Error:')} ${error.message}`);
      }
    }
  }

  // Price Comparison Test (for pairs with both V2 and V3 pools)
  console.log(chalk.yellow.bold('\n💰 Price Comparison Test'));
  console.log(chalk.gray('─'.repeat(40)));

  const arbitragePairs = poolResults.filter(p => p.arbitragePossible);
  
  if (arbitragePairs.length === 0) {
    console.log(chalk.red('❌ No pairs available for price comparison'));
  } else {
    console.log(`${chalk.cyan('Testing price differences for:')} ${arbitragePairs.map(p => p.pair).join(', ')}`);
    
    // Test WETH/USDC if available (most liquid pair)
    const wethUsdcPair = arbitragePairs.find(p => p.pair === 'WETH/USDC');
    if (wethUsdcPair) {
      try {
        console.log(`\n${chalk.cyan('Detailed price analysis for WETH/USDC:')}`);
        
        // Get V2 price
        const v2Pair = await v2Factory.getPair(addresses.WETH, addresses.USDC);
        const v2Contract = new ethers.Contract(
          v2Pair,
          [
            'function getReserves() view returns (uint112 reserve0, uint112 reserve1, uint32 blockTimestampLast)',
            'function token0() view returns (address)',
            'function token1() view returns (address)'
          ],
          provider
        );
        
        const [reserves, token0] = await Promise.all([
          v2Contract.getReserves(),
          v2Contract.token0()
        ]);
        
        // Calculate V2 price (USDC per WETH)
        const isWethToken0 = token0.toLowerCase() === addresses.WETH.toLowerCase();
        const wethReserve = isWethToken0 ? reserves[0] : reserves[1];
        const usdcReserve = isWethToken0 ? reserves[1] : reserves[0];
        
        const v2Price = Number(ethers.formatUnits(usdcReserve, 6)) / Number(ethers.formatEther(wethReserve));
        
        console.log(`   V2 Price: ${v2Price.toFixed(2)} USDC per WETH`);
        console.log(`   V2 Reserves: ${ethers.formatEther(wethReserve)} WETH, ${ethers.formatUnits(usdcReserve, 6)} USDC`);
        
        // Get V3 price
        const v3Pool = await v3Factory.getPool(addresses.WETH, addresses.USDC, 3000);
        const v3Contract = new ethers.Contract(
          v3Pool,
          [
            'function slot0() view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)',
            'function token0() view returns (address)',
            'function liquidity() view returns (uint128)'
          ],
          provider
        );
        
        const [slot0, v3Token0, liquidity] = await Promise.all([
          v3Contract.slot0(),
          v3Contract.token0(),
          v3Contract.liquidity()
        ]);
        
        // Calculate V3 price from sqrtPriceX96
        const sqrtPriceX96 = slot0.sqrtPriceX96;
        const price = (Number(sqrtPriceX96) / (2 ** 96)) ** 2;
        
        // Adjust for token order and decimals
        const isWethToken0V3 = v3Token0.toLowerCase() === addresses.WETH.toLowerCase();
        const v3Price = isWethToken0V3 ? price * (10 ** 12) : (1 / price) / (10 ** 12); // Adjust for USDC 6 decimals vs WETH 18 decimals
        
        console.log(`   V3 Price: ${v3Price.toFixed(2)} USDC per WETH`);
        console.log(`   V3 Liquidity: ${liquidity}`);
        console.log(`   V3 Tick: ${slot0.tick}`);
        
        // Calculate price difference
        const priceDiff = Math.abs(v2Price - v3Price);
        const priceDiffPercent = (priceDiff / Math.min(v2Price, v3Price)) * 100;
        
        console.log(`   ${chalk.cyan('Price Difference:')} ${priceDiff.toFixed(2)} USDC (${priceDiffPercent.toFixed(3)}%)`);
        
        if (priceDiffPercent > 0.1) {
          console.log(`   ${chalk.green('🎯 Potential arbitrage opportunity!')} (>${0.1}% difference)`);
        } else {
          console.log(`   ${chalk.yellow('⚠️  Small price difference')} (<${0.1}%)`);
        }
        
      } catch (error) {
        console.log(`   ${chalk.red('❌ Price analysis error:')} ${error.message}`);
      }
    }
  }

  // Summary
  console.log(chalk.yellow.bold('\n📊 Summary'));
  console.log(chalk.gray('─'.repeat(40)));
  
  const totalPairs = poolResults.length;
  const arbitragePossibleCount = poolResults.filter(p => p.arbitragePossible).length;
  
  console.log(`${chalk.cyan('Total pairs tested:')} ${totalPairs}`);
  console.log(`${chalk.cyan('Arbitrage possible:')} ${arbitragePossibleCount}`);
  console.log(`${chalk.cyan('Success rate:')} ${((arbitragePossibleCount / totalPairs) * 100).toFixed(1)}%`);
  
  if (arbitragePossibleCount === 0) {
    console.log(chalk.red.bold('\n❌ No arbitrage opportunities detected!'));
    console.log(chalk.yellow('This explains why the MEV bot finds 0 opportunities.'));
    console.log(chalk.yellow('Possible reasons:'));
    console.log('• Limited liquidity on testnet (if using Sepolia)');
    console.log('• Pools may not exist for these token pairs');
    console.log('• Price differences are too small to be profitable');
    console.log('• Network connectivity issues');
  } else {
    console.log(chalk.green.bold('\n✅ Arbitrage opportunities detected!'));
    console.log(chalk.yellow('If MEV bot still shows 0 opportunities, check:'));
    console.log('• Profit threshold settings (MIN_PROFIT_WEI)');
    console.log('• Gas price calculations');
    console.log('• Pool loading errors in PoolManager');
  }
  
  console.log(chalk.green.bold('\n✅ Test Complete'));
}

// Run the test
testArbitrageDetection().catch(console.error);
