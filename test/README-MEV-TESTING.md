# Ultimate MEV Strategy Testing Suite

This comprehensive testing suite validates all MEV strategies with profit verification and atomicity testing on Hardhat network.

## 🎯 Test Coverage

### Strategies Tested
- **🥪 Sandwich Attacks** - Front-run and back-run victim transactions
- **🔄 Arbitrage** - Cross-DEX price difference exploitation  
- **🏃 Frontrunning** - Priority execution of profitable transactions
- **🔗 MEV-Share Backrun** - Backrun opportunities from user transactions
- **🔗 Multi-Block** - Cross-block MEV extraction strategies

### Verification Features
- ✅ **Profit Verification** - Actual vs calculated profit comparison
- ✅ **Atomicity Testing** - Bundle execution and transaction ordering
- ✅ **Gas Optimization** - Gas price strategies and cost analysis
- ✅ **Success Rate Monitoring** - Opportunity detection and execution rates
- ✅ **Balance Tracking** - Before/after balance verification

## 🚀 Quick Start

### Prerequisites
```bash
# Ensure Hardhat node is running
npm run hardhat:node

# Build the project
npm run build
```

### Run All Strategy Tests
```bash
# Ultimate test suite (all strategies)
npm run test:ultimate-mev

# Generate MEV opportunities first, then test
npm run test:all-strategies
```

### Individual Strategy Tests
```bash
# Test sandwich attacks only
npm run test:sandwich

# Test frontrunning attacks only  
npm run test:frontrunning

# Generate diverse MEV opportunities
npm run generate:mev-opportunities
```

## 📊 Test Results Interpretation

### Success Metrics
- **Opportunity Detection Rate**: % of transactions that create MEV opportunities
- **Execution Success Rate**: % of detected opportunities successfully executed
- **Return on Investment**: Net profit / Gas costs ratio
- **Average Profit per Attack**: Mean profit across successful executions

### Performance Benchmarks
- **Excellent**: >70% success rate, >100% ROI
- **Good**: 50-70% success rate, 50-100% ROI  
- **Needs Improvement**: <50% success rate, <50% ROI

## 🧪 Test Configuration

### Environment Variables
```bash
# Test configuration
CHAIN_ID=31337                    # Hardhat network
DRY_RUN=true                     # Simulate without actual execution
MIN_PROFIT_ETH=0.001             # Minimum profit threshold
ENABLE_TEST_MODE=true            # Enable testing features
MOCK_OPPORTUNITIES=true          # Use mock data for testing

# Strategy enablement
ENABLE_SANDWICH_STRATEGY=true
ENABLE_ARBITRAGE_STRATEGY=true
ENABLE_FRONTRUNNING_STRATEGY=true
ENABLE_MEV_SHARE=true
ENABLE_MULTI_BLOCK_STRATEGY=true
```

### Test Parameters
```javascript
const testConfig = {
  chainId: 31337,                 // Hardhat
  dryRun: true,                   // Safe testing mode
  minProfitEth: 0.001,           // 0.001 ETH minimum
  transactionCount: 30,           // Test transactions
  testDuration: 120000,           // 2 minutes
  profitVerification: true,       // Verify profits
  atomicityVerification: true     // Verify atomicity
};
```

## 📁 Test Files

### Core Test Suites
- `test-ultimate-mev-strategies.js` - Comprehensive all-strategy test
- `test-sandwich-attack.js` - Dedicated sandwich attack testing
- `test-frontrunning-attack.js` - Dedicated frontrunning testing
- `test-hardhat-integration.js` - Existing flashloan integration test

### Supporting Files
- `scripts/generate-mev-opportunities.js` - Transaction generator
- `src/strategies/frontrunning.ts` - New frontrunning strategy
- `src/strategies/multi-block.ts` - New multi-block strategy

## 🔍 Test Scenarios

### Sandwich Attack Tests
```javascript
// Large DEX swaps (high sandwich potential)
const largeSwap = {
  value: ethers.parseEther("25"),
  to: UNISWAP_V2_ROUTER,
  data: "0x7ff36ab5...", // swapExactETHForTokens
  gasPrice: ethers.parseUnits("35", "gwei")
};

// Verifies: Bundle structure, gas ordering, profit calculation
```

### Frontrunning Tests  
```javascript
// High-value transactions (frontrun targets)
const highValueTx = {
  value: ethers.parseEther("50"),
  gasPrice: ethers.parseUnits("40", "gwei"),
  to: "0x...", // Target address
};

// Verifies: Gas price advantage, execution priority, profit margins
```

### Arbitrage Tests
```javascript
// Cross-DEX price differences
const arbOpportunity = {
  dex1: UNISWAP_V2,
  dex2: UNISWAP_V3, 
  token: "WETH/USDC",
  priceDiff: 0.02 // 2% difference
};

// Verifies: Multi-DEX execution, atomic swaps, profit calculation
```

## 📈 Expected Results

### Typical Performance
```
Strategy Performance Summary:
sandwich        :  15 opps |  12 exec |  80.0% success |   0.0450 ETH profit | 125.5% ROI
arbitrage       :   8 opps |   6 exec |  75.0% success |   0.0320 ETH profit | 110.2% ROI  
frontrunning    :  12 opps |   9 exec |  75.0% success |   0.0280 ETH profit |  95.8% ROI
mev-share       :   5 opps |   4 exec |  80.0% success |   0.0150 ETH profit | 120.0% ROI
multi-block     :   3 opps |   2 exec |  66.7% success |   0.0100 ETH profit |  85.5% ROI

Overall Performance:
Total Opportunities: 43
Total Executions: 33  
Success Rate: 76.7%
Net Profit: 0.1300 ETH
Overall ROI: 108.3%
```

## 🛠️ Troubleshooting

### Common Issues

#### No Opportunities Detected
```bash
# Check strategy configuration
grep -r "ENABLE_.*_STRATEGY" .env

# Verify transaction generation
npm run generate:mev-opportunities

# Check minimum profit thresholds
echo $MIN_PROFIT_ETH
```

#### Low Success Rates
```bash
# Check gas estimation
# Review bundle simulation
# Verify network connectivity
# Adjust profit thresholds
```

#### Profit Calculation Mismatches
```bash
# Enable detailed logging
DEBUG=true npm run test:ultimate-mev

# Check balance tracking
# Verify gas cost calculations
# Review profit formulas
```

### Debug Mode
```bash
# Enable verbose logging
DEBUG=true npm run test:ultimate-mev

# Check individual strategy logs
DEBUG=sandwich npm run test:sandwich
DEBUG=frontrunning npm run test:frontrunning
```

## 🔄 Continuous Testing

### Automated Testing Pipeline
```bash
# 1. Generate opportunities
npm run generate:mev-opportunities

# 2. Run comprehensive tests  
npm run test:ultimate-mev

# 3. Individual strategy validation
npm run test:sandwich
npm run test:frontrunning

# 4. Performance analysis
# Review logs and adjust parameters
```

### Performance Monitoring
- Monitor opportunity detection rates
- Track execution success rates  
- Analyze profit margins and ROI
- Optimize gas strategies based on results

## 📋 Next Steps

### After Successful Testing
1. **🌐 Sepolia Testnet** - Test with real transaction flow
2. **🔧 Parameter Optimization** - Fine-tune based on results  
3. **📊 Performance Monitoring** - Track metrics over time
4. **🚀 Mainnet Deployment** - Deploy with conservative settings

### Optimization Areas
- Gas price strategies
- Profit threshold tuning
- Opportunity detection logic
- Bundle simulation accuracy
- Transaction timing optimization

## 📞 Support

For issues or questions:
1. Check test logs for detailed error messages
2. Review configuration parameters
3. Verify Hardhat network is running
4. Ensure all dependencies are built (`npm run build`)

---

**⚠️ Important**: Always run tests in DRY_RUN mode first to verify functionality before live execution.
