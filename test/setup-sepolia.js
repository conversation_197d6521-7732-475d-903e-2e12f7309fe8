#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const { Wallet } = require('ethers');

console.log('🧪 Setting up MEV Bot for Sepolia Testnet...\n');

// Generate new wallets
console.log('🔑 Generating new private keys...');
const tradingWallet = Wallet.createRandom();
const flashbotsWallet = Wallet.createRandom();

console.log(`✅ Trading Wallet: ${tradingWallet.address}`);
console.log(`✅ Flashbots Signer: ${flashbotsWallet.address}\n`);

// Read the example env file
const envExample = fs.readFileSync('.env.example', 'utf8');

// Replace the placeholder keys
let envContent = envExample
  .replace('PRIVATE_KEY=0x0000000000000000000000000000000000000000000000000000000000000000', 
           `PRIVATE_KEY=${tradingWallet.privateKey}`)
  .replace('FLASHBOTS_SIGNER_KEY=0x0000000000000000000000000000000000000000000000000000000000000000', 
           `FLASHBOTS_SIGNER_KEY=${flashbotsWallet.privateKey}`);

// Prompt for Infura key
console.log('🔗 You need an Infura Project ID for Sepolia access.');
console.log('   1. Go to https://infura.io');
console.log('   2. Create a free account');
console.log('   3. Create a new project');
console.log('   4. Copy the Project ID\n');

// Check if user wants to enter Infura key now
const readline = require('readline');
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

rl.question('Enter your Infura Project ID (or press Enter to skip): ', (infuraKey) => {
  if (infuraKey.trim()) {
    envContent = envContent
      .replace(/YOUR_INFURA_KEY/g, infuraKey.trim());
    console.log('✅ Infura key configured');
  } else {
    console.log('⚠️  You can add your Infura key later by editing .env');
  }

  // Write the .env file
  fs.writeFileSync('.env', envContent);
  console.log('✅ .env file created with Sepolia configuration\n');

  // Show next steps
  console.log('🚀 Setup Complete! Next steps:\n');
  
  if (!infuraKey.trim()) {
    console.log('1. Edit .env and replace YOUR_INFURA_KEY with your actual Infura Project ID');
  }
  
  console.log('2. Get Sepolia ETH for testing:');
  console.log(`   - Visit: https://sepoliafaucet.com/`);
  console.log(`   - Send Sepolia ETH to: ${tradingWallet.address}`);
  console.log('   - You need at least 0.1 ETH for testing\n');
  
  console.log('3. Build and run the bot:');
  console.log('   npm run build');
  console.log('   npm run dev\n');
  
  console.log('📋 Your Configuration:');
  console.log(`   Network: Sepolia Testnet (Chain ID: 11155111)`);
  console.log(`   Trading Wallet: ${tradingWallet.address}`);
  console.log(`   Flashbots Signer: ${flashbotsWallet.address}`);
  console.log(`   Dry Run: Enabled (safe mode)`);
  console.log(`   Min Profit: 0.1 ETH (testnet setting)\n`);
  
  console.log('🔗 Useful Links:');
  console.log('   - Sepolia Faucet: https://sepoliafaucet.com/');
  console.log('   - Sepolia Explorer: https://sepolia.etherscan.io/');
  console.log('   - Setup Guide: ./SEPOLIA_SETUP.md\n');
  
  console.log('⚠️  Remember: This is testnet - perfect for learning and testing!');
  
  rl.close();
});
