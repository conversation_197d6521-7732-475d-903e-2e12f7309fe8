const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("FlashloanArbitrage", function () {
  let flashloanArbitrage;
  let owner;
  let addr1;
  let mockPoolAddressesProvider;
  let mockAavePool;
  let mockUSDC;
  let mockWETH;
  let mockDEXRouter;

  beforeEach(async function () {
    [owner, addr1] = await ethers.getSigners();

    // Deploy MockAavePool
    const MockAavePoolFactory = await ethers.getContractFactory("MockAavePool");
    mockAavePool = await MockAavePoolFactory.deploy();
    await mockAavePool.waitForDeployment();

    // Deploy MockPoolAddressesProvider
    const MockPoolAddressesProviderFactory = await ethers.getContractFactory("MockPoolAddressesProvider");
    mockPoolAddressesProvider = await MockPoolAddressesProviderFactory.deploy(mockAavePool.target);
    await mockPoolAddressesProvider.waitForDeployment();

    // Deploy MockERC20 tokens
    const MockERC20Factory = await ethers.getContractFactory("MockERC20");
    mockUSDC = await MockERC20Factory.deploy("Mock USDC", "mUSDC");
    await mockUSDC.waitForDeployment();
    mockWETH = await MockERC20Factory.deploy("Mock WETH", "mWETH");
    await mockWETH.waitForDeployment();

    // Deploy MockDEXRouter
    const MockDEXRouterFactory = await ethers.getContractFactory("MockDEXRouter");
    mockDEXRouter = await MockDEXRouterFactory.deploy();
    await mockDEXRouter.waitForDeployment();

    // Deploy FlashloanArbitrage contract
    const FlashloanArbitrageFactory = await ethers.getContractFactory("FlashloanArbitrage");
    flashloanArbitrage = await FlashloanArbitrageFactory.deploy(mockPoolAddressesProvider.target);
    await flashloanArbitrage.waitForDeployment();

    // Fund the MockAavePool with mock tokens
    await mockUSDC.mint(mockAavePool.target, ethers.parseUnits("10000000", 6)); // 10M mUSDC
    await mockWETH.mint(mockAavePool.target, ethers.parseUnits("10000", 18)); // 10k mWETH

    // Fund the MockDEXRouter with mock tokens (liquidity for swaps)
    await mockUSDC.mint(mockDEXRouter.target, ethers.parseUnits("10000000", 6)); // 10M mUSDC
    await mockWETH.mint(mockDEXRouter.target, ethers.parseUnits("10000", 18)); // 10k mWETH

    // Fund the FlashloanArbitrage contract with some initial tokens
    await mockUSDC.mint(flashloanArbitrage.target, ethers.parseUnits("100", 6)); // 100 mUSDC
  });

  describe("Deployment", function () {
    it("Should deploy the FlashloanArbitrage contract", async function () {
      expect(flashloanArbitrage.target).to.not.be.undefined;
      expect(await flashloanArbitrage.owner()).to.equal(owner.address);
    });

    it("Should have correct chain ID", async function () {
      const chainId = await flashloanArbitrage.CHAIN_ID();
      expect(chainId).to.equal(31337); // Hardhat network chain ID
    });
  });

  describe("Balance Management", function () {
    it("Should correctly report token balances", async function () {
      const usdcBalance = await flashloanArbitrage.getBalance(mockUSDC.target);
      expect(usdcBalance).to.equal(ethers.parseUnits("100", 6));

      const wethBalance = await flashloanArbitrage.getBalance(mockWETH.target);
      expect(wethBalance).to.equal(0);
    });
  });

  describe("Flashloan and Arbitrage Execution", function () {
    it("Should execute a flashloan and arbitrage with proper swap functions", async function () {
      const flashloanAmount = ethers.parseUnits("1000", 6); // 1000 mUSDC
      const amountOutMin = ethers.parseUnits("990", 6); // 990 mUSDC (1% slippage)

      // Create interfaces for proper swap function encoding
      const mockDEXRouterInterface = new ethers.Interface([
        "function swapExactTokensForTokens(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts)"
      ]);

      // Encode buy swap data (USDC -> WETH)
      const buySwapData = mockDEXRouterInterface.encodeFunctionData("swapExactTokensForTokens", [
        flashloanAmount, // amountIn
        amountOutMin, // amountOutMin (will be ignored in mock, but good practice)
        [mockUSDC.target, mockWETH.target], // path
        flashloanArbitrage.target, // to
        Math.floor(Date.now() / 1000) + 300 // deadline
      ]);

      // Encode sell swap data (WETH -> USDC)
      // Note: The amount will be updated by the contract based on actual received amount
      const sellSwapData = mockDEXRouterInterface.encodeFunctionData("swapExactTokensForTokens", [
        flashloanAmount, // amountIn (will be updated by contract)
        amountOutMin, // amountOutMin
        [mockWETH.target, mockUSDC.target], // path
        flashloanArbitrage.target, // to
        Math.floor(Date.now() / 1000) + 300 // deadline
      ]);

      // Encode arbitrage parameters
      const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'bytes', 'bytes'],
        [
          mockUSDC.target, // tokenA (flashloaned asset)
          mockWETH.target, // tokenB (intermediate token)
          mockDEXRouter.target, // buyRouter
          mockDEXRouter.target, // sellRouter
          buySwapData,
          sellSwapData
        ]
      );

      // Record initial balances
      const initialUSDCBalance = await flashloanArbitrage.getBalance(mockUSDC.target);
      const initialWETHBalance = await flashloanArbitrage.getBalance(mockWETH.target);

      console.log("Initial USDC balance:", ethers.formatUnits(initialUSDCBalance, 6));
      console.log("Initial WETH balance:", ethers.formatUnits(initialWETHBalance, 18));

      // Execute flashloan arbitrage
      await expect(
        flashloanArbitrage.executeFlashloanArbitrage(
          mockUSDC.target,
          flashloanAmount,
          arbitrageParams
        )
      ).to.not.be.reverted;

      // Check final balances
      const finalUSDCBalance = await flashloanArbitrage.getBalance(mockUSDC.target);
      const finalWETHBalance = await flashloanArbitrage.getBalance(mockWETH.target);

      console.log("Final USDC balance:", ethers.formatUnits(finalUSDCBalance, 6));
      console.log("Final WETH balance:", ethers.formatUnits(finalWETHBalance, 18));

      // Verify the arbitrage was profitable
      // With 1:1 mock swaps, we should have the same amount back
      // The contract started with 100 USDC, flashloaned 1000 USDC,
      // swapped 1000 USDC -> 1000 WETH -> 1000 USDC, repaid 1000 USDC
      // Final balance should be 100 USDC (no profit due to 1:1 mock swaps)
      expect(finalUSDCBalance).to.equal(ethers.parseUnits("100", 6));
      expect(finalWETHBalance).to.equal(0); // Should have no WETH left
    }).timeout(60000);

    it("Should fail if not called by owner", async function () {
      const flashloanAmount = ethers.parseUnits("1000", 6);
      const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'bytes', 'bytes'],
        [
          mockUSDC.target,
          mockWETH.target,
          mockDEXRouter.target,
          mockDEXRouter.target,
          "0x",
          "0x"
        ]
      );

      await expect(
        flashloanArbitrage.connect(addr1).executeFlashloanArbitrage(
          mockUSDC.target,
          flashloanAmount,
          arbitrageParams
        )
      ).to.be.revertedWithCustomError(flashloanArbitrage, "OwnableUnauthorizedAccount");
    });

    it("Should fail with asset mismatch", async function () {
      const flashloanAmount = ethers.parseUnits("1000", 6);
      const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'bytes', 'bytes'],
        [
          mockWETH.target, // Different from flashloan asset
          mockUSDC.target,
          mockDEXRouter.target,
          mockDEXRouter.target,
          "0x",
          "0x"
        ]
      );

      await expect(
        flashloanArbitrage.executeFlashloanArbitrage(
          mockUSDC.target, // Flashloan USDC but params say WETH
          flashloanAmount,
          arbitrageParams
        )
      ).to.be.revertedWith("Asset mismatch");
    });
  });

  describe("Profit Withdrawal", function () {
    it("Should allow owner to withdraw profits", async function () {
      // Add some profit to the contract
      await mockUSDC.mint(flashloanArbitrage.target, ethers.parseUnits("50", 6));
      
      const initialOwnerBalance = await mockUSDC.balanceOf(owner.address);
      const contractBalance = await flashloanArbitrage.getBalance(mockUSDC.target);
      
      await flashloanArbitrage.withdrawProfits(mockUSDC.target);
      
      const finalOwnerBalance = await mockUSDC.balanceOf(owner.address);
      const finalContractBalance = await flashloanArbitrage.getBalance(mockUSDC.target);
      
      expect(finalOwnerBalance).to.equal(initialOwnerBalance + contractBalance);
      expect(finalContractBalance).to.equal(0);
    });

    it("Should fail to withdraw profits if not owner", async function () {
      await expect(
        flashloanArbitrage.connect(addr1).withdrawProfits(mockUSDC.target)
      ).to.be.revertedWithCustomError(flashloanArbitrage, "OwnableUnauthorizedAccount");
    });

    it("Should fail to withdraw if no profits", async function () {
      // Withdraw all existing balance first
      await flashloanArbitrage.withdrawProfits(mockUSDC.target);
      
      await expect(
        flashloanArbitrage.withdrawProfits(mockUSDC.target)
      ).to.be.revertedWith("No profits to withdraw");
    });
  });

  describe("Emergency Functions", function () {
    it("Should allow emergency withdrawal by owner", async function () {
      const withdrawAmount = ethers.parseUnits("50", 6);
      const initialOwnerBalance = await mockUSDC.balanceOf(owner.address);
      
      await flashloanArbitrage.emergencyWithdraw(mockUSDC.target, withdrawAmount);
      
      const finalOwnerBalance = await mockUSDC.balanceOf(owner.address);
      expect(finalOwnerBalance).to.equal(initialOwnerBalance + withdrawAmount);
    });

    it("Should fail emergency withdrawal if not owner", async function () {
      await expect(
        flashloanArbitrage.connect(addr1).emergencyWithdraw(mockUSDC.target, ethers.parseUnits("50", 6))
      ).to.be.revertedWithCustomError(flashloanArbitrage, "OwnableUnauthorizedAccount");
    });
  });
});

// Additional test for the TypeScript strategy
describe("FlashloanStrategy Integration", function () {
  it("Should be able to import and instantiate FlashloanStrategy", async function () {
    expect(true).to.be.true;
  });

  it("Should calculate flashloan premium correctly", async function () {
    const amount = ethers.parseUnits("1000", 6); // 1000 USDC
    const expectedPremium = (amount * BigInt(9)) / BigInt(10000); // 0.09%
    expect(expectedPremium).to.equal(ethers.parseUnits("0.9", 6)); // 0.9 USDC
  });

  it("Should validate minimum profit thresholds", async function () {
    const minProfitThreshold = 0.02; // 2%
    expect(minProfitThreshold).to.be.greaterThan(0.01);
  });

  it("Should handle network-specific configurations", async function () {
    // Test that different networks have different configurations
    const mainnetChainId = 1;
    const sepoliaChainId = ********;
    
    expect(mainnetChainId).to.not.equal(sepoliaChainId);
    expect(sepoliaChainId).to.equal(********);
  });
});
