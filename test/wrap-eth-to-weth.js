#!/usr/bin/env node

/**
 * Wrap ETH to WETH on Sepolia
 * This script helps you convert ETH to WETH for flashloan arbitrage
 */

const { ethers } = require('ethers');
const chalk = require('chalk');

console.log(chalk.blue.bold('\n💱 ETH → WETH Wrapper for Sepolia\n'));

async function wrapEthToWeth() {
  try {
    // Load configuration
    require('dotenv').config();
    
    if (!process.env.PRIVATE_KEY) {
      console.log(chalk.red('❌ PRIVATE_KEY not found in .env file'));
      return;
    }
    
    // Connect to Sepolia
    const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);
    const wallet = new ethers.Wallet(process.env.PRIVATE_KEY, provider);
    
    console.log(`${chalk.cyan('Wallet Address:')} ${chalk.white(wallet.address)}`);
    
    // Check current balances
    const ethBalance = await provider.getBalance(wallet.address);
    const ethBalanceFormatted = ethers.formatEther(ethBalance);
    
    console.log(`${chalk.cyan('Current ETH Balance:')} ${chalk.white(ethBalanceFormatted)} ETH`);
    
    // Sepolia WETH contract
    const wethAddress = '******************************************';
    const wethAbi = [
      'function deposit() payable',
      'function withdraw(uint256) external',
      'function balanceOf(address) view returns (uint256)',
      'function symbol() view returns (string)',
      'function decimals() view returns (uint8)'
    ];
    
    const wethContract = new ethers.Contract(wethAddress, wethAbi, wallet);
    
    // Check current WETH balance
    const currentWethBalance = await wethContract.balanceOf(wallet.address);
    const currentWethFormatted = ethers.formatEther(currentWethBalance);
    
    console.log(`${chalk.cyan('Current WETH Balance:')} ${chalk.white(currentWethFormatted)} WETH`);
    
    // Determine how much to wrap
    const minEthToKeep = ethers.parseEther('0.1'); // Keep 0.1 ETH for gas
    const availableToWrap = ethBalance - minEthToKeep;
    
    if (availableToWrap <= 0) {
      console.log(chalk.red('\n❌ Insufficient ETH balance to wrap (need to keep 0.1 ETH for gas)'));
      return;
    }
    
    // Wrap a reasonable amount (0.1 ETH or half of available, whichever is smaller)
    const wrapAmount = availableToWrap > ethers.parseEther('0.2') 
      ? ethers.parseEther('0.1') 
      : availableToWrap / BigInt(2);
    
    const wrapAmountFormatted = ethers.formatEther(wrapAmount);
    
    console.log(chalk.yellow.bold('\n📋 Wrap Transaction Details:'));
    console.log(`${chalk.cyan('Amount to Wrap:')} ${chalk.white(wrapAmountFormatted)} ETH → WETH`);
    console.log(`${chalk.cyan('ETH After Wrap:')} ${chalk.white(ethers.formatEther(ethBalance - wrapAmount))} ETH`);
    console.log(`${chalk.cyan('WETH After Wrap:')} ${chalk.white(ethers.formatEther(currentWethBalance + wrapAmount))} WETH`);
    
    // Ask for confirmation
    console.log(chalk.yellow('\n⚠️  This will send a transaction to Sepolia network'));
    console.log(chalk.gray('Press Ctrl+C to cancel, or wait 5 seconds to proceed...'));
    
    // Wait 5 seconds
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    console.log(chalk.blue('\n🚀 Sending wrap transaction...'));
    
    // Send wrap transaction
    const tx = await wethContract.deposit({
      value: wrapAmount,
      gasLimit: 100000 // Conservative gas limit
    });
    
    console.log(`${chalk.cyan('Transaction Hash:')} ${chalk.blue(tx.hash)}`);
    console.log(chalk.yellow('⏳ Waiting for confirmation...'));
    
    // Wait for confirmation
    const receipt = await tx.wait();
    
    if (receipt.status === 1) {
      console.log(chalk.green.bold('\n✅ ETH Successfully Wrapped to WETH!'));
      
      // Check new balances
      const newEthBalance = await provider.getBalance(wallet.address);
      const newWethBalance = await wethContract.balanceOf(wallet.address);
      
      console.log(chalk.green('\n📊 Updated Balances:'));
      console.log(`${chalk.cyan('ETH Balance:')} ${chalk.white(ethers.formatEther(newEthBalance))} ETH`);
      console.log(`${chalk.cyan('WETH Balance:')} ${chalk.white(ethers.formatEther(newWethBalance))} WETH`);
      
      console.log(chalk.blue.bold('\n🎉 Ready for WETH-based Flashloan Arbitrage!'));
      console.log(chalk.gray('You can now run the MEV bot with WETH as primary token'));
      
    } else {
      console.log(chalk.red('\n❌ Transaction failed'));
    }
    
  } catch (error) {
    console.log(chalk.red(`\n❌ Error: ${error.message}`));
    
    if (error.message.includes('insufficient funds')) {
      console.log(chalk.yellow('\n💡 Suggestion: You need more ETH for gas fees'));
    } else if (error.message.includes('network')) {
      console.log(chalk.yellow('\n💡 Suggestion: Check your RPC_URL in .env file'));
    }
  }
}

// Show instructions if no arguments
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(chalk.blue('Usage: node wrap-eth-to-weth.js'));
  console.log(chalk.gray('This script will wrap some of your ETH to WETH on Sepolia'));
  console.log(chalk.gray('Make sure you have:'));
  console.log(chalk.gray('• PRIVATE_KEY in .env file'));
  console.log(chalk.gray('• RPC_URL pointing to Sepolia'));
  console.log(chalk.gray('• At least 0.2 ETH in your wallet'));
  process.exit(0);
}

// Run the wrapper
wrapEthToWeth().then(() => {
  console.log(chalk.blue.bold('\n🏁 Wrap Process Complete\n'));
}).catch(error => {
  console.log(chalk.red(`\nWrapper error: ${error.message}\n`));
});
