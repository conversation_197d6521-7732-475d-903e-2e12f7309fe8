// Test script to verify the V3 price calculation fix

/**
 * Test script to verify the V3 price calculation fix
 * This script tests the corrected price calculation for WETH/USDC pairs
 */

// Test data from the logs
const testCases = [
  {
    name: "USDC/WETH Pool (tick 198259)",
    tick: 198259,
    token0: { symbol: "USDC", decimals: 6 },
    token1: { symbol: "WETH", decimals: 18 },
    expectedPriceRange: { min: 0.0003, max: 0.001 } // WETH per USDC
  },
  {
    name: "USDC/WETH Pool (tick 198268)",
    tick: 198268,
    token0: { symbol: "USDC", decimals: 6 },
    token1: { symbol: "WETH", decimals: 18 },
    expectedPriceRange: { min: 0.0003, max: 0.001 }
  },
  {
    name: "USDC/WETH Pool (tick 198348)",
    tick: 198348,
    token0: { symbol: "USDC", decimals: 6 },
    token1: { symbol: "WETH", decimals: 18 },
    expectedPriceRange: { min: 0.0003, max: 0.001 }
  }
];

/**
 * Calculate V3 price using the corrected formula
 */
function calculateV3PriceFixed(tick, token0Decimals, token1Decimals) {
  // Calculate raw price from tick: price = 1.0001^tick
  const rawPrice = Math.pow(1.0001, tick);
  
  // Adjust for token decimals: divide by decimal adjustment
  const decimalsAdjustment = Math.pow(10, token1Decimals - token0Decimals);
  const adjustedPrice = rawPrice / decimalsAdjustment;
  
  return {
    rawPrice,
    decimalsAdjustment,
    adjustedPrice,
    calculation: `1.0001^${tick} / 10^(${token1Decimals} - ${token0Decimals}) = ${rawPrice} / ${decimalsAdjustment} = ${adjustedPrice}`
  };
}

/**
 * Calculate V3 price using the old (incorrect) formula
 */
function calculateV3PriceOld(tick, token0Decimals, token1Decimals) {
  // Calculate raw price from tick: price = 1.0001^tick
  const rawPrice = Math.pow(1.0001, tick);
  
  // Old incorrect formula: multiply by decimal adjustment
  const decimalsAdjustment = Math.pow(10, token1Decimals - token0Decimals);
  const adjustedPrice = rawPrice * decimalsAdjustment;
  
  return {
    rawPrice,
    decimalsAdjustment,
    adjustedPrice,
    calculation: `1.0001^${tick} * 10^(${token1Decimals} - ${token0Decimals}) = ${rawPrice} * ${decimalsAdjustment} = ${adjustedPrice}`
  };
}

/**
 * Convert price to human-readable format
 */
function formatPrice(price) {
  if (price > 1000000) {
    return `${(price / 1000000).toFixed(2)}M`;
  } else if (price > 1000) {
    return `${(price / 1000).toFixed(2)}K`;
  } else if (price < 0.001) {
    return price.toExponential(4);
  } else {
    return price.toFixed(6);
  }
}

/**
 * Check if price is within expected range
 */
function isPriceRealistic(price, expectedRange) {
  return price >= expectedRange.min && price <= expectedRange.max;
}

async function main() {
  console.log("🔧 Testing V3 Price Calculation Fix");
  console.log("=" .repeat(60));
  
  for (const testCase of testCases) {
    console.log(`\n📊 ${testCase.name}`);
    console.log("-".repeat(40));
    
    // Calculate using old (incorrect) method
    const oldResult = calculateV3PriceOld(
      testCase.tick,
      testCase.token0.decimals,
      testCase.token1.decimals
    );
    
    // Calculate using new (correct) method
    const newResult = calculateV3PriceFixed(
      testCase.tick,
      testCase.token0.decimals,
      testCase.token1.decimals
    );
    
    console.log(`Tick: ${testCase.tick}`);
    console.log(`Token0: ${testCase.token0.symbol} (${testCase.token0.decimals} decimals)`);
    console.log(`Token1: ${testCase.token1.symbol} (${testCase.token1.decimals} decimals)`);
    console.log(`Expected Range: ${testCase.expectedPriceRange.min} - ${testCase.expectedPriceRange.max}`);
    
    console.log(`\n❌ OLD (Incorrect) Calculation:`);
    console.log(`   Formula: ${oldResult.calculation}`);
    console.log(`   Result: ${formatPrice(oldResult.adjustedPrice)}`);
    console.log(`   Realistic: ${isPriceRealistic(oldResult.adjustedPrice, testCase.expectedPriceRange) ? '✅' : '❌'}`);
    
    console.log(`\n✅ NEW (Correct) Calculation:`);
    console.log(`   Formula: ${newResult.calculation}`);
    console.log(`   Result: ${formatPrice(newResult.adjustedPrice)}`);
    console.log(`   Realistic: ${isPriceRealistic(newResult.adjustedPrice, testCase.expectedPriceRange) ? '✅' : '❌'}`);
    
    // Calculate the difference
    const improvement = oldResult.adjustedPrice / newResult.adjustedPrice;
    console.log(`\n📈 Improvement: ${improvement.toExponential(2)}x reduction in price`);
  }
  
  console.log("\n" + "=".repeat(60));
  console.log("🎯 Summary:");
  console.log("- Fixed decimal adjustment: DIVIDE by 10^(decimals1-decimals0) instead of MULTIPLY");
  console.log("- For USDC(6)/WETH(18): divide by 10^12 instead of multiply by 10^12");
  console.log("- This brings prices from unrealistic billions to realistic decimals");
  console.log("- The fix aligns with Uniswap V3 documentation and expected market prices");
  
  console.log("\n🔗 Reference:");
  console.log("- Uniswap V3 Math Primer: https://blog.uniswap.org/uniswap-v3-math-primer");
  console.log("- Formula: price = 1.0001^tick / 10^(token1.decimals - token0.decimals)");
}

main().catch(console.error);
