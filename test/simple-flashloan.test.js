const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("Simple FlashloanArbitrage Test", function () {
  let flashloanArbitrage;
  let owner;
  let mockPoolAddressesProvider;
  let mockAavePool;
  let mockUSDC;
  let mockWETH;
  let mockDEXRouter;

  beforeEach(async function () {
    [owner] = await ethers.getSigners();

    // Deploy MockAavePool
    const MockAavePoolFactory = await ethers.getContractFactory("MockAavePool");
    mockAavePool = await MockAavePoolFactory.deploy();
    await mockAavePool.waitForDeployment();

    // Deploy MockPoolAddressesProvider
    const MockPoolAddressesProviderFactory = await ethers.getContractFactory("MockPoolAddressesProvider");
    mockPoolAddressesProvider = await MockPoolAddressesProviderFactory.deploy(mockAavePool.target);
    await mockPoolAddressesProvider.waitForDeployment();

    // Deploy MockERC20 tokens
    const MockERC20Factory = await ethers.getContractFactory("MockERC20");
    mockUSDC = await MockERC20Factory.deploy("Mock USDC", "mUSDC");
    await mockUSDC.waitForDeployment();
    mockWETH = await MockERC20Factory.deploy("Mock WETH", "mWETH");
    await mockWETH.waitForDeployment();

    // Deploy MockDEXRouter
    const MockDEXRouterFactory = await ethers.getContractFactory("MockDEXRouter");
    mockDEXRouter = await MockDEXRouterFactory.deploy();
    await mockDEXRouter.waitForDeployment();

    // Deploy FlashloanArbitrage contract
    const FlashloanArbitrageFactory = await ethers.getContractFactory("FlashloanArbitrage");
    flashloanArbitrage = await FlashloanArbitrageFactory.deploy(mockPoolAddressesProvider.target);
    await flashloanArbitrage.waitForDeployment();

    // Fund the MockAavePool with mock tokens
    await mockUSDC.mint(mockAavePool.target, ethers.parseUnits("10000000", 6)); // 10M mUSDC
    await mockWETH.mint(mockAavePool.target, ethers.parseUnits("10000", 18)); // 10k mWETH

    // Fund the MockDEXRouter with mock tokens (liquidity for swaps)
    await mockUSDC.mint(mockDEXRouter.target, ethers.parseUnits("10000000", 6)); // 10M mUSDC
    await mockWETH.mint(mockDEXRouter.target, ethers.parseUnits("10000", 18)); // 10k mWETH

    // Fund the FlashloanArbitrage contract with some initial tokens
    await mockUSDC.mint(flashloanArbitrage.target, ethers.parseUnits("100", 6)); // 100 mUSDC
  });

  describe("Working flashloan with proper swap data", function () {
    it("Should execute a flashloan and arbitrage with real swap functions", async function () {
      const flashloanAmount = ethers.parseUnits("1000", 6); // 1000 mUSDC
      const amountOutMin = ethers.parseUnits("990", 6); // 990 mUSDC (1% slippage)

      // Create interfaces for proper swap function encoding
      const mockDEXRouterInterface = new ethers.Interface([
        "function swapExactTokensForTokens(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts)"
      ]);

      // Encode buy swap data (USDC -> WETH)
      const buySwapData = mockDEXRouterInterface.encodeFunctionData("swapExactTokensForTokens", [
        flashloanAmount, // amountIn
        amountOutMin, // amountOutMin (will be ignored in mock, but good practice)
        [mockUSDC.target, mockWETH.target], // path
        flashloanArbitrage.target, // to
        Math.floor(Date.now() / 1000) + 300 // deadline
      ]);

      // Encode sell swap data (WETH -> USDC)
      // Note: The amount will be updated by the contract based on actual received amount
      const sellSwapData = mockDEXRouterInterface.encodeFunctionData("swapExactTokensForTokens", [
        flashloanAmount, // amountIn (will be updated by contract)
        amountOutMin, // amountOutMin
        [mockWETH.target, mockUSDC.target], // path
        flashloanArbitrage.target, // to
        Math.floor(Date.now() / 1000) + 300 // deadline
      ]);

      // Encode arbitrage parameters
      const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['address', 'address', 'address', 'address', 'bytes', 'bytes'],
        [
          mockUSDC.target, // tokenA (flashloaned asset)
          mockWETH.target, // tokenB (intermediate token)
          mockDEXRouter.target, // buyRouter
          mockDEXRouter.target, // sellRouter
          buySwapData,
          sellSwapData
        ]
      );

      // Record initial balances
      const initialUSDCBalance = await flashloanArbitrage.getBalance(mockUSDC.target);
      const initialWETHBalance = await flashloanArbitrage.getBalance(mockWETH.target);

      console.log("Initial USDC balance:", ethers.formatUnits(initialUSDCBalance, 6));
      console.log("Initial WETH balance:", ethers.formatUnits(initialWETHBalance, 18));

      // Execute flashloan arbitrage
      const tx = await flashloanArbitrage.executeFlashloanArbitrage(
        mockUSDC.target,
        flashloanAmount,
        arbitrageParams
      );

      await tx.wait();

      // Check final balances
      const finalUSDCBalance = await flashloanArbitrage.getBalance(mockUSDC.target);
      const finalWETHBalance = await flashloanArbitrage.getBalance(mockWETH.target);

      console.log("Final USDC balance:", ethers.formatUnits(finalUSDCBalance, 6));
      console.log("Final WETH balance:", ethers.formatUnits(finalWETHBalance, 18));

      // Verify the arbitrage completed successfully
      // With 1:1 mock swaps, we should have the same amount back
      // The contract started with 100 USDC, flashloaned 1000 USDC,
      // swapped 1000 USDC -> 1000 WETH -> 1000 USDC, repaid 1000 USDC
      // Final balance should be 100 USDC (no profit due to 1:1 mock swaps)
      expect(finalUSDCBalance).to.equal(ethers.parseUnits("100", 6));
      expect(finalWETHBalance).to.equal(0); // Should have no WETH left
    }).timeout(60000);

    it("Should handle basic contract functions", async function () {
      // Test basic functionality
      expect(await flashloanArbitrage.owner()).to.equal(owner.address);
      expect(await flashloanArbitrage.CHAIN_ID()).to.equal(31337);
      
      const usdcBalance = await flashloanArbitrage.getBalance(mockUSDC.target);
      expect(usdcBalance).to.equal(ethers.parseUnits("100", 6));
    });

    it("Should allow profit withdrawal", async function () {
      // Add some profit to the contract
      await mockUSDC.mint(flashloanArbitrage.target, ethers.parseUnits("50", 6));
      
      const initialOwnerBalance = await mockUSDC.balanceOf(owner.address);
      const contractBalance = await flashloanArbitrage.getBalance(mockUSDC.target);
      
      await flashloanArbitrage.withdrawProfits(mockUSDC.target);
      
      const finalOwnerBalance = await mockUSDC.balanceOf(owner.address);
      const finalContractBalance = await flashloanArbitrage.getBalance(mockUSDC.target);
      
      expect(finalOwnerBalance).to.equal(initialOwnerBalance + contractBalance);
      expect(finalContractBalance).to.equal(0);
    });
  });
});
