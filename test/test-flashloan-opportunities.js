#!/usr/bin/env node

/**
 * Diagnostic script to test flashloan opportunity detection
 */

const { ethers } = require('ethers');
const { config } = require('../dist/config');
const { PoolManager } = require('../dist/dex/pools');
const { FlashloanStrategy } = require('../dist/strategies/flashloan');
const { BalancerFlashloanStrategy } = require('../dist/strategies/balancer-flashloan');
const { logger } = require('../dist/utils/logger');

async function testFlashloanOpportunities() {
  console.log('🔍 Testing Flashloan Opportunity Detection\n');

  // Initialize components
  const provider = new ethers.JsonRpcProvider(config.rpcUrl);
  const poolManager = new PoolManager();
  const flashloanStrategy = new FlashloanStrategy(poolManager);
  const balancerStrategy = new BalancerFlashloanStrategy(poolManager);

  console.log('📋 Configuration:');
  console.log(`   Network: ${config.chainId === 11155111 ? 'Sepolia' : 'Mainnet'}`);
  console.log(`   DEX Pairs: ${config.flashloanDexPairs.join(', ')}`);
  console.log(`   Primary Token: ${config.flashloanPrimaryToken}`);
  console.log(`   Target Tokens: ${config.flashloanTargetTokens.join(', ')}`);
  console.log(`   Min Spread: ${config.minArbitrageSpread}%`);
  console.log(`   Cross-DEX: ${config.enableCrossDexArbitrage ? 'Enabled' : 'Disabled'}\n`);

  // Test 1: Check pool availability
  console.log('🏊 Testing Pool Availability:');
  
  const wethAddress = config.chainId === 11155111 
    ? '******************************************' // Sepolia WETH
    : '******************************************'; // Mainnet WETH
    
  const usdcAddress = config.chainId === 11155111
    ? '******************************************' // Sepolia USDC
    : '******************************************'; // Mainnet USDC

  try {
    // Test Uniswap V2 pool
    const v2Pool = await poolManager.getPool(wethAddress, usdcAddress, 'uniswap-v2');
    console.log(`   Uniswap V2 WETH/USDC: ${v2Pool ? '✅ Found' : '❌ Not found'}`);
    if (v2Pool) {
      console.log(`      Address: ${v2Pool.address}`);
      console.log(`      Reserves: ${v2Pool.reserves ? 'Available' : 'Missing'}`);
      if (v2Pool.reserves) {
        console.log(`      Reserve0: ${ethers.formatEther(v2Pool.reserves.reserve0)}`);
        console.log(`      Reserve1: ${ethers.formatUnits(v2Pool.reserves.reserve1, 6)}`);
      }
    }

    // Test Uniswap V3 pool
    const v3Pool = await poolManager.getPool(wethAddress, usdcAddress, 'uniswap-v3', 3000);
    console.log(`   Uniswap V3 WETH/USDC (0.3%): ${v3Pool ? '✅ Found' : '❌ Not found'}`);
    if (v3Pool) {
      console.log(`      Address: ${v3Pool.address}`);
      console.log(`      Liquidity: ${v3Pool.liquidity ? ethers.formatEther(v3Pool.liquidity) : 'Missing'}`);
      console.log(`      SqrtPriceX96: ${v3Pool.sqrtPriceX96 ? v3Pool.sqrtPriceX96.toString() : 'Missing'}`);
    }

    // Test Balancer pool
    const balancerPool = await poolManager.getPool(wethAddress, usdcAddress, 'balancer');
    console.log(`   Balancer WETH/USDC: ${balancerPool ? '✅ Found' : '❌ Not found'}`);
    if (balancerPool) {
      console.log(`      Address: ${balancerPool.address}`);
      console.log(`      Balances: ${balancerPool.balances ? 'Available' : 'Missing'}`);
    }

  } catch (error) {
    console.log(`   ❌ Error fetching pools: ${error.message}`);
  }

  console.log('\n💰 Testing Price Calculations:');
  
  try {
    // Test price calculations if pools exist
    const v2Pool = await poolManager.getPool(wethAddress, usdcAddress, 'uniswap-v2');
    const v3Pool = await poolManager.getPool(wethAddress, usdcAddress, 'uniswap-v3', 3000);

    if (v2Pool && v2Pool.reserves) {
      console.log(`   V2 Pool Token Order: ${v2Pool.token0.symbol}/${v2Pool.token1.symbol}`);
      console.log(`   V2 Reserve0 (${v2Pool.token0.symbol}): ${ethers.formatUnits(v2Pool.reserves.reserve0, v2Pool.token0.decimals)}`);
      console.log(`   V2 Reserve1 (${v2Pool.token1.symbol}): ${ethers.formatUnits(v2Pool.reserves.reserve1, v2Pool.token1.decimals)}`);

      const v2Price = calculateV2Price(v2Pool);
      console.log(`   Uniswap V2 Price: ${v2Price ? v2Price.toFixed(2) : 'N/A'} ${v2Pool.token1.symbol} per ${v2Pool.token0.symbol}`);
    }

    if (v3Pool && v3Pool.sqrtPriceX96) {
      console.log(`   V3 Pool Token Order: ${v3Pool.token0.symbol}/${v3Pool.token1.symbol}`);
      console.log(`   V3 Liquidity: ${ethers.formatEther(v3Pool.liquidity)}`);
      console.log(`   V3 SqrtPriceX96: ${v3Pool.sqrtPriceX96.toString()}`);

      const v3Price = calculateV3Price(v3Pool);
      console.log(`   Uniswap V3 Price: ${v3Price ? v3Price.toFixed(2) : 'N/A'} ${v3Pool.token1.symbol} per ${v3Pool.token0.symbol}`);
    }

    if (v2Pool && v3Pool && v2Pool.reserves && v3Pool.sqrtPriceX96) {
      const v2Price = calculateV2Price(v2Pool);
      const v3Price = calculateV3Price(v3Pool);
      
      if (v2Price && v3Price) {
        const priceDiff = Math.abs(v2Price - v3Price) / Math.min(v2Price, v3Price);
        console.log(`   Price Difference: ${(priceDiff * 100).toFixed(3)}%`);
        console.log(`   Min Required: ${config.minArbitrageSpread}%`);
        console.log(`   Profitable: ${priceDiff >= (config.minArbitrageSpread / 100) ? '✅ Yes' : '❌ No'}`);
      }
    }

  } catch (error) {
    console.log(`   ❌ Error calculating prices: ${error.message}`);
  }

  console.log('\n🔍 Testing Opportunity Scanning:');
  
  try {
    // Test flashloan opportunity scanning
    console.log('   Scanning for AAVE flashloan opportunities...');
    const aaveOpportunities = await flashloanStrategy.scanForFlashloanOpportunities();
    console.log(`   AAVE Opportunities: ${aaveOpportunities.length}`);

    console.log('   Scanning for Balancer flashloan opportunities...');
    const balancerOpportunities = await balancerStrategy.scanForBalancerFlashloanOpportunities();
    console.log(`   Balancer Opportunities: ${balancerOpportunities.length}`);

    const totalOpportunities = aaveOpportunities.length + balancerOpportunities.length;
    console.log(`   Total Opportunities: ${totalOpportunities}`);

    if (totalOpportunities > 0) {
      console.log('\n💎 Found Opportunities:');
      [...aaveOpportunities, ...balancerOpportunities].forEach((opp, i) => {
        console.log(`   ${i + 1}. Profit: ${ethers.formatEther(opp.expectedProfit)} ETH`);
        console.log(`      Confidence: ${opp.confidence}%`);
        console.log(`      Type: ${opp.flashloanProvider || 'Unknown'}`);
      });
    }

  } catch (error) {
    console.log(`   ❌ Error scanning opportunities: ${error.message}`);
    console.log(`   Stack: ${error.stack}`);
  }

  console.log('\n🎯 Recommendations:');
  
  if (config.chainId === 11155111) {
    console.log('   📍 Sepolia Testnet Issues:');
    console.log('   • Limited liquidity in testnet pools');
    console.log('   • Price differences may be minimal');
    console.log('   • Consider lowering MIN_ARBITRAGE_SPREAD to 0.05%');
    console.log('   • Test with smaller amounts (0.1 ETH)');
    console.log('   • Enable verbose logging to see filtering reasons');
  }

  console.log('\n✨ Diagnostic completed!');
}

function calculateV2Price(pool) {
  if (!pool.reserves) return null;

  // Use actual token decimals from pool
  const reserve0 = Number(ethers.formatUnits(pool.reserves.reserve0, pool.token0.decimals));
  const reserve1 = Number(ethers.formatUnits(pool.reserves.reserve1, pool.token1.decimals));

  return reserve1 / reserve0; // token1 per token0
}

function calculateV3Price(pool) {
  if (!pool.sqrtPriceX96) return null;

  const sqrtPrice = Number(pool.sqrtPriceX96) / (2 ** 96);
  const price = sqrtPrice ** 2;

  // Adjust for token decimals
  const decimalsAdjustment = 10 ** (pool.token1.decimals - pool.token0.decimals);
  return price * decimalsAdjustment;
}

// Run the test
testFlashloanOpportunities().catch(console.error);
