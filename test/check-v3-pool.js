#!/usr/bin/env node

const { ethers } = require('ethers');
require('dotenv').config();

const SEPOLIA_ADDRESSES = {
  WETH: '******************************************',
  USDC: '******************************************',
  UNISWAP_V3_FACTORY: '******************************************'
};

const FACTORY_V3_ABI = [
  'function getPool(address tokenA, address tokenB, uint24 fee) view returns (address pool)'
];

const POOL_V3_ABI = [
  'function slot0() view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)',
  'function token0() view returns (address)',
  'function token1() view returns (address)',
  'function fee() view returns (uint24)'
];

async function main() {
  console.log('🔍 Checking V3 Pool Token Ordering\n');
  
  const provider = new ethers.JsonRpcProvider(process.env.RPC_URL);
  const factory = new ethers.Contract(SEPOLIA_ADDRESSES.UNISWAP_V3_FACTORY, FACTORY_V3_ABI, provider);
  
  // Get V3 pool (0.3% fee)
  const poolAddress = await factory.getPool(SEPOLIA_ADDRESSES.WETH, SEPOLIA_ADDRESSES.USDC, 3000);
  console.log(`V3 Pool Address: ${poolAddress}`);
  
  if (poolAddress === ethers.ZeroAddress) {
    console.log('❌ No V3 pool exists');
    return;
  }
  
  const pool = new ethers.Contract(poolAddress, POOL_V3_ABI, provider);
  
  // Get pool data
  const [slot0, token0, token1, fee] = await Promise.all([
    pool.slot0(),
    pool.token0(),
    pool.token1(),
    pool.fee()
  ]);
  
  console.log(`📊 V3 Pool Data:`);
  console.log(`Token0: ${token0}`);
  console.log(`Token1: ${token1}`);
  console.log(`Fee: ${fee} (${Number(fee)/10000}%)`);
  console.log(`SqrtPriceX96: ${slot0.sqrtPriceX96.toString()}`);
  console.log(`Tick: ${slot0.tick.toString()}`);
  
  // Determine which token is which
  const isToken0WETH = token0.toLowerCase() === SEPOLIA_ADDRESSES.WETH.toLowerCase();
  const isToken0USDC = token0.toLowerCase() === SEPOLIA_ADDRESSES.USDC.toLowerCase();
  
  console.log(`\n🏷️  Token Identification:`);
  console.log(`Token0 is WETH: ${isToken0WETH}`);
  console.log(`Token0 is USDC: ${isToken0USDC}`);
  
  if (isToken0WETH) {
    console.log(`Token0 = WETH, Token1 = USDC`);
  } else if (isToken0USDC) {
    console.log(`Token0 = USDC, Token1 = WETH`);
  } else {
    console.log('❌ Unknown token configuration');
  }
  
  // Calculate price using sqrtPriceX96 (more reliable)
  const sqrtPriceX96 = BigInt(slot0.sqrtPriceX96.toString());
  const Q96 = BigInt(2) ** BigInt(96);

  console.log(`\n📈 Price Calculation (sqrtPriceX96):`);
  console.log(`SqrtPriceX96: ${sqrtPriceX96.toString()}`);

  // Calculate price = (sqrtPriceX96 / 2^96)^2
  // Use floating point for the calculation
  const sqrtPrice = Number(sqrtPriceX96) / Number(Q96);
  const rawPrice = sqrtPrice ** 2;

  console.log(`SqrtPrice: ${sqrtPrice}`);
  console.log(`Raw price (token1/token0): ${rawPrice}`);

  if (isToken0USDC) {
    // Token0 = USDC (6 decimals), Token1 = WETH (18 decimals)
    // rawPrice = WETH_wei / USDC_units
    // To get USDC per WETH:
    // 1 WETH (10^18 wei) costs how many USDC (10^6 units)?
    // USDC_units = WETH_wei / rawPrice
    // USDC = USDC_units / 10^6, WETH = WETH_wei / 10^18
    // USDC per WETH = (USDC_units / 10^6) / (WETH_wei / 10^18) = (USDC_units * 10^18) / (WETH_wei * 10^6)
    // But USDC_units = WETH_wei / rawPrice
    // So USDC per WETH = (WETH_wei / rawPrice * 10^18) / (WETH_wei * 10^6) = 10^12 / rawPrice

    const usdcPerWeth = (10 ** 12) / rawPrice;

    console.log(`USDC per WETH: ${usdcPerWeth.toFixed(2)}`);
  } else if (isToken0WETH) {
    // Token0 = WETH (18 decimals), Token1 = USDC (6 decimals)
    // rawPrice = USDC_units / WETH_wei
    const usdcPerWeth = rawPrice * (10 ** 12);

    console.log(`USDC per WETH: ${usdcPerWeth.toFixed(2)}`);
  }

  // Also try tick calculation for comparison
  const tickNumber = Number(slot0.tick);
  const tickPrice = 1.0001 ** tickNumber;

  console.log(`\n📈 Price Calculation (tick):`);
  console.log(`Tick: ${tickNumber}`);
  console.log(`Tick price: ${tickPrice}`);

  if (isToken0USDC) {
    const usdcPerWethFromTick = (10 ** 12) / tickPrice;
    console.log(`USDC per WETH (from tick): ${usdcPerWethFromTick.toFixed(2)}`);
  }
  
  console.log(`\n📊 Expected V2 price: ~471.65 USDC/WETH`);
}

main().catch(console.error);
