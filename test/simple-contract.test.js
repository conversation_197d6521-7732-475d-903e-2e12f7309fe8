const { expect } = require("chai");
const { ethers } = require("hardhat");

describe("SimpleFlashloanArbitrage Test", function () {
  let simpleFlashloanArbitrage;
  let owner;
  let mockPoolAddressesProvider;
  let mockAavePool;
  let mockUSDC;

  beforeEach(async function () {
    [owner] = await ethers.getSigners();

    // Deploy MockAavePool
    const MockAavePoolFactory = await ethers.getContractFactory("MockAavePool");
    mockAavePool = await MockAavePoolFactory.deploy();
    await mockAavePool.waitForDeployment();

    // Deploy MockPoolAddressesProvider
    const MockPoolAddressesProviderFactory = await ethers.getContractFactory("MockPoolAddressesProvider");
    mockPoolAddressesProvider = await MockPoolAddressesProviderFactory.deploy(mockAavePool.target);
    await mockPoolAddressesProvider.waitForDeployment();

    // Deploy MockERC20 tokens
    const MockERC20Factory = await ethers.getContractFactory("MockERC20");
    mockUSDC = await MockERC20Factory.deploy("Mock USDC", "mUSDC");
    await mockUSDC.waitForDeployment();

    // Deploy SimpleFlashloanArbitrage contract
    const SimpleFlashloanArbitrageFactory = await ethers.getContractFactory("SimpleFlashloanArbitrage");
    simpleFlashloanArbitrage = await SimpleFlashloanArbitrageFactory.deploy(mockPoolAddressesProvider.target);
    await simpleFlashloanArbitrage.waitForDeployment();

    // Fund the MockAavePool with mock tokens
    await mockUSDC.mint(mockAavePool.target, ethers.parseUnits("10000000", 6)); // 10M mUSDC

    // Fund the SimpleFlashloanArbitrage contract with enough tokens to repay flashloan
    await mockUSDC.mint(simpleFlashloanArbitrage.target, ethers.parseUnits("2000", 6)); // 2000 mUSDC
  });

  describe("Basic flashloan functionality", function () {
    it("Should execute a simple flashloan successfully", async function () {
      const flashloanAmount = ethers.parseUnits("1000", 6); // 1000 mUSDC

      // Record initial balances
      const initialUSDCBalance = await simpleFlashloanArbitrage.getBalance(mockUSDC.target);
      console.log("Initial USDC balance:", ethers.formatUnits(initialUSDCBalance, 6));

      // Execute flashloan arbitrage
      const tx = await simpleFlashloanArbitrage.executeFlashloanArbitrage(
        mockUSDC.target,
        flashloanAmount
      );

      await tx.wait();

      // Check final balances
      const finalUSDCBalance = await simpleFlashloanArbitrage.getBalance(mockUSDC.target);
      console.log("Final USDC balance:", ethers.formatUnits(finalUSDCBalance, 6));

      // With no premium and no swaps, the contract should have the same balance
      // Started with 2000, flashloaned 1000, repaid 1000, should have 2000 left
      expect(finalUSDCBalance).to.equal(ethers.parseUnits("2000", 6));
    });

    it("Should handle basic contract functions", async function () {
      // Test basic functionality
      expect(await simpleFlashloanArbitrage.owner()).to.equal(owner.address);
      expect(await simpleFlashloanArbitrage.CHAIN_ID()).to.equal(31337);
      
      const usdcBalance = await simpleFlashloanArbitrage.getBalance(mockUSDC.target);
      expect(usdcBalance).to.equal(ethers.parseUnits("2000", 6));
    });

    it("Should allow profit withdrawal", async function () {
      // Add some profit to the contract
      await mockUSDC.mint(simpleFlashloanArbitrage.target, ethers.parseUnits("50", 6));
      
      const initialOwnerBalance = await mockUSDC.balanceOf(owner.address);
      const contractBalance = await simpleFlashloanArbitrage.getBalance(mockUSDC.target);
      
      await simpleFlashloanArbitrage.withdrawProfits(mockUSDC.target);
      
      const finalOwnerBalance = await mockUSDC.balanceOf(owner.address);
      const finalContractBalance = await simpleFlashloanArbitrage.getBalance(mockUSDC.target);
      
      expect(finalOwnerBalance).to.equal(initialOwnerBalance + contractBalance);
      expect(finalContractBalance).to.equal(0);
    });
  });
});
