#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting MEV Bot with Split-Screen Dashboard Demo\n');

console.log('This demo will show:');
console.log('├── Left side: Live status dashboard with bot statistics');
console.log('├── Right side: Real-time logs from the MEV bot');
console.log('└── Interactive controls: Press "q" to quit, "r" to refresh\n');

console.log('Setting up environment...');

// Set environment variable for split screen
process.env.SPLIT_SCREEN_DASHBOARD = 'true';
process.env.NODE_ENV = 'development';

console.log('✅ Split-screen dashboard enabled');
console.log('✅ Development mode activated');

console.log('\nStarting MEV Bot in 3 seconds...');
console.log('Press Ctrl+C to stop the demo at any time.\n');

setTimeout(() => {
  console.log('🎬 Launching split-screen dashboard...\n');
  
  // Start the MEV bot with split screen dashboard
  const mevBot = spawn('npm', ['run', 'dev'], {
    stdio: 'inherit',
    env: {
      ...process.env,
      SPLIT_SCREEN_DASHBOARD: 'true',
      NODE_ENV: 'development'
    }
  });

  mevBot.on('close', (code) => {
    console.log(`\n👋 MEV Bot exited with code ${code}`);
    console.log('Split-screen dashboard demo completed!');
    process.exit(code);
  });

  mevBot.on('error', (error) => {
    console.error('❌ Error starting MEV Bot:', error.message);
    process.exit(1);
  });

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Stopping MEV Bot...');
    mevBot.kill('SIGINT');
  });

}, 3000);
