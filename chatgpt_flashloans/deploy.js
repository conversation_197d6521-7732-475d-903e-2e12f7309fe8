const hre = require("hardhat");

async function main() {
    const [deployer] = await hre.ethers.getSigners();

    const USDC = "******************************************";
    const WETH = "******************************************";
    const UNISWAP_ROUTER = "******************************************";
    const SUSHISWAP_ROUTER = "******************************************";
    const AAVE_POOL_PROVIDER = "******************************************"; // Aave Sepolia

    const FlashloanArb = await hre.ethers.getContractFactory("FlashloanArbitrage");
    const arb = await FlashloanArb.deploy(AAVE_POOL_PROVIDER, UNISWAP_ROUTER, SUSHISWAP_ROUTER);

    await arb.deployed();
    console.log("FlashloanArbitrage deployed to:", arb.address);
}

main().catch((error) => {
    console.error(error);
    process.exitCode = 1;
});
