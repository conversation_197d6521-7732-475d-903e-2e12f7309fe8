// SPDX-License-Identifier: MIT
pragma solidity ^0.8.20;

import "@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol";
import "@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol";
import "@aave/core-v3/contracts/interfaces/IPool.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";

interface ISwapRouter {
    function exactInputSingle(
        bytes calldata params
    ) external payable returns (uint256 amountOut);
}

contract FlashloanArbitrage is FlashLoanSimpleReceiverBase {
    address public owner;
    address public immutable uniswapRouter;
    address public immutable sushiswapRouter;
    address public immutable USDC;
    address public immutable WETH;

    constructor(
        address _provider,
        address _uniRouter,
        address _sushiRouter,
        address _usdc,
        address _weth
    ) FlashLoanSimpleReceiverBase(IPoolAddressesProvider(_provider)) {
        owner = msg.sender;
        uniswapRouter = _uniRouter;
        sushiswapRouter = _sushiRouter;
        USDC = _usdc;
        WETH = _weth;
    }

    modifier onlyOwner() {
        require(msg.sender == owner, "Not owner");
        _;
    }

    function executeOperation(
        address asset,
        uint256 amount,
        uint256 premium,
        address initiator,
        bytes calldata
    ) external override returns (bool) {
        // Approve asset (USDC) to Uniswap
        IERC20(asset).approve(uniswapRouter, amount);

        // 1. Swap USDC -> WETH on Uniswap
        ISwapRouter(uniswapRouter).exactInputSingle(
            abi.encodePacked(
                bytes.concat(
                    abi.encodePacked(asset),
                    abi.encodePacked(uint24(3000)),
                    abi.encodePacked(WETH)
                )
            )
        );

        // 2. Get WETH balance
        uint256 wethBalance = IERC20(WETH).balanceOf(address(this));

        // 3. Approve WETH to SushiSwap
        IERC20(WETH).approve(sushiswapRouter, wethBalance);

        // 4. Swap WETH -> USDC on SushiSwap
        ISwapRouter(sushiswapRouter).exactInputSingle(
            abi.encodePacked(
                bytes.concat(
                    abi.encodePacked(WETH),
                    abi.encodePacked(uint24(3000)),
                    abi.encodePacked(asset)
                )
            )
        );

        // 5. Repay Flashloan
        uint256 totalOwed = amount + premium;
        IERC20(asset).approve(address(POOL), totalOwed);

        return true;
    }

    function startArbitrage(address token, uint256 amount) external onlyOwner {
        POOL.flashLoanSimple(address(this), token, amount, "", 0);
    }
}
