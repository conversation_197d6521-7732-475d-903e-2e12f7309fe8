const { ethers } = require("hardhat");

async function main() {
  console.log("🔄 Generating Test Transactions for MEV Bot...\n");

  // Get signers
  const [deployer, account1, account2, account3, account4] = await ethers.getSigners();
  const accounts = [deployer, account1, account2, account3, account4];
  
  console.log("👥 Using accounts:");
  for (let i = 0; i < accounts.length; i++) {
    const balance = await ethers.provider.getBalance(accounts[i].address);
    console.log(`   Account ${i}: ${accounts[i].address} (${ethers.formatEther(balance)} ETH)`);
  }

  // Token addresses (Sepolia)
  const tokens = {
    USDC: "******************************************",
    DAI: "******************************************",
    WETH: "******************************************"
  };

  // Uniswap V2 Router (Sepolia)
  const uniswapV2Router = "******************************************";

  console.log("\n🎯 Starting continuous transaction generation...");
  console.log("Press Ctrl+C to stop\n");

  let transactionCount = 0;
  const startTime = Date.now();

  // Function to generate random transactions
  async function generateTransaction() {
    try {
      const txType = Math.floor(Math.random() * 4);
      const fromAccount = accounts[Math.floor(Math.random() * accounts.length)];
      const toAccount = accounts[Math.floor(Math.random() * accounts.length)];

      if (fromAccount.address === toAccount.address) return;

      let tx;
      const gasPrice = ethers.parseUnits((10 + Math.random() * 40).toFixed(0), "gwei"); // 10-50 gwei

      switch (txType) {
        case 0: // Simple ETH transfer
          const ethAmount = ethers.parseEther((0.01 + Math.random() * 0.1).toFixed(4));
          tx = await fromAccount.sendTransaction({
            to: toAccount.address,
            value: ethAmount,
            gasPrice: gasPrice
          });
          console.log(`💸 ETH Transfer: ${ethers.formatEther(ethAmount)} ETH | ${tx.hash.slice(0, 10)}...`);
          break;

        case 1: // Token transfer (if account has tokens)
          try {
            const tokenAddress = Object.values(tokens)[Math.floor(Math.random() * Object.keys(tokens).length)];
            const token = await ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", tokenAddress);
            
            const balance = await token.balanceOf(fromAccount.address);
            if (balance > 0) {
              const transferAmount = balance / 10n; // Transfer 10% of balance
              if (transferAmount > 0) {
                tx = await token.connect(fromAccount).transfer(toAccount.address, transferAmount, {
                  gasPrice: gasPrice
                });
                console.log(`🪙 Token Transfer: ${transferAmount.toString()} tokens | ${tx.hash.slice(0, 10)}...`);
              }
            }
          } catch (error) {
            // Fallback to ETH transfer if token transfer fails
            const ethAmount = ethers.parseEther("0.01");
            tx = await fromAccount.sendTransaction({
              to: toAccount.address,
              value: ethAmount,
              gasPrice: gasPrice
            });
            console.log(`💸 ETH Transfer (fallback): 0.01 ETH | ${tx.hash.slice(0, 10)}...`);
          }
          break;

        case 2: // Contract interaction (approve tokens)
          try {
            const tokenAddress = Object.values(tokens)[Math.floor(Math.random() * Object.keys(tokens).length)];
            const token = await ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", tokenAddress);
            
            const approveAmount = ethers.parseUnits((100 + Math.random() * 1000).toFixed(0), 6);
            tx = await token.connect(fromAccount).approve(uniswapV2Router, approveAmount, {
              gasPrice: gasPrice
            });
            console.log(`✅ Token Approval: ${approveAmount.toString()} | ${tx.hash.slice(0, 10)}...`);
          } catch (error) {
            // Fallback to ETH transfer
            const ethAmount = ethers.parseEther("0.005");
            tx = await fromAccount.sendTransaction({
              to: toAccount.address,
              value: ethAmount,
              gasPrice: gasPrice
            });
            console.log(`💸 ETH Transfer (fallback): 0.005 ETH | ${tx.hash.slice(0, 10)}...`);
          }
          break;

        case 3: // Large transaction (potential MEV target)
          const largeAmount = ethers.parseEther((1 + Math.random() * 5).toFixed(4));
          const highGasPrice = ethers.parseUnits((50 + Math.random() * 100).toFixed(0), "gwei");
          tx = await fromAccount.sendTransaction({
            to: toAccount.address,
            value: largeAmount,
            gasPrice: highGasPrice
          });
          console.log(`🎯 Large TX: ${ethers.formatEther(largeAmount)} ETH (${ethers.formatUnits(highGasPrice, "gwei")} gwei) | ${tx.hash.slice(0, 10)}...`);
          break;
      }

      if (tx) {
        transactionCount++;
        
        // Display stats every 10 transactions
        if (transactionCount % 10 === 0) {
          const elapsed = (Date.now() - startTime) / 1000;
          const rate = (transactionCount / elapsed * 60).toFixed(1);
          console.log(`\n📊 Stats: ${transactionCount} transactions, ${rate} tx/min\n`);
        }
      }

    } catch (error) {
      console.log(`❌ Transaction failed: ${error.message.slice(0, 50)}...`);
    }
  }

  // Generate transactions continuously
  const interval = setInterval(async () => {
    await generateTransaction();
  }, 2000 + Math.random() * 3000); // Random interval between 2-5 seconds

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Stopping transaction generation...');
    clearInterval(interval);
    
    const elapsed = (Date.now() - startTime) / 1000;
    const rate = (transactionCount / elapsed * 60).toFixed(1);
    
    console.log(`\n📊 Final Stats:`);
    console.log(`   Total Transactions: ${transactionCount}`);
    console.log(`   Runtime: ${elapsed.toFixed(1)}s`);
    console.log(`   Average Rate: ${rate} tx/min`);
    console.log('\n✅ Transaction generation stopped');
    process.exit(0);
  });

  // Keep the script running
  console.log("🔄 Transaction generation started...");
  console.log("   Generating transactions every 2-5 seconds");
  console.log("   Mix of ETH transfers, token transfers, approvals, and large transactions");
  console.log("   Press Ctrl+C to stop");
}

main().catch((error) => {
  console.error("❌ Error:", error);
  process.exit(1);
});
