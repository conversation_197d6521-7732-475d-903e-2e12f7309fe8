const { ethers } = require("hardhat");
const { config } = require("../src/config");

/**
 * Deploy Dynamic Flashloan Arbitrage Contract
 * Supports Aave, Balancer, and Uniswap V3 flashloans with dynamic provider selection
 */
async function main() {
  console.log("🚀 Deploying Dynamic Flashloan Arbitrage Contract...");
  console.log("=" .repeat(60));

  const [deployer] = await ethers.getSigners();
  console.log("📝 Deploying with account:", deployer.address);
  console.log("💰 Account balance:", ethers.formatEther(await deployer.provider.getBalance(deployer.address)), "ETH");

  // Network-specific addresses
  const networkAddresses = getNetworkAddresses(config.chainId);
  console.log("🌐 Network:", getNetworkName(config.chainId));
  console.log("📍 Addresses:", networkAddresses);

  // Estimate deployment cost
  const DynamicFlashloanArbitrage = await ethers.getContractFactory("DynamicFlashloanArbitrage");
  const deploymentData = DynamicFlashloanArbitrage.interface.encodeDeploy([
    networkAddresses.aaveAddressProvider,
    networkAddresses.balancerVault,
    networkAddresses.uniswapV3Router,
    networkAddresses.uniswapV2Router,
    networkAddresses.uniswapV3Factory
  ]);

  const estimatedGas = await deployer.estimateGas({
    data: deploymentData
  });

  const gasPrice = await deployer.provider.getFeeData();
  const estimatedCost = estimatedGas * gasPrice.gasPrice;
  const estimatedCostEth = ethers.formatEther(estimatedCost);

  console.log("⛽ Estimated gas:", estimatedGas.toString());
  console.log("💸 Estimated cost:", estimatedCostEth, "ETH");

  // Check if cost exceeds limit
  const maxCostEth = 0.05; // 0.05 ETH limit
  if (parseFloat(estimatedCostEth) > maxCostEth) {
    console.log(`❌ Deployment cost (${estimatedCostEth} ETH) exceeds limit (${maxCostEth} ETH)`);
    console.log("🛑 Deployment cancelled to prevent high costs");
    return;
  }

  console.log("✅ Cost within acceptable range, proceeding with deployment...");
  console.log("-".repeat(60));

  try {
    // Deploy the contract
    const dynamicFlashloan = await DynamicFlashloanArbitrage.deploy(
      networkAddresses.aaveAddressProvider,
      networkAddresses.balancerVault,
      networkAddresses.uniswapV3Router,
      networkAddresses.uniswapV2Router,
      networkAddresses.uniswapV3Factory
    );

    console.log("⏳ Waiting for deployment transaction...");
    await dynamicFlashloan.waitForDeployment();

    const contractAddress = await dynamicFlashloan.getAddress();
    console.log("✅ Dynamic Flashloan Arbitrage deployed to:", contractAddress);

    // Verify deployment
    console.log("🔍 Verifying deployment...");
    const chainId = await dynamicFlashloan.CHAIN_ID();
    const balancerVault = await dynamicFlashloan.BALANCER_VAULT();
    const uniswapV3Router = await dynamicFlashloan.UNISWAP_V3_ROUTER();

    console.log("📋 Contract verification:");
    console.log("   Chain ID:", chainId.toString());
    console.log("   Balancer Vault:", balancerVault);
    console.log("   Uniswap V3 Router:", uniswapV3Router);

    // Update environment variable
    console.log("🔧 Updating environment configuration...");
    console.log(`   Add to .env: DYNAMIC_FLASHLOAN_CONTRACT=${contractAddress}`);

    // Display usage instructions
    console.log("=" .repeat(60));
    console.log("🎯 DEPLOYMENT SUCCESSFUL!");
    console.log("=" .repeat(60));
    console.log("📝 Contract Address:", contractAddress);
    console.log("🔗 Network:", getNetworkName(config.chainId));
    console.log("💰 Deployment Cost:", estimatedCostEth, "ETH");
    console.log("");
    console.log("🚀 Next Steps:");
    console.log("1. Add to .env file:");
    console.log(`   DYNAMIC_FLASHLOAN_CONTRACT=${contractAddress}`);
    console.log("2. Fund the contract with initial ETH for gas:");
    console.log(`   npx hardhat run scripts/fund-contract.js --network ${getNetworkName(config.chainId).toLowerCase()}`);
    console.log("3. Test the contract:");
    console.log(`   npm run test:dynamic-flashloan`);
    console.log("4. Start the bot with dynamic strategy enabled:");
    console.log(`   ENABLE_FLASHLOAN_ATTACKS=true npm run dev`);
    console.log("");
    console.log("⚡ Features enabled:");
    console.log("   ✅ Aave V3 Flashloans (0.09% fee)");
    console.log("   ✅ Balancer V2 Flashloans (0% fee)");
    console.log("   ✅ Uniswap V3 Flash Swaps (dynamic fees)");
    console.log("   ✅ Dynamic provider selection");
    console.log("   ✅ MEV protection via Flashbots");
    console.log("   ✅ Gas optimization");
    console.log("   ✅ Profit maximization");

  } catch (error) {
    console.error("❌ Deployment failed:", error.message);
    
    if (error.message.includes("insufficient funds")) {
      console.log("💡 Solution: Add more ETH to deployer account");
      console.log(`   Current balance: ${ethers.formatEther(await deployer.provider.getBalance(deployer.address))} ETH`);
      console.log(`   Required: ~${estimatedCostEth} ETH`);
    }
    
    process.exit(1);
  }
}

/**
 * Get network-specific contract addresses
 */
function getNetworkAddresses(chainId) {
  switch (chainId) {
    case 1: // Ethereum Mainnet
      return {
        aaveAddressProvider: "******************************************",
        balancerVault: "******************************************",
        uniswapV3Router: "******************************************",
        uniswapV2Router: "******************************************",
        uniswapV3Factory: "******************************************"
      };
    case ********: // Sepolia Testnet
      return {
        aaveAddressProvider: "******************************************",
        balancerVault: "******************************************",
        uniswapV3Router: "******************************************",
        uniswapV2Router: "******************************************",
        uniswapV3Factory: "******************************************"
      };
    case 31337: // Hardhat Local
      return {
        aaveAddressProvider: "******************************************", // Fork mainnet
        balancerVault: "******************************************",
        uniswapV3Router: "******************************************",
        uniswapV2Router: "******************************************",
        uniswapV3Factory: "******************************************"
      };
    default:
      throw new Error(`Unsupported network: ${chainId}`);
  }
}

/**
 * Get network name from chain ID
 */
function getNetworkName(chainId) {
  switch (chainId) {
    case 1: return "Mainnet";
    case ********: return "Sepolia";
    case 31337: return "Hardhat";
    default: return `Unknown (${chainId})`;
  }
}

// Execute deployment
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("💥 Deployment script failed:", error);
    process.exit(1);
  });
