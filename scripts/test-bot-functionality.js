#!/usr/bin/env node

/**
 * Test bot functionality with workers
 */

// Disable dashboard to avoid interference
process.env.SPLIT_SCREEN_DASHBOARD = 'false';
process.env.DISABLE_DASHBOARD = 'true';

const { MEVBot } = require('../dist/core/bot');

async function testBotFunctionality() {
  console.log('🤖 Testing MEV Bot Functionality\n');

  let bot;

  try {
    // Initialize bot
    console.log('⏳ Initializing MEV Bot...');
    bot = new MEVBot();
    
    // Start bot
    console.log('🚀 Starting bot...');
    await bot.start();
    
    console.log('✅ Bot started successfully');
    
    // Wait a bit for initialization
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Check bot state
    const state = bot.getState();
    console.log('\n📊 Bot State:');
    console.log(`   Running: ${state.isRunning}`);
    console.log(`   Successful Trades: ${state.successfulTrades}`);
    console.log(`   Failed Trades: ${state.failedTrades}`);
    console.log(`   Total Profit: ${state.totalProfit.toString()}`);
    
    // Test arbitrage strategy directly
    console.log('\n🔍 Testing arbitrage strategy...');
    const arbitrageStrategy = bot.arbitrageStrategy;
    
    if (arbitrageStrategy) {
      console.log(`   Workers enabled: ${arbitrageStrategy.isUsingWorkers()}`);
      
      if (arbitrageStrategy.isUsingWorkers()) {
        const workerStats = arbitrageStrategy.getWorkerStats();
        console.log(`   Worker count: ${workerStats.length}`);
        console.log(`   Active workers: ${workerStats.filter(w => w.isActive).length}`);
      }
      
      // Run a single arbitrage scan
      const start = Date.now();
      const opportunities = await arbitrageStrategy.scanForArbitrageOpportunities();
      const duration = Date.now() - start;
      
      console.log(`   Scan completed in ${duration}ms`);
      console.log(`   Opportunities found: ${opportunities.length}`);
      
      if (arbitrageStrategy.isUsingWorkers()) {
        const finalStats = arbitrageStrategy.getWorkerStats();
        const totalTasks = finalStats.reduce((sum, w) => sum + w.tasksProcessed, 0);
        console.log(`   Total worker tasks: ${totalTasks}`);
        console.log(`   Active workers: ${finalStats.filter(w => w.tasksProcessed > 0).length}/${finalStats.length}`);
      }
    }
    
    console.log('\n✅ Bot functionality test completed successfully!');

  } catch (error) {
    console.error('❌ Bot test failed:', error.message);
    console.error(error.stack);
  } finally {
    // Cleanup
    if (bot) {
      console.log('\n🧹 Stopping bot...');
      try {
        await bot.stop();
        console.log('✅ Bot stopped successfully');
      } catch (cleanupError) {
        console.error('⚠️  Bot stop error:', cleanupError.message);
      }
    }
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Test interrupted');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Test terminated');
  process.exit(0);
});

// Run the test
if (require.main === module) {
  testBotFunctionality().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { testBotFunctionality };
