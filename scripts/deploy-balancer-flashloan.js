const { ethers } = require("hardhat");

async function main() {
  console.log("🔵 Deploying BalancerFlashloanArbitrage contract...");
  console.log("💰 Using Official Balancer V2 Flashloan API - 0% FEES!");

  // Network detection
  const network = await ethers.provider.getNetwork();
  const chainId = Number(network.chainId);
  
  let networkName;
  if (chainId === 1) {
    networkName = "Mainnet";
  } else if (chainId === ********) {
    networkName = "Sepolia";
  } else {
    console.error(`❌ Unsupported network: Chain ID ${chainId}`);
    process.exit(1);
  }

  console.log(`📋 Deployment Details:`);
  console.log(`Network: ${networkName} (Chain ID: ${chainId})`);
  console.log(`Balancer Vault: ****************************************** (universal)`);

  // Get deployer account
  const [deployer] = await ethers.getSigners();
  const deployerBalance = await ethers.provider.getBalance(deployer.address);
  
  console.log(`Deployer: ${deployer.address}`);
  console.log(`Balance: ${ethers.formatEther(deployerBalance)} ETH`);

  // Check minimum balance
  const minBalance = ethers.parseEther(chainId === 1 ? "0.05" : "0.01");
  if (deployerBalance < minBalance) {
    console.error(`❌ Insufficient balance. Need at least ${ethers.formatEther(minBalance)} ETH.`);
    process.exit(1);
  }

  // Get the contract factory
  const BalancerFlashloanArbitrage = await ethers.getContractFactory("BalancerFlashloanArbitrage");

  // Deploy the contract
  console.log("📝 Deploying contract...");
  const balancerFlashloan = await BalancerFlashloanArbitrage.deploy();

  console.log("⏳ Waiting for deployment confirmation...");
  await balancerFlashloan.waitForDeployment();

  const contractAddress = await balancerFlashloan.getAddress();
  console.log("✅ BalancerFlashloanArbitrage deployed successfully!");

  // Verify deployment
  console.log("🔍 Verifying deployment...");
  const owner = await balancerFlashloan.owner();
  const chainIdContract = await balancerFlashloan.CHAIN_ID();
  const v2Router = await balancerFlashloan.UNISWAP_V2_ROUTER();
  const v3Router = await balancerFlashloan.UNISWAP_V3_ROUTER();
  const vaultAddress = await balancerFlashloan.getVault();

  console.log("\n📊 Contract Verification:");
  console.log("=".repeat(50));
  console.log(`Contract Address: ${contractAddress}`);
  console.log(`Owner: ${owner}`);
  console.log(`Chain ID: ${chainIdContract}`);
  console.log(`Balancer Vault: ${vaultAddress}`);
  console.log(`Uniswap V2 Router: ${v2Router}`);
  console.log(`Uniswap V3 Router: ${v3Router}`);
  console.log("=".repeat(50));

  // Save deployment info
  const deploymentInfo = {
    network: networkName.toLowerCase(),
    chainId: chainId,
    contractAddress: contractAddress,
    owner: owner,
    balancerVault: vaultAddress,
    deployedAt: new Date().toISOString(),
    routers: {
      uniswapV2: v2Router,
      uniswapV3: v3Router
    },
    features: {
      flashloanFees: "0%",
      provider: "Balancer V2",
      officialAPI: true
    }
  };

  // Write deployment info to file
  const fs = require('fs');
  const filename = `deployment-balancer-${networkName.toLowerCase()}.json`;
  fs.writeFileSync(filename, JSON.stringify(deploymentInfo, null, 2));

  console.log("\n🎉 BALANCER FLASHLOAN DEPLOYMENT COMPLETED!");
  console.log("=".repeat(50));
  console.log("🔧 NEXT STEPS:");
  console.log("1. Update your .env file:");
  console.log(`   BALANCER_FLASHLOAN_CONTRACT=${contractAddress}`);
  console.log("2. Fund the contract with ETH for gas fees");
  console.log("3. Test with DRY_RUN=true first");
  console.log("4. Enable flashloan attacks:");
  console.log("   ENABLE_FLASHLOAN_ATTACKS=true");
  console.log("");
  console.log("📋 Environment Variable Usage:");
  console.log("- BALANCER_FLASHLOAN_CONTRACT: Used by BalancerFlashloanStrategy");
  console.log("- Provides 0% fee flashloans via official Balancer V2 API");
  console.log("- Best for smaller amounts due to liquidity constraints");
  console.log("");
  console.log("💡 BALANCER ADVANTAGES:");
  console.log("- 💰 0% flashloan fees (vs 0.09% Aave)");
  console.log("- 🏦 Access to $1B+ in liquidity");
  console.log("- 🔵 Official Balancer V2 API");
  console.log("- ⚡ Lower gas costs than Aave");
  console.log("- 🌐 Same vault address on all networks");
  console.log("");
  console.log("📈 PROFIT CALCULATION:");
  console.log("- Balancer: 100% of arbitrage profit");
  console.log("- Aave: Arbitrage profit - 0.09% fee");
  console.log("- On 100k USDC: Save $90 per transaction!");
  console.log("");
  console.log(`📄 Deployment details saved to: ${filename}`);
  console.log("=".repeat(50));

  // Test contract interaction
  console.log("\n🧪 Testing contract interaction...");
  try {
    const testVault = await balancerFlashloan.getVault();
    console.log(`✅ Contract interaction test passed`);
    console.log(`   Vault address: ${testVault}`);
  } catch (error) {
    console.error(`❌ Contract interaction test failed:`, error.message);
  }

  return contractAddress;
}

// Handle errors
main()
  .then((contractAddress) => {
    console.log(`\n🎉 Balancer flashloan deployment completed!`);
    console.log(`Contract Address: ${contractAddress}`);
    console.log(`💰 Ready for 0% fee flashloan arbitrage!`);
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  });
