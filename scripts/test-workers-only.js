#!/usr/bin/env node

/**
 * Test workers without any dashboard interference
 */

// Disable dashboard and UI components
process.env.SPLIT_SCREEN_DASHBOARD = 'false';
process.env.DISABLE_DASHBOARD = 'true';

const { ArbitrageStrategy } = require('../dist/strategies/arbitrage');

async function testWorkersOnly() {
  console.log('🧪 Testing Workers Only\n');

  let arbitrageStrategy;

  try {
    // Initialize arbitrage strategy
    arbitrageStrategy = new ArbitrageStrategy();
    
    // Wait for worker initialization
    console.log('⏳ Initializing...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log(`Workers Enabled: ${arbitrageStrategy.isUsingWorkers()}`);
    
    if (arbitrageStrategy.isUsingWorkers()) {
      const workerStats = arbitrageStrategy.getWorkerStats();
      console.log(`Worker Count: ${workerStats.length}`);
      console.log('Worker IDs:', workerStats.map(w => w.workerId));
    }

    // Test single scan
    console.log('\n🔍 Testing single scan...');
    const start = Date.now();
    const opportunities = await arbitrageStrategy.scanForArbitrageOpportunities();
    const duration = Date.now() - start;

    console.log(`✅ Completed in ${duration}ms`);
    console.log(`✅ Found ${opportunities.length} opportunities`);

    // Check worker distribution
    if (arbitrageStrategy.isUsingWorkers()) {
      const finalStats = arbitrageStrategy.getWorkerStats();
      console.log('\n👷 Final Worker Stats:');
      finalStats.forEach(worker => {
        console.log(`   Worker ${worker.workerId}: ${worker.tasksProcessed} tasks processed`);
      });
      
      const totalTasks = finalStats.reduce((sum, w) => sum + w.tasksProcessed, 0);
      console.log(`   Total tasks: ${totalTasks}`);
      
      if (totalTasks === 0) {
        console.log('⚠️  No tasks were processed by workers - may be falling back to single-threaded');
      } else {
        const activeWorkers = finalStats.filter(w => w.tasksProcessed > 0).length;
        console.log(`   Active workers: ${activeWorkers}/${finalStats.length}`);
      }
    }

    console.log('\n✅ Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error(error.stack);
  } finally {
    // Cleanup
    if (arbitrageStrategy) {
      console.log('\n🧹 Cleaning up...');
      try {
        await arbitrageStrategy.shutdown();
        console.log('✅ Cleanup completed');
      } catch (cleanupError) {
        console.error('⚠️  Cleanup error:', cleanupError.message);
      }
    }
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Test interrupted');
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Test terminated');
  process.exit(0);
});

// Run the test
if (require.main === module) {
  testWorkersOnly().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

module.exports = { testWorkersOnly };
