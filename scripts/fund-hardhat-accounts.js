const { ethers } = require("hardhat");

async function main() {
  console.log("💰 Funding Hardhat Accounts with Tokens...\n");

  // Load environment variables
  require("dotenv").config();
  
  const chainId = await ethers.provider.getNetwork().then(n => n.chainId);
  const forkNetwork = process.env.FORK_NETWORK || "mainnet";
  
  console.log(`📡 Network: ${forkNetwork} (Chain ID: ${chainId})`);
  
  // Get accounts to fund
  const [deployer, account1, account2, account3] = await ethers.getSigners();
  const accountsToFund = [deployer, account1, account2, account3];
  
  console.log(`👥 Funding ${accountsToFund.length} accounts...\n`);
  
  if (forkNetwork === "mainnet" && chainId === 1n) {
    await fundMainnetTokens(accountsToFund);
  } else if (forkNetwork === "sepolia" && (chainId === 11155111n || chainId === 31337n)) {
    // Support both direct Sepolia and Hardhat fork of Sepolia
    console.log("🪙 Funding with Sepolia tokens (via fork)...");
    await fundSepoliaTokensOnFork(accountsToFund);
  } else if (chainId === 31337n) {
    // Hardhat fork - try to detect what we're forking from
    console.log("🔍 Detecting fork source for token funding...");
    const latestBlock = await ethers.provider.getBlockNumber();
    if (latestBlock > 1000) {
      console.log("📡 Detected forked network, attempting Sepolia token funding...");
      await fundSepoliaTokensOnFork(accountsToFund);
    } else {
      console.log("ℹ️  Isolated Hardhat network - no token funding needed");
      console.log("   Accounts already have 10,000 ETH each");
    }
  } else {
    console.log("ℹ️  No token funding needed for isolated Hardhat network");
    console.log("   Accounts already have 10,000 ETH each");
  }
  
  // Display final balances
  await displayBalances(accountsToFund);
  
  console.log("\n✅ Account funding complete!");
}

async function fundMainnetTokens(accounts) {
  console.log("🪙 Funding with Mainnet tokens...\n");
  
  const tokens = {
    USDC: {
      address: "******************************************",
      whale: "******************************************", // Binance
      decimals: 6,
      amount: "100000" // 100,000 USDC
    },
    DAI: {
      address: "******************************************",
      whale: "******************************************", // Binance
      decimals: 18,
      amount: "100000" // 100,000 DAI
    },
    USDT: {
      address: "******************************************",
      whale: "******************************************", // Binance
      decimals: 6,
      amount: "100000" // 100,000 USDT
    },
    WETH: {
      address: "******************************************",
      whale: "******************************************", // Binance
      decimals: 18,
      amount: "100" // 100 WETH
    }
  };
  
  for (const [symbol, tokenInfo] of Object.entries(tokens)) {
    console.log(`💸 Funding accounts with ${symbol}...`);
    
    try {
      // Impersonate whale account
      await ethers.provider.send("hardhat_impersonateAccount", [tokenInfo.whale]);
      const whale = await ethers.getSigner(tokenInfo.whale);
      
      // Fund whale with ETH for gas
      const [deployer] = await ethers.getSigners();
      await deployer.sendTransaction({
        to: tokenInfo.whale,
        value: ethers.parseEther("10")
      });
      
      // Get token contract
      const token = await ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", tokenInfo.address);
      const amount = ethers.parseUnits(tokenInfo.amount, tokenInfo.decimals);
      
      // Transfer tokens to each account
      for (let i = 0; i < accounts.length; i++) {
        const tx = await token.connect(whale).transfer(accounts[i].address, amount);
        await tx.wait();
        console.log(`   ✅ Account ${i + 1}: ${tokenInfo.amount} ${symbol}`);
      }
      
      // Stop impersonating
      await ethers.provider.send("hardhat_stopImpersonatingAccount", [tokenInfo.whale]);
      
    } catch (error) {
      console.log(`   ❌ Failed to fund ${symbol}: ${error.message}`);
    }
  }
}

async function fundSepoliaTokens(accounts) {
  console.log("🪙 Funding with Sepolia tokens...\n");

  const tokens = {
    USDC: {
      address: "******************************************",
      decimals: 6,
      amount: "10000" // 10,000 USDC
    },
    DAI: {
      address: "******************************************",
      decimals: 18,
      amount: "10000" // 10,000 DAI
    },
    WETH: {
      address: "******************************************",
      decimals: 18,
      amount: "10" // 10 WETH
    }
  };

  console.log("ℹ️  Sepolia tokens need to be obtained from faucets");
  console.log("   This script will check current balances only\n");

  for (const [symbol, tokenInfo] of Object.entries(tokens)) {
    try {
      const token = await ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", tokenInfo.address);

      for (let i = 0; i < accounts.length; i++) {
        const balance = await token.balanceOf(accounts[i].address);
        const formattedBalance = ethers.formatUnits(balance, tokenInfo.decimals);
        console.log(`   Account ${i + 1} ${symbol}: ${formattedBalance}`);
      }
    } catch (error) {
      console.log(`   ❌ Failed to check ${symbol}: ${error.message}`);
    }
  }
}

async function fundSepoliaTokensOnFork(accounts) {
  console.log("🪙 Funding with Sepolia tokens on fork...\n");

  const tokens = {
    USDC: {
      address: "******************************************",
      decimals: 6,
      amount: "50000" // 50,000 USDC for testing
    },
    DAI: {
      address: "******************************************",
      decimals: 18,
      amount: "50000" // 50,000 DAI for testing
    },
    WETH: {
      address: "******************************************",
      decimals: 18,
      amount: "100" // 100 WETH for testing
    }
  };

  // Try to find accounts with token balances to use as "whales"
  const potentialWhales = [
    "******************************************", // Common test address
    "0x8ba1f109551bD432803012645Hac136c", // Another test address
    "******************************************", // Hardhat account 0
  ];

  for (const [symbol, tokenInfo] of Object.entries(tokens)) {
    console.log(`💸 Attempting to fund accounts with ${symbol}...`);

    try {
      const token = await ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", tokenInfo.address);
      const amount = ethers.parseUnits(tokenInfo.amount, tokenInfo.decimals);

      // Try to find a whale account with tokens
      let whaleFound = false;
      for (const whaleAddress of potentialWhales) {
        try {
          const whaleBalance = await token.balanceOf(whaleAddress);
          if (whaleBalance > amount * BigInt(accounts.length)) {
            // Found a whale with enough tokens
            await ethers.provider.send("hardhat_impersonateAccount", [whaleAddress]);
            const whale = await ethers.getSigner(whaleAddress);

            // Fund whale with ETH for gas
            const [deployer] = await ethers.getSigners();
            await deployer.sendTransaction({
              to: whaleAddress,
              value: ethers.parseEther("10")
            });

            // Transfer tokens to test accounts
            for (let i = 0; i < accounts.length; i++) {
              const tx = await token.connect(whale).transfer(accounts[i].address, amount);
              await tx.wait();
              console.log(`   ✅ Account ${i + 1}: ${tokenInfo.amount} ${symbol}`);
            }

            await ethers.provider.send("hardhat_stopImpersonatingAccount", [whaleAddress]);
            whaleFound = true;
            break;
          }
        } catch (error) {
          // Continue to next whale
          continue;
        }
      }

      if (!whaleFound) {
        // If no whale found, try to mint tokens directly to accounts
        console.log(`   ⚠️  No whale found for ${symbol}, checking current balances...`);
        for (let i = 0; i < accounts.length; i++) {
          const balance = await token.balanceOf(accounts[i].address);
          const formattedBalance = ethers.formatUnits(balance, tokenInfo.decimals);
          console.log(`   Account ${i + 1} ${symbol}: ${formattedBalance}`);
        }
      }

    } catch (error) {
      console.log(`   ❌ Failed to fund ${symbol}: ${error.message}`);
    }
  }
}

async function displayBalances(accounts) {
  console.log("\n📊 Final Account Balances:");
  
  for (let i = 0; i < accounts.length; i++) {
    const ethBalance = await ethers.provider.getBalance(accounts[i].address);
    console.log(`\n👤 Account ${i + 1}: ${accounts[i].address}`);
    console.log(`   ETH: ${ethers.formatEther(ethBalance)}`);
    
    // Check token balances if on a fork
    const chainId = await ethers.provider.getNetwork().then(n => n.chainId);
    if (chainId === 1n || chainId === 11155111n) {
      await displayTokenBalances(accounts[i].address, chainId);
    }
  }
}

async function displayTokenBalances(address, chainId) {
  const tokens = chainId === 1n ? {
    USDC: { address: "******************************************", decimals: 6 },
    DAI: { address: "******************************************", decimals: 18 },
    USDT: { address: "******************************************", decimals: 6 },
    WETH: { address: "******************************************", decimals: 18 }
  } : {
    USDC: { address: "******************************************", decimals: 6 },
    DAI: { address: "******************************************", decimals: 18 },
    WETH: { address: "******************************************", decimals: 18 }
  };
  
  for (const [symbol, tokenInfo] of Object.entries(tokens)) {
    try {
      const token = await ethers.getContractAt("@openzeppelin/contracts/token/ERC20/IERC20.sol:IERC20", tokenInfo.address);
      const balance = await token.balanceOf(address);
      const formattedBalance = ethers.formatUnits(balance, tokenInfo.decimals);
      console.log(`   ${symbol}: ${formattedBalance}`);
    } catch (error) {
      console.log(`   ${symbol}: Error reading balance`);
    }
  }
}

// Handle errors
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Funding failed:", error);
    process.exit(1);
  });
