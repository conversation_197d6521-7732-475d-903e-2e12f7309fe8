#!/bin/bash

# Start Hardhat node with Sepolia fork
# Usage: ./scripts/start-hardhat-sepolia.sh [FORK_URL]

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Starting Hardhat Node with Sepolia Fork${NC}"
echo "============================================="

# Load environment variables if .env exists
if [ -f ".env" ]; then
    source .env
fi

# Use provided URL or environment variable or default
FORK_URL=${1:-${SEPOLIA_FORK_URL:-${ALCHEMY_API_KEY:+https://eth-sepolia.g.alchemy.com/v2/$ALCHEMY_API_KEY}}}
FORK_URL=${FORK_URL:-https://eth-sepolia.g.alchemy.com/v2/demo}

echo -e "${BLUE}Fork URL:${NC} $FORK_URL"
echo -e "${YELLOW}Note: Using demo key may have rate limits. Set ALCHEMY_API_KEY for better performance.${NC}"
echo ""

# Check if port 8545 is already in use
if lsof -Pi :8545 -sTCP:LISTEN -t >/dev/null ; then
    echo -e "${YELLOW}⚠️  Port 8545 is already in use${NC}"
    echo "Kill existing process? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        echo "Killing process on port 8545..."
        lsof -ti:8545 | xargs kill -9 || true
        sleep 2
    else
        echo "Exiting..."
        exit 1
    fi
fi

echo -e "${GREEN}Starting Hardhat node...${NC}"
echo "Press Ctrl+C to stop"
echo ""

# Start Hardhat node with Sepolia fork
npx hardhat node --fork "$FORK_URL"
