const { ethers } = require("hardhat");
const fs = require("fs");
const path = require("path");

async function main() {
  console.log("🚀 Setting up Hardhat Fork for MEV Bot Testing...\n");

  // Load environment variables
  require("dotenv").config();
  
  const forkNetwork = process.env.FORK_NETWORK || "mainnet";
  const chainId = await ethers.provider.getNetwork().then(n => n.chainId);
  
  console.log(`📡 Network: ${forkNetwork} (Chain ID: ${chainId})`);
  console.log(`🔗 RPC URL: ${process.env.RPC_URL || "http://localhost:8545"}`);
  
  // Get signers
  const [deployer, ...accounts] = await ethers.getSigners();
  console.log(`\n👤 Deployer: ${deployer.address}`);
  console.log(`💰 Deployer Balance: ${ethers.formatEther(await ethers.provider.getBalance(deployer.address))} ETH`);
  
  // Display available accounts
  console.log(`\n📋 Available Test Accounts:`);
  for (let i = 0; i < Math.min(accounts.length, 5); i++) {
    const balance = await ethers.provider.getBalance(accounts[i].address);
    console.log(`   Account ${i + 1}: ${accounts[i].address} (${ethers.formatEther(balance)} ETH)`);
  }
  
  // Check if we're on a fork
  const latestBlock = await ethers.provider.getBlockNumber();
  console.log(`\n🧱 Latest Block: ${latestBlock}`);
  
  if (latestBlock > 100) {
    console.log("✅ Successfully connected to forked network");
    
    // Test token contracts if on mainnet fork
    if (forkNetwork === "mainnet" && chainId === 1n) {
      await testMainnetTokens();
    } else if (forkNetwork === "sepolia" && chainId === 11155111n) {
      await testSepoliaTokens();
    }
  } else {
    console.log("ℹ️  Running on isolated Hardhat network (no fork)");
  }
  
  // Test basic functionality
  await testBasicFunctionality();
  
  console.log("\n✅ Hardhat fork setup complete!");
  console.log("\n📝 Next steps:");
  console.log("   1. Deploy flashloan contracts: npm run deploy:hardhat");
  console.log("   2. Fund accounts with tokens: npm run fund:hardhat");
  console.log("   3. Start MEV bot: npm run dev");
}

async function testMainnetTokens() {
  console.log("\n🪙 Testing Mainnet Token Contracts...");
  
  const tokens = {
    USDC: "******************************************",
    DAI: "******************************************",
    USDT: "******************************************",
    WETH: "******************************************"
  };
  
  for (const [symbol, address] of Object.entries(tokens)) {
    try {
      const contract = await ethers.getContractAt("IERC20", address);
      const name = await contract.name();
      const decimals = await contract.decimals();
      const totalSupply = await contract.totalSupply();
      
      console.log(`   ✅ ${symbol}: ${name} (${decimals} decimals, ${ethers.formatUnits(totalSupply, decimals)} total supply)`);
    } catch (error) {
      console.log(`   ❌ ${symbol}: Failed to connect (${error.message})`);
    }
  }
}

async function testSepoliaTokens() {
  console.log("\n🪙 Testing Sepolia Token Contracts...");
  
  const tokens = {
    USDC: "******************************************",
    DAI: "******************************************",
    WETH: "******************************************"
  };
  
  for (const [symbol, address] of Object.entries(tokens)) {
    try {
      const contract = await ethers.getContractAt("IERC20", address);
      const name = await contract.name();
      const decimals = await contract.decimals();
      
      console.log(`   ✅ ${symbol}: ${name} (${decimals} decimals)`);
    } catch (error) {
      console.log(`   ❌ ${symbol}: Failed to connect (${error.message})`);
    }
  }
}

async function testBasicFunctionality() {
  console.log("\n🧪 Testing Basic Functionality...");
  
  try {
    // Test transaction
    const [deployer, recipient] = await ethers.getSigners();
    const tx = await deployer.sendTransaction({
      to: recipient.address,
      value: ethers.parseEther("1.0")
    });
    
    await tx.wait();
    console.log(`   ✅ Test transaction successful: ${tx.hash}`);
    
    // Test block mining
    const blockBefore = await ethers.provider.getBlockNumber();
    await ethers.provider.send("evm_mine", []);
    const blockAfter = await ethers.provider.getBlockNumber();
    
    console.log(`   ✅ Block mining test: ${blockBefore} → ${blockAfter}`);
    
    // Test time manipulation
    const timeBefore = await ethers.provider.getBlock("latest").then(b => b.timestamp);
    await ethers.provider.send("evm_increaseTime", [3600]); // 1 hour
    await ethers.provider.send("evm_mine", []);
    const timeAfter = await ethers.provider.getBlock("latest").then(b => b.timestamp);
    
    console.log(`   ✅ Time manipulation test: ${timeBefore} → ${timeAfter} (+${timeAfter - timeBefore}s)`);
    
  } catch (error) {
    console.log(`   ❌ Basic functionality test failed: ${error.message}`);
  }
}

// Handle errors
main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Setup failed:", error);
    process.exit(1);
  });
