#!/usr/bin/env node

/**
 * Debug token configuration
 */

const { config, COMMON_TOKENS } = require('../dist/config');

console.log('🔍 Token Configuration Debug\n');

console.log(`Chain ID: ${config.chainId}`);
console.log(`Token count: ${COMMON_TOKENS.length}`);
console.log(`Expected pairs: ${(COMMON_TOKENS.length * (COMMON_TOKENS.length - 1)) / 2}`);

console.log('\nTokens:');
COMMON_TOKENS.forEach((token, index) => {
  console.log(`  ${index + 1}. ${token.symbol} (${token.name})`);
});

console.log('\nToken pairs:');
let pairCount = 0;
for (let i = 0; i < COMMON_TOKENS.length; i++) {
  for (let j = i + 1; j < COMMON_TOKENS.length; j++) {
    pairCount++;
    console.log(`  ${pairCount}. ${COMMON_TOKENS[i].symbol}/${COMMON_TOKENS[j].symbol}`);
  }
}

console.log(`\nTotal pairs: ${pairCount}`);

// Check environment
console.log('\nEnvironment:');
console.log(`NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);
console.log(`CHAIN_ID: ${process.env.CHAIN_ID || 'undefined'}`);
console.log(`RPC_URL: ${process.env.RPC_URL || 'undefined'}`);
