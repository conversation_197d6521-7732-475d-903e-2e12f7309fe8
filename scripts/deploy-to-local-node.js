const { ethers } = require("hardhat");
const fs = require("fs");

async function main() {
  console.log("🔍 Detecting local node network...");

  try {
    // Connect to local node and detect network
    const provider = new ethers.JsonRpcProvider("http://localhost:8545");
    const network = await provider.getNetwork();
    const chainId = Number(network.chainId);
    
    console.log(`📡 Detected Chain ID: ${chainId}`);
    
    let networkName;
    let deploymentFile;
    
    switch (chainId) {
      case 1:
        networkName = "Mainnet";
        deploymentFile = "deployment-hybrid-mainnet.json";
        break;
      case 11155111:
        networkName = "Sepolia";
        deploymentFile = "deployment-hybrid-sepolia.json";
        break;
      case 31337:
        networkName = "Hardhat";
        deploymentFile = "deployment-hybrid-hardhat.json";
        break;
      default:
        console.error(`❌ Unsupported network: Chain ID ${chainId}`);
        process.exit(1);
    }
    
    console.log(`🌐 Network: ${networkName}`);
    console.log(`🚀 Deploying HybridFlashloanArbitrage contract to local ${networkName} node...`);
    
    // Set up contract addresses based on network
    let AAVE_POOL_ADDRESSES_PROVIDER;
    let BALANCER_VAULT;
    
    if (chainId === 1) {
      // Mainnet
      AAVE_POOL_ADDRESSES_PROVIDER = "******************************************";
      BALANCER_VAULT = "******************************************";
    } else if (chainId === 11155111) {
      // Sepolia
      AAVE_POOL_ADDRESSES_PROVIDER = "0x012bAC54348C0E635dCAc9D5FB99f06F24136C9A";
      BALANCER_VAULT = "******************************************";
    } else if (chainId === 31337) {
      // Hardhat - use mainnet addresses (they'll be available if forking)
      AAVE_POOL_ADDRESSES_PROVIDER = "******************************************";
      BALANCER_VAULT = "******************************************";
    }
    
    console.log(`📋 Using addresses for ${networkName}:`);
    console.log(`   AAVE Pool: ${AAVE_POOL_ADDRESSES_PROVIDER}`);
    console.log(`   Balancer Vault: ${BALANCER_VAULT}`);
    
    // Get deployer account
    const [deployer] = await ethers.getSigners();
    console.log(`👤 Deploying with account: ${deployer.address}`);
    
    // Check deployer balance
    const balance = await provider.getBalance(deployer.address);
    console.log(`💰 Account balance: ${ethers.formatEther(balance)} ETH`);
    
    if (balance === 0n) {
      console.error("❌ Deployer account has no ETH balance");
      process.exit(1);
    }
    
    // Deploy the contract
    const HybridFlashloanArbitrage = await ethers.getContractFactory("HybridFlashloanArbitrage");
    
    console.log("📄 Deploying contract...");
    const contract = await HybridFlashloanArbitrage.deploy(
      AAVE_POOL_ADDRESSES_PROVIDER,
      BALANCER_VAULT
    );
    
    console.log("⏳ Waiting for deployment confirmation...");
    await contract.waitForDeployment();
    
    const contractAddress = await contract.getAddress();
    console.log(`✅ Contract deployed to: ${contractAddress}`);
    
    // Verify deployment
    console.log("🔍 Verifying deployment...");
    const code = await provider.getCode(contractAddress);
    if (code === "0x") {
      console.error("❌ Contract deployment failed - no code at address");
      process.exit(1);
    }
    
    console.log("✅ Contract deployment verified");
    
    // Test contract functions
    console.log("🧪 Testing contract functions...");
    try {
      const owner = await contract.owner();
      console.log(`👤 Contract owner: ${owner}`);
      
      const aavePool = await contract.POOL_ADDRESSES_PROVIDER();
      console.log(`🏦 AAVE Pool: ${aavePool}`);
      
      const balancerVault = await contract.BALANCER_VAULT();
      console.log(`⚖️  Balancer Vault: ${balancerVault}`);
      
    } catch (error) {
      console.warn(`⚠️  Contract function test failed: ${error.message}`);
    }
    
    // Save deployment info
    const deploymentInfo = {
      network: networkName.toLowerCase(),
      chainId: chainId,
      contractAddress: contractAddress,
      owner: deployer.address,
      aavePoolAddressesProvider: AAVE_POOL_ADDRESSES_PROVIDER,
      balancerVault: BALANCER_VAULT,
      deployedAt: new Date().toISOString(),
      blockNumber: await provider.getBlockNumber(),
      transactionHash: contract.deploymentTransaction()?.hash
    };
    
    // Add router addresses based on network
    if (chainId === 1) {
      deploymentInfo.routers = {
        uniswapV2: "0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D",
        uniswapV3: "0xE592427A0AEce92De3Edee1F18E0157C05861564",
        sushiswap: "0xd9e1cE17f2641f24aE83637ab66a2cca9C378B9F",
        balancer: "******************************************"
      };
    } else if (chainId === 11155111) {
      deploymentInfo.routers = {
        uniswapV2: "0x86dcd3293C53Cf8EFd7303B57beb2a3F671dDE98",
        uniswapV3: "0x3bFA4769FB09eefC5a80d6E87c3B9C650f7Ae48E"
      };
    }
    
    fs.writeFileSync(deploymentFile, JSON.stringify(deploymentInfo, null, 2));
    console.log(`📄 Deployment info saved to: ${deploymentFile}`);
    
    console.log("\n🎉 Deployment completed successfully!");
    console.log("\n📋 Summary:");
    console.log(`   🌐 Network: ${networkName} (Chain ID: ${chainId})`);
    console.log(`   📄 Contract: ${contractAddress}`);
    console.log(`   👤 Owner: ${deployer.address}`);
    console.log(`   📁 Config: ${deploymentFile}`);
    
    console.log("\n🚀 Next steps:");
    console.log("   1. Update your .env file with the contract address:");
    console.log(`      HYBRID_FLASHLOAN_CONTRACT=${contractAddress}`);
    console.log("   2. Start the MEV bot:");
    console.log("      npm run dev:local");
    
  } catch (error) {
    console.error("❌ Deployment failed:", error.message);
    
    if (error.message.includes("ECONNREFUSED")) {
      console.log("\n💡 Troubleshooting:");
      console.log("   - Make sure your local Ethereum node is running on localhost:8545");
      console.log("   - Check if the node is synced and responding");
      console.log("   - Test connection: curl -X POST -H 'Content-Type: application/json' --data '{\"jsonrpc\":\"2.0\",\"method\":\"eth_blockNumber\",\"params\":[],\"id\":1}' http://localhost:8545");
    }
    
    process.exit(1);
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error("❌ Unexpected error:", error);
    process.exit(1);
  });
