const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 Deploying FlashloanArbitrage contract to Sepolia...");

  // Aave V3 Pool Addresses Provider on Sepolia
  const AAVE_POOL_ADDRESSES_PROVIDER = "******************************************";

  // Get the contract factory
  const FlashloanArbitrage = await ethers.getContractFactory("FlashloanArbitrage");

  // Deploy the contract
  console.log("📝 Deploying contract...");
  const flashloanArbitrage = await FlashloanArbitrage.deploy(AAVE_POOL_ADDRESSES_PROVIDER);

  // Wait for deployment
  await flashloanArbitrage.waitForDeployment();

  const contractAddress = await flashloanArbitrage.getAddress();
  console.log("✅ FlashloanArbitrage deployed to:", contractAddress);

  // Verify deployment
  console.log("🔍 Verifying deployment...");
  const owner = await flashloanArbitrage.owner();
  console.log("📋 Contract owner:", owner);

  // Save deployment info
  const deploymentInfo = {
    contractAddress: contractAddress,
    owner: owner,
    network: "sepolia",
    aavePoolAddressesProvider: AAVE_POOL_ADDRESSES_PROVIDER,
    deployedAt: new Date().toISOString(),
    transactionHash: flashloanArbitrage.deploymentTransaction()?.hash
  };

  console.log("\n📊 Deployment Summary:");
  console.log("=".repeat(50));
  console.log(`Contract Address: ${contractAddress}`);
  console.log(`Owner: ${owner}`);
  console.log(`Network: Sepolia Testnet`);
  console.log(`Aave Pool Provider: ${AAVE_POOL_ADDRESSES_PROVIDER}`);
  console.log(`Transaction Hash: ${deploymentInfo.transactionHash}`);
  console.log("=".repeat(50));

  console.log("\n🔧 Next Steps:");
  console.log("1. Update your .env file with the contract address:");
  console.log(`   FLASHLOAN_CONTRACT_ADDRESS=${contractAddress}`);
  console.log("2. Fund the contract with some ETH for gas fees");
  console.log("3. Test the flashloan functionality");

  console.log("\n⚠️  Important Notes:");
  console.log("- This is a testnet deployment");
  console.log("- Make sure to test thoroughly before mainnet");
  console.log("- The contract owner can withdraw profits and emergency withdraw");
  console.log("- Only the owner can execute flashloan arbitrage");

  return contractAddress;
}

// Handle errors
main()
  .then((contractAddress) => {
    console.log(`\n🎉 Deployment completed successfully!`);
    console.log(`Contract Address: ${contractAddress}`);
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  });
