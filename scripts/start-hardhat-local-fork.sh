#!/bin/bash

# Start Hardhat node forking from your local ETH RPC node
# This gives you Hardhat's testing features with your local node's data

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}🏠 Starting Hardhat Fork from Local ETH Node${NC}"
echo "=============================================="

# Check if local node is running
LOCAL_NODE_URL="http://localhost:8545"
echo -e "${BLUE}Checking local node at: ${LOCAL_NODE_URL}${NC}"

if ! curl -s -X POST -H "Content-Type: application/json" \
   --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
   "$LOCAL_NODE_URL" > /dev/null 2>&1; then
    echo -e "${RED}❌ Local ETH node not found at $LOCAL_NODE_URL${NC}"
    echo ""
    echo "Please make sure your local Ethereum node is running:"
    echo "  • Geth: geth --http --http.api eth,net,web3 --ws --ws.api eth,net,web3"
    echo "  • Erigon: ./build/bin/erigon --http.api eth,net,web3 --ws"
    echo ""
    exit 1
fi

# Get network info from local node
echo -e "${BLUE}Detecting local node network...${NC}"

CHAIN_ID_HEX=$(curl -s -X POST -H "Content-Type: application/json" \
               --data '{"jsonrpc":"2.0","method":"eth_chainId","params":[],"id":1}' \
               "$LOCAL_NODE_URL" | grep -o '"result":"[^"]*"' | cut -d'"' -f4)

BLOCK_HEX=$(curl -s -X POST -H "Content-Type: application/json" \
            --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
            "$LOCAL_NODE_URL" | grep -o '"result":"[^"]*"' | cut -d'"' -f4)

CHAIN_ID=$((CHAIN_ID_HEX))
BLOCK_NUM=$((BLOCK_HEX))

# Determine network name
case $CHAIN_ID in
    1) NETWORK="Mainnet" ;;
    11155111) NETWORK="Sepolia" ;;
    *) NETWORK="Unknown (Chain ID: $CHAIN_ID)" ;;
esac

echo -e "${GREEN}✅ Local node detected:${NC}"
echo "   🌐 Network: $NETWORK"
echo "   🔗 Chain ID: $CHAIN_ID"
echo "   🧱 Latest Block: $BLOCK_NUM"
echo ""

# Check if Hardhat port is available
HARDHAT_PORT=8545
if lsof -Pi :$HARDHAT_PORT -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo -e "${YELLOW}⚠️  Port $HARDHAT_PORT is already in use${NC}"
    echo "This might be your local node. Hardhat will try to use a different port."
    echo ""
    
    # Try alternative ports
    for port in 8546 8547 8548; do
        if ! lsof -Pi :$port -sTCP:LISTEN -t >/dev/null 2>&1; then
            HARDHAT_PORT=$port
            echo -e "${BLUE}Using port $HARDHAT_PORT for Hardhat${NC}"
            break
        fi
    done
fi

# Set up environment for Hardhat fork
echo -e "${BLUE}Configuring environment for Hardhat fork...${NC}"

# Create temporary .env for this session
cat > .env.hardhat-fork-temp << EOF
# Hardhat fork configuration
CHAIN_ID=31337
FORK_NETWORK=$NETWORK
MAINNET_FORK_URL=$LOCAL_NODE_URL
SEPOLIA_FORK_URL=$LOCAL_NODE_URL

# Hardhat RPC (will run on different port)
RPC_URL=http://localhost:$HARDHAT_PORT
MEMPOOL_WEBSOCKET_URL=ws://localhost:$HARDHAT_PORT

# Local node source
LOCAL_NODE_URL=$LOCAL_NODE_URL
LOCAL_NODE_WS=ws://localhost:8546

# Hardhat test accounts
PRIVATE_KEY=0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80
FLASHBOTS_SIGNER_KEY=0x59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d

# Testing configuration
MIN_PROFIT_WEI=****************
DRY_RUN=false
LOG_LEVEL=debug
ENABLE_FLASHBOTS=false
ENABLE_FLASHBOTS_MEMPOOL=false
ENABLE_ETHERS_MEMPOOL=true

# Strategy toggles
ENABLE_SANDWICH_ATTACKS=true
ENABLE_FRONT_RUNNING=true
ENABLE_ARBITRAGE=true
ENABLE_FLASHLOAN_ATTACKS=true
ENABLE_MULTI_BLOCK_ATTACKS=true
EOF

echo -e "${GREEN}✅ Environment configured${NC}"
echo ""

# Start Hardhat node
echo -e "${GREEN}🚀 Starting Hardhat node...${NC}"
echo -e "${BLUE}Fork source: $LOCAL_NODE_URL ($NETWORK)${NC}"
echo -e "${BLUE}Hardhat will run on: http://localhost:$HARDHAT_PORT${NC}"
echo ""
echo -e "${YELLOW}Press Ctrl+C to stop${NC}"
echo ""

# Determine fork command based on port
if [ "$HARDHAT_PORT" = "8545" ]; then
    # Standard port
    npx hardhat node --fork "$LOCAL_NODE_URL"
else
    # Custom port
    npx hardhat node --fork "$LOCAL_NODE_URL" --port "$HARDHAT_PORT"
fi

# Cleanup on exit
trap 'rm -f .env.hardhat-fork-temp' EXIT
