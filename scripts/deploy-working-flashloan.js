const { ethers } = require("hardhat");

async function main() {
  console.log("🚀 Deploying WorkingFlashloanArbitrage Contract...");
  
  const [deployer] = await ethers.getSigners();
  console.log("📋 Deploying with account:", deployer.address);
  
  // Get network info
  const network = await ethers.provider.getNetwork();
  console.log("🌐 Network:", network.name, "Chain ID:", network.chainId);
  
  // Aave Pool Addresses Provider for different networks
  const poolAddressesProviders = {
    1: "******************************************", // Mainnet
    ********: "******************************************", // Sepolia
    137: "******************************************", // Polygon
    42161: "******************************************", // Arbitrum
  };
  
  let poolAddressesProvider = poolAddressesProviders[network.chainId];
  
  // For local hardhat network, deploy a mock pool addresses provider
  if (network.chainId === 31337n) {
    console.log("🧪 Local network detected, deploying mock contracts...");
    
    // Deploy MockAavePool
    const MockAavePool = await ethers.getContractFactory("MockAavePool");
    const mockAavePool = await MockAavePool.deploy();
    await mockAavePool.waitForDeployment();
    console.log("✅ MockAavePool deployed to:", await mockAavePool.getAddress());
    
    // Deploy MockPoolAddressesProvider
    const MockPoolAddressesProvider = await ethers.getContractFactory("MockPoolAddressesProvider");
    const mockPoolAddressesProvider = await MockPoolAddressesProvider.deploy(await mockAavePool.getAddress());
    await mockPoolAddressesProvider.waitForDeployment();
    
    poolAddressesProvider = await mockPoolAddressesProvider.getAddress();
    console.log("✅ MockPoolAddressesProvider deployed to:", poolAddressesProvider);
  }
  
  if (!poolAddressesProvider) {
    throw new Error(`No Aave Pool Addresses Provider found for chain ID: ${network.chainId}`);
  }
  
  console.log("🏦 Using Aave Pool Addresses Provider:", poolAddressesProvider);
  
  // Deploy the contract
  const WorkingFlashloanArbitrage = await ethers.getContractFactory("WorkingFlashloanArbitrage");
  const workingFlashloanArbitrage = await WorkingFlashloanArbitrage.deploy(poolAddressesProvider);
  
  await workingFlashloanArbitrage.waitForDeployment();
  
  const contractAddress = await workingFlashloanArbitrage.getAddress();
  console.log("✅ WorkingFlashloanArbitrage deployed to:", contractAddress);
  console.log("👤 Owner:", await workingFlashloanArbitrage.owner());
  console.log("🔗 Chain ID:", await workingFlashloanArbitrage.CHAIN_ID());
  
  // Save deployment info
  const deploymentInfo = {
    network: network.name,
    chainId: network.chainId,
    contractAddress: contractAddress,
    poolAddressesProvider: poolAddressesProvider,
    deployer: deployer.address,
    deploymentTime: new Date().toISOString(),
    blockNumber: await ethers.provider.getBlockNumber()
  };
  
  console.log("\n📄 Deployment Summary:");
  console.log("═══════════════════════════════════════");
  console.log(`Network: ${deploymentInfo.network} (${deploymentInfo.chainId})`);
  console.log(`Contract: ${deploymentInfo.contractAddress}`);
  console.log(`Deployer: ${deploymentInfo.deployer}`);
  console.log(`Block: ${deploymentInfo.blockNumber}`);
  console.log(`Time: ${deploymentInfo.deploymentTime}`);
  
  // Verify contract if on a public network
  if (network.chainId !== 31337) { // Not local hardhat network
    console.log("\n🔍 Contract verification info:");
    console.log("Run this command to verify:");
    console.log(`npx hardhat verify --network ${network.name} ${contractAddress} ${poolAddressesProvider}`);
  }
  
  return deploymentInfo;
}

main()
  .then((deploymentInfo) => {
    console.log("\n🎉 Deployment completed successfully!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Deployment failed:", error);
    process.exit(1);
  });
