const { ethers } = require("hardhat");

async function main() {
  console.log("🚨 DEPLOYING TO ETHEREUM MAINNET 🚨");
  console.log("⚠️  WARNING: This will deploy to mainnet with real ETH!");
  
  // Confirm deployment
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const answer = await new Promise((resolve) => {
    readline.question('Are you sure you want to deploy to MAINNET? (yes/no): ', resolve);
  });
  
  readline.close();
  
  if (answer.toLowerCase() !== 'yes') {
    console.log("❌ Deployment cancelled");
    process.exit(0);
  }

  console.log("🚀 Deploying FlashloanArbitrage contract to Ethereum Mainnet...");

  // Aave V3 Pool Addresses Provider on Mainnet
  const AAVE_POOL_ADDRESSES_PROVIDER = "******************************************";

  // Get deployer account
  const [deployer] = await ethers.getSigners();
  const deployerBalance = await ethers.provider.getBalance(deployer.address);
  
  console.log("📋 Deployment Details:");
  console.log("=".repeat(50));
  console.log(`Deployer: ${deployer.address}`);
  console.log(`Balance: ${ethers.formatEther(deployerBalance)} ETH`);
  console.log(`Network: Ethereum Mainnet (Chain ID: 1)`);
  console.log(`Aave Pool Provider: ${AAVE_POOL_ADDRESSES_PROVIDER}`);
  console.log("=".repeat(50));

  // Check minimum balance
  const minBalance = ethers.parseEther("0.1"); // 0.1 ETH minimum
  if (deployerBalance < minBalance) {
    console.error(`❌ Insufficient balance. Need at least 0.1 ETH for deployment.`);
    process.exit(1);
  }

  // Get current gas price
  const feeData = await ethers.provider.getFeeData();
  console.log(`⛽ Current Gas Price: ${ethers.formatUnits(feeData.gasPrice, 'gwei')} gwei`);
  console.log(`⛽ Max Fee Per Gas: ${ethers.formatUnits(feeData.maxFeePerGas, 'gwei')} gwei`);

  // Get the contract factory
  const FlashloanArbitrage = await ethers.getContractFactory("FlashloanArbitrage");

  // Estimate deployment cost
  const deploymentData = FlashloanArbitrage.interface.encodeDeploy([AAVE_POOL_ADDRESSES_PROVIDER]);
  const estimatedGas = await ethers.provider.estimateGas({
    data: deploymentData
  });
  
  const estimatedCost = estimatedGas * feeData.gasPrice;
  console.log(`💰 Estimated deployment cost: ${ethers.formatEther(estimatedCost)} ETH`);

  // Final confirmation
  const finalAnswer = await new Promise((resolve) => {
    const readline2 = require('readline').createInterface({
      input: process.stdin,
      output: process.stdout
    });
    readline2.question(`Proceed with deployment? Cost: ${ethers.formatEther(estimatedCost)} ETH (yes/no): `, resolve);
    readline2.close();
  });

  if (finalAnswer.toLowerCase() !== 'yes') {
    console.log("❌ Deployment cancelled");
    process.exit(0);
  }

  // Deploy the contract
  console.log("📝 Deploying contract...");
  const flashloanArbitrage = await FlashloanArbitrage.deploy(AAVE_POOL_ADDRESSES_PROVIDER, {
    gasLimit: estimatedGas + BigInt(50000), // Add buffer
    maxFeePerGas: feeData.maxFeePerGas,
    maxPriorityFeePerGas: feeData.maxPriorityFeePerGas
  });

  console.log("⏳ Waiting for deployment confirmation...");
  await flashloanArbitrage.waitForDeployment();

  const contractAddress = await flashloanArbitrage.getAddress();
  const deploymentTx = flashloanArbitrage.deploymentTransaction();
  
  console.log("✅ FlashloanArbitrage deployed successfully!");
  console.log("📋 Deployment Summary:");
  console.log("=".repeat(60));
  console.log(`Contract Address: ${contractAddress}`);
  console.log(`Transaction Hash: ${deploymentTx.hash}`);
  console.log(`Block Number: ${deploymentTx.blockNumber}`);
  console.log(`Gas Used: ${deploymentTx.gasLimit}`);
  console.log(`Gas Price: ${ethers.formatUnits(deploymentTx.gasPrice, 'gwei')} gwei`);
  console.log("=".repeat(60));

  // Verify deployment
  console.log("🔍 Verifying deployment...");
  const owner = await flashloanArbitrage.owner();
  const chainId = await flashloanArbitrage.CHAIN_ID();
  const v2Router = await flashloanArbitrage.UNISWAP_V2_ROUTER();
  const v3Router = await flashloanArbitrage.UNISWAP_V3_ROUTER();
  const sushiRouter = await flashloanArbitrage.SUSHISWAP_ROUTER();

  console.log("📊 Contract Verification:");
  console.log(`Owner: ${owner}`);
  console.log(`Chain ID: ${chainId}`);
  console.log(`Uniswap V2 Router: ${v2Router}`);
  console.log(`Uniswap V3 Router: ${v3Router}`);
  console.log(`SushiSwap Router: ${sushiRouter}`);

  // Save deployment info
  const deploymentInfo = {
    network: "mainnet",
    chainId: 1,
    contractAddress: contractAddress,
    owner: owner,
    aavePoolAddressesProvider: AAVE_POOL_ADDRESSES_PROVIDER,
    deployedAt: new Date().toISOString(),
    transactionHash: deploymentTx.hash,
    blockNumber: deploymentTx.blockNumber,
    gasUsed: deploymentTx.gasLimit.toString(),
    gasPrice: deploymentTx.gasPrice.toString(),
    routers: {
      uniswapV2: v2Router,
      uniswapV3: v3Router,
      sushiswap: sushiRouter
    }
  };

  // Write deployment info to file
  const fs = require('fs');
  fs.writeFileSync(
    'deployment-mainnet.json', 
    JSON.stringify(deploymentInfo, null, 2)
  );

  console.log("\n🎉 MAINNET DEPLOYMENT COMPLETED SUCCESSFULLY!");
  console.log("=".repeat(60));
  console.log("🔧 NEXT STEPS:");
  console.log("1. Update your .env.mainnet file:");
  console.log(`   FLASHLOAN_CONTRACT_ADDRESS=${contractAddress}`);
  console.log("2. Fund the contract with ETH for gas fees");
  console.log("3. Start with DRY_RUN=true for testing");
  console.log("4. Monitor the contract carefully");
  console.log("5. Set up alerts and monitoring");
  console.log("");
  console.log("⚠️  IMPORTANT MAINNET WARNINGS:");
  console.log("- This contract handles real money");
  console.log("- MEV competition is fierce on mainnet");
  console.log("- Always test with small amounts first");
  console.log("- Monitor gas prices and profitability");
  console.log("- Have emergency procedures ready");
  console.log("");
  console.log("📄 Deployment details saved to: deployment-mainnet.json");
  console.log("=".repeat(60));

  return contractAddress;
}

// Handle errors
main()
  .then((contractAddress) => {
    console.log(`\n🎉 Mainnet deployment completed!`);
    console.log(`Contract Address: ${contractAddress}`);
    console.log(`🚨 Remember: This is MAINNET - real money at risk!`);
    process.exit(0);
  })
  .catch((error) => {
    console.error("❌ Mainnet deployment failed:", error);
    console.error("🔍 Check your configuration and try again");
    process.exit(1);
  });
