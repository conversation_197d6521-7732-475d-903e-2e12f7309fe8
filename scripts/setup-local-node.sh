#!/bin/bash

# Setup script for using local ETH RPC node with MEV bot
# This script helps configure the bot to use your local Ethereum node

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🏠 Local ETH RPC Node Setup for MEV Bot${NC}"
echo "=========================================="
echo ""

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -f "hardhat.config.ts" ]; then
    print_error "Please run this script from the project root directory"
    exit 1
fi

echo "🔍 Choose your local node setup:"
echo "1) Direct local node (production-ready)"
echo "2) Hardhat fork with local node as source (testing with Hardhat features)"
echo "3) Check local node connection"
echo ""
echo "💡 Option 2 gives you:"
echo "   ✅ Hardhat's testing features (time manipulation, account impersonation)"
echo "   ✅ Your local node's real blockchain data"
echo "   ✅ Fast testing with real liquidity"
echo ""
read -p "Enter your choice (1-3): " setup_choice

case $setup_choice in
    1)
        print_status "Setting up direct local node configuration..."
        cp .env.local .env
        print_success "Environment configured for direct local node"
        SETUP_TYPE="direct"
        ;;
    2)
        print_status "Setting up Hardhat fork with local node..."
        cp .env.hardhat-local .env
        print_success "Environment configured for Hardhat + local node"
        SETUP_TYPE="hardhat"
        ;;
    3)
        print_status "Testing local node connection..."
        SETUP_TYPE="test"
        ;;
    *)
        print_error "Invalid choice. Exiting."
        exit 1
        ;;
esac

# Test local node connection
print_status "Testing local node connection..."

# Common local node ports to check
LOCAL_PORTS=(************** 30303)
FOUND_NODES=()

for port in "${LOCAL_PORTS[@]}"; do
    if curl -s -X POST -H "Content-Type: application/json" \
       --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
       http://localhost:$port > /dev/null 2>&1; then
        
        # Get chain ID to identify the network
        CHAIN_ID=$(curl -s -X POST -H "Content-Type: application/json" \
                   --data '{"jsonrpc":"2.0","method":"eth_chainId","params":[],"id":1}' \
                   http://localhost:$port | grep -o '"result":"[^"]*"' | cut -d'"' -f4)
        
        # Get latest block
        BLOCK_HEX=$(curl -s -X POST -H "Content-Type: application/json" \
                    --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
                    http://localhost:$port | grep -o '"result":"[^"]*"' | cut -d'"' -f4)
        
        BLOCK_NUM=$((BLOCK_HEX))
        
        # Determine network name
        case $CHAIN_ID in
            "0x1") NETWORK="Mainnet" ;;
            "0xaa36a7") NETWORK="Sepolia" ;;
            "0x7a69") NETWORK="Hardhat" ;;
            *) NETWORK="Unknown (Chain ID: $CHAIN_ID)" ;;
        esac
        
        print_success "Found node on port $port: $NETWORK (Block: $BLOCK_NUM)"
        FOUND_NODES+=("$port:$NETWORK:$BLOCK_NUM")
    fi
done

if [ ${#FOUND_NODES[@]} -eq 0 ]; then
    print_error "No local Ethereum nodes found on common ports"
    echo ""
    echo "🔧 To set up a local node:"
    echo ""
    echo "📋 Option 1: Geth (Most Popular)"
    echo "   # Install Geth"
    echo "   brew install ethereum  # macOS"
    echo "   # or"
    echo "   sudo apt-get install ethereum  # Ubuntu"
    echo ""
    echo "   # Start Mainnet node"
    echo "   geth --http --http.api eth,net,web3 --ws --ws.api eth,net,web3"
    echo ""
    echo "   # Start Sepolia testnet node"
    echo "   geth --sepolia --http --http.api eth,net,web3 --ws --ws.api eth,net,web3"
    echo ""
    echo "📋 Option 2: Erigon (Faster Sync)"
    echo "   git clone https://github.com/ledgerwatch/erigon.git"
    echo "   cd erigon && make erigon"
    echo "   ./build/bin/erigon --http.api eth,net,web3 --ws"
    echo ""
    echo "📋 Option 3: Use Hardhat for testing"
    echo "   npm run hardhat:node"
    echo ""
    exit 1
fi

# If testing only, exit here
if [ "$SETUP_TYPE" = "test" ]; then
    print_success "Local node connection test complete!"
    exit 0
fi

# Configure the environment based on found nodes
if [ ${#FOUND_NODES[@]} -gt 1 ]; then
    echo ""
    print_status "Multiple nodes found. Select which one to use:"
    for i in "${!FOUND_NODES[@]}"; do
        IFS=':' read -r port network block <<< "${FOUND_NODES[$i]}"
        echo "$((i+1))) Port $port - $network (Block: $block)"
    done
    echo ""
    read -p "Enter your choice (1-${#FOUND_NODES[@]}): " node_choice
    
    if [ "$node_choice" -ge 1 ] && [ "$node_choice" -le ${#FOUND_NODES[@]} ]; then
        selected_node="${FOUND_NODES[$((node_choice-1))]}"
        IFS=':' read -r selected_port selected_network selected_block <<< "$selected_node"
    else
        print_error "Invalid choice. Using first node."
        IFS=':' read -r selected_port selected_network selected_block <<< "${FOUND_NODES[0]}"
    fi
else
    IFS=':' read -r selected_port selected_network selected_block <<< "${FOUND_NODES[0]}"
fi

print_success "Selected: Port $selected_port - $selected_network"

# Update .env file with correct URLs
if [ "$selected_port" != "8545" ]; then
    print_status "Updating configuration for port $selected_port..."
    sed -i.bak "s|localhost:8545|localhost:$selected_port|g" .env
    sed -i.bak "s|localhost:8546|localhost:$((selected_port+1))|g" .env
fi

# Set correct chain ID based on network
case $selected_network in
    "Mainnet")
        sed -i.bak "s/CHAIN_ID=.*/CHAIN_ID=1/" .env
        print_status "Configured for Mainnet (Chain ID: 1)"
        ;;
    "Sepolia")
        sed -i.bak "s/CHAIN_ID=.*/CHAIN_ID=11155111/" .env
        print_status "Configured for Sepolia (Chain ID: 11155111)"
        ;;
    "Hardhat")
        sed -i.bak "s/CHAIN_ID=.*/CHAIN_ID=31337/" .env
        print_status "Configured for Hardhat (Chain ID: 31337)"
        ;;
esac

# Clean up backup files
rm -f .env.bak

echo ""
print_success "Local node setup complete!"
echo ""
echo "📋 Configuration Summary:"
echo "   🌐 Network: $selected_network"
echo "   🔗 RPC URL: http://localhost:$selected_port"
echo "   📡 WebSocket: ws://localhost:$((selected_port+1))"
echo "   🧱 Latest Block: $selected_block"
echo ""
echo "🚀 Next steps:"
if [ "$SETUP_TYPE" = "direct" ]; then
    echo "   1. Deploy contracts: npm run deploy:mainnet (or deploy:sepolia)"
    echo "   2. Start MEV bot: npm run dev"
else
    echo "   1. Start Hardhat fork: npm run hardhat:fork:mainnet"
    echo "   2. Deploy contracts: npm run hardhat:deploy"
    echo "   3. Start MEV bot: npm run dev:hardhat"
fi
echo ""
echo "💡 Advantages of your local node:"
echo "   ✅ No rate limits"
echo "   ✅ Fastest response times"
echo "   ✅ Complete control"
echo "   ✅ Maximum reliability"
echo "   ✅ Direct mempool access"
echo ""
print_success "Happy MEV hunting with your local node! 🎯"
