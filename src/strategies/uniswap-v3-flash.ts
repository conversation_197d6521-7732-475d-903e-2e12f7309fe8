import { ethers } from 'ethers';
import { Token, Pool } from '../types';
import { config, ADDRESSES } from '../config';
import { logger } from '../utils/logger';
import { enhancedLogger } from '../utils/enhancedLogger';
import { statusDashboard } from '../utils/statusDashboard';
import { PoolManager } from '../dex/pools';

export interface UniswapV3FlashSwapRoute {
  tokenA: Token;
  tokenB: Token;
  borrowPool: Pool;
  sellPool: Pool;
  amount: bigint;
  expectedProfit: bigint;
  confidence: number;
  gasEstimate: bigint;
}

export interface UniswapV3FlashSwapOpportunity {
  route: UniswapV3FlashSwapRoute;
  profitability: number;
  riskScore: number;
  executionPriority: number;
}

/**
 * Uniswap V3 Flash Swap Strategy
 * Performs arbitrage between different fee tiers of the same token pair on Uniswap V3
 */
export class UniswapV3FlashSwapStrategy {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private poolManager: PoolManager;
  private flashSwapContract: ethers.Contract | null = null;
  
  // Get trading pairs from environment configuration
  private get TRADING_PAIRS() {
    return config.uniswapV3TradingPairs.map(pair => {
      const [tokenA, tokenB] = pair.split('/');
      return { tokenA, tokenB };
    });
  }

  // Get fee tiers from environment configuration
  private get FEE_TIERS() {
    return config.uniswapV3FeeTiers;
  }
  
  // Contract ABI for flash swap contract
  private readonly FLASH_SWAP_ABI = [
    'function executeFlashSwapArbitrage(address tokenA, address tokenB, uint24 borrowFee, uint24 sellFee, uint256 amount, uint256 minProfit) external',
    'function getPoolAddress(address tokenA, address tokenB, uint24 fee) external view returns (address)',
    'function checkArbitrageOpportunity(address tokenA, address tokenB, uint256 amount) external view returns (bool exists, uint24 lowFeeTier, uint24 highFeeTier, uint256 expectedProfit)',
    'event FlashSwapExecuted(address indexed tokenA, address indexed tokenB, uint24 borrowFee, uint24 sellFee, uint256 amount, uint256 profit)'
  ];

  constructor(provider: ethers.JsonRpcProvider, wallet: ethers.Wallet) {
    this.provider = provider;
    this.wallet = wallet;
    this.poolManager = new PoolManager();
    
    // Initialize flash swap contract (will be deployed)
    const contractAddress = process.env.UNISWAP_V3_FLASH_SWAP_CONTRACT || '';
    if (contractAddress) {
      this.flashSwapContract = new ethers.Contract(contractAddress, this.FLASH_SWAP_ABI, wallet);
    }
  }

  /**
   * Scan for Uniswap V3 flash swap arbitrage opportunities
   */
  async scanForOpportunities(): Promise<UniswapV3FlashSwapOpportunity[]> {
    const opportunities: UniswapV3FlashSwapOpportunity[] = [];
    
    try {
      enhancedLogger.info('🔍 Scanning for Uniswap V3 flash swap opportunities...');
      
      for (const pair of this.TRADING_PAIRS) {
        const tokenA = this.getTokenBySymbol(pair.tokenA);
        const tokenB = this.getTokenBySymbol(pair.tokenB);
        
        if (!tokenA || !tokenB) {
          logger.debug(`Skipping pair ${pair.tokenA}/${pair.tokenB} - tokens not found`);
          continue;
        }
        
        // Check all fee tier combinations
        for (let i = 0; i < this.FEE_TIERS.length; i++) {
          for (let j = 0; j < this.FEE_TIERS.length; j++) {
            if (i === j) continue; // Skip same fee tier
            
            const borrowFee = this.FEE_TIERS[i];
            const sellFee = this.FEE_TIERS[j];
            
            const opportunity = await this.checkFeeTierArbitrage(
              tokenA,
              tokenB,
              borrowFee,
              sellFee
            );
            
            if (opportunity) {
              opportunities.push(opportunity);
            }
          }
        }
      }
      
      // Sort by profitability
      opportunities.sort((a, b) => b.profitability - a.profitability);
      
      enhancedLogger.info(`Found ${opportunities.length} Uniswap V3 flash swap opportunities`);
      
      return opportunities;
    } catch (error) {
      logger.logError(error as Error, 'UniswapV3FlashSwapStrategy.scanForOpportunities');
      return [];
    }
  }

  /**
   * Check arbitrage opportunity between two fee tiers
   */
  private async checkFeeTierArbitrage(
    tokenA: Token,
    tokenB: Token,
    borrowFee: number,
    sellFee: number
  ): Promise<UniswapV3FlashSwapOpportunity | null> {
    try {
      // Get pools for both fee tiers
      const borrowPool = await this.poolManager.getPool(
        tokenA.address,
        tokenB.address,
        'uniswap-v3',
        borrowFee
      );
      
      const sellPool = await this.poolManager.getPool(
        tokenA.address,
        tokenB.address,
        'uniswap-v3',
        sellFee
      );
      
      if (!borrowPool || !sellPool) {
        return null;
      }
      
      // Calculate optimal flash swap amount
      const amount = this.calculateOptimalAmount(tokenA);
      
      // Simulate the arbitrage
      const simulation = await this.simulateFlashSwapArbitrage(
        tokenA,
        tokenB,
        borrowPool,
        sellPool,
        amount
      );
      
      if (!simulation.profitable) {
        return null;
      }
      
      const route: UniswapV3FlashSwapRoute = {
        tokenA,
        tokenB,
        borrowPool,
        sellPool,
        amount,
        expectedProfit: simulation.expectedProfit,
        confidence: simulation.confidence,
        gasEstimate: simulation.gasEstimate
      };
      
      const opportunity: UniswapV3FlashSwapOpportunity = {
        route,
        profitability: Number(ethers.formatEther(simulation.expectedProfit)),
        riskScore: this.calculateRiskScore(route),
        executionPriority: this.calculateExecutionPriority(route)
      };
      
      return opportunity;
    } catch (error) {
      logger.debug(`Error checking fee tier arbitrage for ${tokenA.symbol}/${tokenB.symbol}`, {
        error: (error as Error).message
      });
      return null;
    }
  }

  /**
   * Simulate flash swap arbitrage to estimate profit
   */
  private async simulateFlashSwapArbitrage(
    tokenA: Token,
    tokenB: Token,
    borrowPool: Pool,
    sellPool: Pool,
    amount: bigint
  ): Promise<{
    profitable: boolean;
    expectedProfit: bigint;
    confidence: number;
    gasEstimate: bigint;
  }> {
    try {
      // This is a simplified simulation
      // In a real implementation, you would:
      // 1. Calculate exact swap amounts using pool math
      // 2. Account for slippage
      // 3. Calculate gas costs
      // 4. Estimate price impact
      
      // For now, use a basic price difference calculation
      const priceDifference = this.calculatePriceDifference(borrowPool, sellPool);
      
      if (priceDifference < 0.001) { // Less than 0.1% difference
        return {
          profitable: false,
          expectedProfit: 0n,
          confidence: 0,
          gasEstimate: 0n
        };
      }
      
      // Estimate profit (simplified)
      const estimatedProfit = (amount * BigInt(Math.floor(priceDifference * 10000))) / 10000n;
      const gasEstimate = 300000n; // Estimated gas for flash swap
      const gasCost = gasEstimate * BigInt(config.maxGasPriceGwei) * 1000000000n; // Convert to wei
      
      const netProfit = estimatedProfit - gasCost;
      const profitable = netProfit > BigInt(config.minProfitWei);
      
      return {
        profitable,
        expectedProfit: netProfit,
        confidence: Math.min(95, 50 + (priceDifference * 1000)), // Higher confidence for larger spreads
        gasEstimate
      };
    } catch (error) {
      logger.debug('Error simulating flash swap arbitrage', { error: (error as Error).message });
      return {
        profitable: false,
        expectedProfit: 0n,
        confidence: 0,
        gasEstimate: 0n
      };
    }
  }

  /**
   * Calculate price difference between two pools
   */
  private calculatePriceDifference(pool1: Pool, pool2: Pool): number {
    try {
      // Simplified price calculation
      // In reality, you'd use the actual pool math with sqrt price
      if (!pool1.reserves || !pool2.reserves) {
        return 0;
      }

      const price1 = Number(pool1.reserves.reserve1) / Number(pool1.reserves.reserve0);
      const price2 = Number(pool2.reserves.reserve1) / Number(pool2.reserves.reserve0);

      return Math.abs(price1 - price2) / Math.min(price1, price2);
    } catch (error) {
      return 0;
    }
  }

  /**
   * Calculate optimal flash swap amount based on token
   */
  private calculateOptimalAmount(token: Token): bigint {
    const baseAmounts: Record<string, number> = {
      'WETH': config.flashloanBaseAmountWeth || 10,
      'USDC': 30000,
      'USDT': 30000,
      'WBTC': 1,
      'DAI': 30000
    };
    
    const baseAmount = baseAmounts[token.symbol] || 1000;
    return ethers.parseUnits(baseAmount.toString(), token.decimals);
  }

  /**
   * Execute flash swap arbitrage
   */
  async executeFlashSwap(opportunity: UniswapV3FlashSwapOpportunity): Promise<{
    success: boolean;
    txHash?: string;
    profit?: bigint;
    error?: string;
  }> {
    try {
      if (!this.flashSwapContract) {
        return { success: false, error: 'Flash swap contract not deployed' };
      }
      
      const { route } = opportunity;
      
      enhancedLogger.info('🚀 Executing Uniswap V3 flash swap arbitrage', {
        pair: `${route.tokenA.symbol}/${route.tokenB.symbol}`,
        borrowFee: route.borrowPool.fee,
        sellFee: route.sellPool.fee,
        amount: ethers.formatUnits(route.amount, route.tokenA.decimals),
        expectedProfit: ethers.formatEther(route.expectedProfit)
      });
      
      // Execute the flash swap
      const tx = await this.flashSwapContract.executeFlashSwapArbitrage(
        route.tokenA.address,
        route.tokenB.address,
        route.borrowPool.fee,
        route.sellPool.fee,
        route.amount,
        route.expectedProfit / 2n // Accept 50% of expected profit as minimum
      );
      
      const receipt = await tx.wait();
      
      // Parse profit from events
      const profit = this.parseFlashSwapProfit(receipt);
      
      enhancedLogger.success('✅ Flash swap executed successfully');

      statusDashboard.recordSuccessfulTransaction({
        timestamp: Date.now(),
        type: 'uniswap-v3-flash',
        profit: profit || 0n,
        gasUsed: BigInt(receipt.gasUsed || 0),
        txHash: receipt.hash,
        confidence: 95,
        details: `${route.tokenA.symbol}/${route.tokenB.symbol} arbitrage`
      });

      return {
        success: true,
        txHash: receipt.hash,
        profit: profit || undefined
      };
    } catch (error) {
      const errorMessage = (error as Error).message;
      logger.logError(error as Error, 'UniswapV3FlashSwapStrategy.executeFlashSwap');
      
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  /**
   * Parse profit from transaction receipt
   */
  private parseFlashSwapProfit(receipt: ethers.TransactionReceipt): bigint | null {
    try {
      if (!this.flashSwapContract) {
        return null;
      }

      for (const log of receipt.logs) {
        try {
          const parsed = this.flashSwapContract.interface.parseLog({
            topics: [...log.topics],
            data: log.data
          });
          if (parsed?.name === 'FlashSwapExecuted') {
            return parsed.args.profit;
          }
        } catch (e) {
          // Ignore parsing errors for other contracts' logs
        }
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Get token by symbol
   */
  private getTokenBySymbol(symbol: string): Token | null {
    const tokenAddresses: Record<string, string> = {
      'WETH': ADDRESSES.WETH,
      'USDC': ADDRESSES.USDC,
      'USDT': ADDRESSES.USDT,
      'WBTC': ADDRESSES.WBTC,
      'DAI': ADDRESSES.DAI
    };
    
    const address = tokenAddresses[symbol];
    if (!address) return null;
    
    const tokenDecimals: Record<string, number> = {
      'WETH': 18,
      'USDC': 6,
      'USDT': 6,
      'WBTC': 8,
      'DAI': 18
    };
    
    return {
      address,
      symbol: symbol as any,
      decimals: tokenDecimals[symbol],
      name: symbol
    };
  }

  /**
   * Calculate risk score for a route
   */
  private calculateRiskScore(route: UniswapV3FlashSwapRoute): number {
    // Lower score = lower risk
    let riskScore = 0;
    
    // Higher amounts = higher risk
    const amountUSD = Number(ethers.formatEther(route.amount)) * 2000; // Rough ETH price
    if (amountUSD > 100000) riskScore += 30;
    else if (amountUSD > 50000) riskScore += 20;
    else if (amountUSD > 10000) riskScore += 10;
    
    // Lower confidence = higher risk
    riskScore += (100 - route.confidence) / 2;
    
    return Math.min(100, riskScore);
  }

  /**
   * Calculate execution priority
   */
  private calculateExecutionPriority(route: UniswapV3FlashSwapRoute): number {
    // Higher score = higher priority
    let priority = 0;
    
    // Higher profit = higher priority
    const profitETH = Number(ethers.formatEther(route.expectedProfit));
    priority += profitETH * 100;
    
    // Higher confidence = higher priority
    priority += route.confidence;
    
    return priority;
  }
}
