import { ethers } from 'ethers';
import { FlashbotsBundleTransaction } from '@flashbots/ethers-provider-bundle';
import { FlashbotsBundleManager } from '../flashbots/bundle-provider';
import { AdvancedGasEstimator } from '../gas/advanced-estimator';
import { GasOptimizer } from '../gas/optimizer';
import { SwapDataBuilder } from './swap-data-builder';
import { PriceCalculator } from './price-calculator';
import { config } from '../config';
import { logger } from '../utils/logger';
import { enhancedLogger } from '../utils/enhancedLogger';
import { ArbitrageRoute, FlashloanRoute } from '../types';

export interface ExecutionResult {
  success: boolean;
  txHash?: string;
  bundleHash?: string;
  gasUsed?: bigint;
  gasPrice?: bigint;
  profit?: bigint;
  error?: string;
  executionTime?: number;
}

export interface ExecutionOptions {
  useFlashbots: boolean;
  urgency: 'slow' | 'standard' | 'fast' | 'instant';
  maxGasCostEth: number;
  slippageTolerance: number;
  deadline?: number;
}

/**
 * Enhanced MEV Executor with Flashbots integration
 * Handles both regular mempool and Flashbots bundle execution
 */
export class FlashbotsExecutor {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;
  private flashbotsManager: FlashbotsBundleManager;
  private gasEstimator: AdvancedGasEstimator;
  private gasOptimizer: GasOptimizer;
  private swapDataBuilder: SwapDataBuilder;
  private priceCalculator: PriceCalculator;

  constructor(
    provider: ethers.JsonRpcProvider,
    wallet: ethers.Wallet,
    flashbotsManager: FlashbotsBundleManager,
    gasEstimator: AdvancedGasEstimator,
    gasOptimizer: GasOptimizer
  ) {
    this.provider = provider;
    this.wallet = wallet;
    this.flashbotsManager = flashbotsManager;
    this.gasEstimator = gasEstimator;
    this.gasOptimizer = gasOptimizer;
    this.swapDataBuilder = new SwapDataBuilder(provider);
    this.priceCalculator = new PriceCalculator(provider);
  }

  /**
   * Execute arbitrage opportunity with enhanced validation
   */
  async executeArbitrage(
    route: ArbitrageRoute,
    options: ExecutionOptions = {
      useFlashbots: true,
      urgency: 'fast',
      maxGasCostEth: 0.01,
      slippageTolerance: 0.5
    }
  ): Promise<ExecutionResult> {
    const startTime = Date.now();

    try {
      enhancedLogger.systemStatus('🚀 Executing arbitrage opportunity...');
      enhancedLogger.systemStatus(`   Expected Profit: ${ethers.formatEther(route.expectedProfit)} ETH`);
      enhancedLogger.systemStatus(`   Use Flashbots: ${options.useFlashbots}`);

      // Validate and enhance the route with accurate calculations
      const validatedRoute = await this.validateArbitrageRoute(route);
      if (!validatedRoute) {
        enhancedLogger.systemStatus('❌ Route validation failed');
        return { success: false, error: 'Route validation failed' };
      }

      enhancedLogger.systemStatus(`   Validated Profit: ${ethers.formatEther(validatedRoute.expectedProfit)} ETH`);
      enhancedLogger.systemStatus(`   Confidence: ${validatedRoute.confidence}%`);

      // Check if gas conditions are favorable
      const gasFavorable = await this.gasEstimator.isGasFavorable(options.maxGasCostEth);
      if (!gasFavorable) {
        enhancedLogger.systemStatus('⚠️  Gas prices too high, skipping execution');
        return { success: false, error: 'Gas prices unfavorable' };
      }

      // Build transaction with validated route
      const transaction = await this.buildArbitrageTransaction(validatedRoute, options);
      if (!transaction) {
        return { success: false, error: 'Failed to build transaction' };
      }

      enhancedLogger.systemStatus('✅ Transaction built successfully');

      // Check for simulation mode
      if (config.simulationMode) {
        return await this.simulateArbitrageExecution(validatedRoute, transaction, options);
      }

      // Execute based on strategy
      if (options.useFlashbots && this.flashbotsManager.isAvailable()) {
        return await this.executeViaFlashbots([transaction], options);
      } else {
        // Extract transaction request from bundle transaction
        const txRequest = 'transaction' in transaction ? transaction.transaction : transaction;
        return await this.executeViaMempool(txRequest, options);
      }

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.executeArbitrage');
      return {
        success: false,
        error: (error as Error).message,
        executionTime: Date.now() - startTime
      };
    }
  }

  /**
   * Execute flashloan opportunity
   */
  async executeFlashloan(
    route: FlashloanRoute,
    options: ExecutionOptions = {
      useFlashbots: true,
      urgency: 'fast',
      maxGasCostEth: 0.02,
      slippageTolerance: 0.3
    }
  ): Promise<ExecutionResult> {
    const startTime = Date.now();

    try {
      enhancedLogger.systemStatus('💰 Executing flashloan opportunity...');
      enhancedLogger.systemStatus(`   Flashloan Amount: ${ethers.formatUnits(route.flashloanAmount, route.flashloanToken.decimals)} ${route.flashloanToken.symbol}`);
      enhancedLogger.systemStatus(`   Expected Profit: ${ethers.formatEther(route.expectedProfit)} ETH`);

      // Check profitability after current gas costs
      const gasEstimate = await this.gasEstimator.calculateGasCost(
        BigInt(500000), // Estimated gas for flashloan
        options.urgency
      );

      if (!this.gasOptimizer.isProfitable(route.expectedProfit, gasEstimate)) {
        enhancedLogger.systemStatus('⚠️  Opportunity no longer profitable after gas costs');
        return { success: false, error: 'Not profitable after gas costs' };
      }

      // Build flashloan transaction
      const transaction = await this.buildFlashloanTransaction(route, options);
      if (!transaction) {
        return { success: false, error: 'Failed to build flashloan transaction' };
      }

      // Check for simulation mode
      if (config.simulationMode) {
        return await this.simulateFlashloanExecution(route, transaction, options);
      }

      // Execute based on strategy
      if (options.useFlashbots && this.flashbotsManager.isAvailable()) {
        return await this.executeViaFlashbots([transaction], options);
      } else {
        // Extract transaction request from bundle transaction
        const txRequest = 'transaction' in transaction ? transaction.transaction : transaction;
        return await this.executeViaMempool(txRequest, options);
      }

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.executeFlashloan');
      return {
        success: false,
        error: (error as Error).message,
        executionTime: Date.now() - startTime
      };
    }
  }

  /**
   * Execute transaction via Flashbots bundle
   */
  private async executeViaFlashbots(
    transactions: FlashbotsBundleTransaction[],
    options: ExecutionOptions
  ): Promise<ExecutionResult> {
    try {
      enhancedLogger.systemStatus('📦 Executing via Flashbots bundle...');

      const targetBlock = await this.flashbotsManager.getNextBlock();
      
      // Simulate bundle first
      const simulation = await this.flashbotsManager.simulateBundle(transactions, targetBlock);
      if (!simulation.success) {
        enhancedLogger.systemStatus('❌ Bundle simulation failed');
        return { success: false, error: simulation.error };
      }

      enhancedLogger.systemStatus('✅ Bundle simulation successful');

      // Check for simulation mode
      if (config.simulationMode) {
        enhancedLogger.systemStatus('🎭 SIMULATION MODE: Bundle would be submitted to Flashbots');
        enhancedLogger.systemStatus(`   Target Block: ${targetBlock}`);
        enhancedLogger.systemStatus(`   Transactions: ${transactions.length}`);
        enhancedLogger.systemStatus(`   Estimated Gas: ${simulation.simulation?.gasUsed || 'unknown'}`);
        return {
          success: true,
          bundleHash: 'SIMULATION_BUNDLE_HASH',
          executionTime: Date.now() - Date.now()
        };
      }

      // Submit bundle
      const submission = await this.flashbotsManager.submitBundle(transactions, targetBlock);
      if (!submission.success) {
        enhancedLogger.systemStatus('❌ Bundle submission failed');
        return { success: false, error: submission.error };
      }

      enhancedLogger.systemStatus('🎉 Bundle submitted successfully!');
      
      return {
        success: true,
        bundleHash: submission.bundleHash,
        executionTime: Date.now() - Date.now()
      };

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.executeViaFlashbots');
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * Execute transaction via regular mempool
   */
  private async executeViaMempool(
    transaction: ethers.TransactionRequest,
    options: ExecutionOptions
  ): Promise<ExecutionResult> {
    try {
      enhancedLogger.systemStatus('🌐 Executing via mempool...');

      // Get optimal gas pricing
      const gasPrice = await this.gasEstimator.getOptimalGasPrice(options.urgency);
      
      // Prepare transaction (use EIP-1559 if available, otherwise legacy)
      const txRequest: ethers.TransactionRequest = {
        ...transaction,
        ...(gasPrice.maxFeePerGas && gasPrice.maxPriorityFeePerGas ? {
          // EIP-1559 transaction
          maxFeePerGas: gasPrice.maxFeePerGas,
          maxPriorityFeePerGas: gasPrice.maxPriorityFeePerGas
        } : {
          // Legacy transaction
          gasPrice: gasPrice.gasPrice
        })
      };

      if (config.dryRun || config.simulationMode) {
        const mode = config.simulationMode ? 'SIMULATION' : 'DRY RUN';
        enhancedLogger.systemStatus(`🎭 ${mode}: Transaction would be sent to mempool`);
        enhancedLogger.systemStatus(`   Gas Price: ${ethers.formatUnits(gasPrice.gasPrice, 'gwei')} gwei`);
        enhancedLogger.systemStatus(`   Gas Limit: ${transaction.gasLimit || 'estimated'}`);
        enhancedLogger.systemStatus(`   To: ${transaction.to}`);
        enhancedLogger.systemStatus(`   Value: ${ethers.formatEther(transaction.value || 0)} ETH`);
        return { success: true, txHash: `${mode.replace(' ', '_')}_TX_HASH` };
      }

      // Send transaction
      const tx = await this.wallet.sendTransaction(txRequest);
      enhancedLogger.systemStatus(`📤 Transaction sent: ${tx.hash}`);

      // Wait for confirmation
      const receipt = await tx.wait();
      if (!receipt) {
        return { success: false, error: 'Transaction failed' };
      }

      enhancedLogger.systemStatus('✅ Transaction confirmed!');
      enhancedLogger.systemStatus(`   Gas Used: ${receipt.gasUsed}`);
      enhancedLogger.systemStatus(`   Gas Price: ${ethers.formatUnits(receipt.gasPrice || 0, 'gwei')} gwei`);

      return {
        success: true,
        txHash: receipt.hash,
        gasUsed: receipt.gasUsed,
        gasPrice: receipt.gasPrice || BigInt(0)
      };

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.executeViaMempool');
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * Simulate arbitrage execution without sending transactions
   */
  private async simulateArbitrageExecution(
    route: ArbitrageRoute,
    transaction: FlashbotsBundleTransaction,
    options: ExecutionOptions
  ): Promise<ExecutionResult> {
    try {
      enhancedLogger.systemStatus('🎭 SIMULATION: Arbitrage opportunity detected');
      enhancedLogger.systemStatus(`   Expected Profit: ${ethers.formatEther(route.expectedProfit)} ETH`);
      enhancedLogger.systemStatus(`   Route: ${route.pools.map(p => p.protocol || 'Unknown').join(' → ')}`);
      enhancedLogger.systemStatus(`   Tokens: ${route.tokens.map(t => t.symbol).join(' → ')}`);

      // Simulate gas estimation
      const gasEstimate = await this.gasEstimator.getOptimalGasPrice(options.urgency);
      const estimatedGasCost = BigInt(300000) * gasEstimate.gasPrice; // Rough estimate

      enhancedLogger.systemStatus(`   Estimated Gas Cost: ${ethers.formatEther(estimatedGasCost)} ETH`);

      // Convert expectedProfit to BigInt for calculation
      const expectedProfitBigInt = BigInt(route.expectedProfit.toString());
      const netProfit = expectedProfitBigInt - estimatedGasCost;

      enhancedLogger.systemStatus(`   Net Profit: ${ethers.formatEther(netProfit)} ETH`);
      if (netProfit <= 0) {
        enhancedLogger.systemStatus('⚠️  Would not be profitable after gas costs');
        return { success: false, error: 'Not profitable after gas costs' };
      }

      enhancedLogger.systemStatus('✅ SIMULATION: Arbitrage would be profitable and executed');

      return {
        success: true,
        txHash: 'SIMULATION_ARBITRAGE_TX',
        gasUsed: BigInt(300000),
        gasPrice: gasEstimate.gasPrice,
        profit: netProfit,
        executionTime: Date.now() - Date.now()
      };

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.simulateArbitrageExecution');
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * Simulate flashloan execution without sending transactions
   */
  private async simulateFlashloanExecution(
    route: FlashloanRoute,
    transaction: FlashbotsBundleTransaction,
    options: ExecutionOptions
  ): Promise<ExecutionResult> {
    try {
      enhancedLogger.systemStatus('🎭 SIMULATION: Flashloan opportunity detected');
      enhancedLogger.systemStatus(`   Flashloan Amount: ${ethers.formatEther(route.flashloanAmount)} ${route.flashloanToken.symbol}`);
      enhancedLogger.systemStatus(`   Expected Profit: ${ethers.formatEther(route.expectedProfit)} ETH`);
      enhancedLogger.systemStatus(`   Arbitrage Route: ${route.arbitrageRoute.tokens.map(t => t.symbol).join(' → ')}`);

      // Simulate gas estimation for flashloan
      const gasEstimate = await this.gasEstimator.getOptimalGasPrice(options.urgency);
      const estimatedGasCost = BigInt(400000) * gasEstimate.gasPrice; // Higher gas for flashloan

      enhancedLogger.systemStatus(`   Estimated Gas Cost: ${ethers.formatEther(estimatedGasCost)} ETH`);

      // Convert expectedProfit to BigInt for calculation
      const expectedProfitBigInt = BigInt(route.expectedProfit.toString());
      const netProfit = expectedProfitBigInt - estimatedGasCost;

      enhancedLogger.systemStatus(`   Net Profit: ${ethers.formatEther(netProfit)} ETH`);
      if (netProfit <= 0) {
        enhancedLogger.systemStatus('⚠️  Would not be profitable after gas costs');
        return { success: false, error: 'Not profitable after gas costs' };
      }

      enhancedLogger.systemStatus('✅ SIMULATION: Flashloan would be profitable and executed');

      return {
        success: true,
        txHash: 'SIMULATION_FLASHLOAN_TX',
        gasUsed: BigInt(400000),
        gasPrice: gasEstimate.gasPrice,
        profit: netProfit,
        executionTime: Date.now() - Date.now()
      };

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.simulateFlashloanExecution');
      return { success: false, error: (error as Error).message };
    }
  }

  /**
   * Build arbitrage transaction with proper swap data
   */
  private async buildArbitrageTransaction(
    route: ArbitrageRoute,
    options: ExecutionOptions
  ): Promise<FlashbotsBundleTransaction | null> {
    try {
      // Validate route has valid pools and tokens
      if (!route.pools || route.pools.length === 0) {
        logger.debug('Invalid arbitrage route: no pools');
        return null;
      }

      if (!route.tokens || route.tokens.length < 2) {
        logger.debug('Invalid arbitrage route: insufficient tokens');
        return null;
      }

      const targetPool = route.pools[0];
      if (!targetPool.address || !ethers.isAddress(targetPool.address)) {
        logger.debug('Invalid pool address in arbitrage route', { address: targetPool.address });
        return null;
      }

      // Build swap data using the new swap data builder
      const swapData = await this.swapDataBuilder.buildArbitrageSwapData(
        route,
        options,
        this.wallet.address
      );

      if (!swapData) {
        logger.debug('Failed to build swap data for arbitrage');
        return null;
      }

      // Validate swap data
      if (!this.swapDataBuilder.validateSwapData(swapData)) {
        logger.debug('Invalid swap data generated', { swapData });
        return null;
      }

      // Estimate gas for the transaction
      const gasLimit = await this.gasOptimizer.estimateGasForTransaction(
        swapData.to,
        swapData.data,
        swapData.value
      );

      // Build the transaction
      const transaction: ethers.TransactionRequest = {
        to: swapData.to,
        data: swapData.data,
        value: swapData.value,
        gasLimit
      };

      logger.debug('Built arbitrage transaction', {
        to: transaction.to,
        dataLength: swapData.data.length,
        gasLimit: gasLimit.toString()
      });

      return this.flashbotsManager.createBundleTransaction(transaction, this.wallet);

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.buildArbitrageTransaction');
      return null;
    }
  }

  /**
   * Validate and enhance arbitrage route with accurate profit calculations
   */
  async validateArbitrageRoute(route: ArbitrageRoute): Promise<ArbitrageRoute | null> {
    try {
      if (!route.pools || route.pools.length < 1 || !route.tokens || route.tokens.length < 2) {
        return null;
      }

      const buyPool = route.pools[0];
      const sellPool = route.pools[1] || route.pools[0]; // Use same pool if only one provided
      const tokenA = route.tokens[0];
      const tokenB = route.tokens[1];

      // Find optimal arbitrage amount and calculate accurate profit
      const { optimalAmount, maxProfit } = await this.priceCalculator.findOptimalArbitrageAmount(
        buyPool,
        sellPool,
        tokenA,
        tokenB
      );

      if (maxProfit <= 0n) {
        logger.debug('No profitable arbitrage found for route');
        return null;
      }

      // Calculate gas estimate for the arbitrage
      const gasEstimate = await this.estimateArbitrageGas(route);

      // Check if profitable after gas costs
      const currentGasPrice = await this.gasEstimator.getOptimalGasPrice('fast');
      const maxFeePerGas = currentGasPrice.maxFeePerGas || ethers.parseUnits('20', 'gwei'); // Fallback

      const isProfitable = await this.priceCalculator.isProfitableAfterGas(
        maxProfit,
        gasEstimate,
        maxFeePerGas
      );

      if (!isProfitable) {
        logger.debug('Arbitrage not profitable after gas costs', {
          profit: ethers.formatEther(maxProfit),
          gasCost: ethers.formatEther(gasEstimate * maxFeePerGas)
        });
        return null;
      }

      // Return enhanced route with accurate data
      return {
        ...route,
        expectedProfit: maxProfit,
        gasEstimate,
        confidence: 85 // High confidence for validated routes
      };

    } catch (error) {
      logger.debug('Error validating arbitrage route:', error);
      return null;
    }
  }

  /**
   * Estimate gas for arbitrage transaction
   */
  private async estimateArbitrageGas(route: ArbitrageRoute): Promise<bigint> {
    try {
      // Base gas for simple swap
      let gasEstimate = 150000n; // Conservative base estimate

      // Add gas for each additional pool/hop
      if (route.pools.length > 1) {
        gasEstimate += BigInt(route.pools.length - 1) * 50000n;
      }

      // Add gas for complex protocols
      for (const pool of route.pools) {
        switch (pool.protocol) {
          case 'uniswap-v3':
            gasEstimate += 30000n; // V3 is more gas intensive
            break;
          case 'curve':
            gasEstimate += 20000n; // Curve calculations
            break;
          default:
            gasEstimate += 10000n; // V2 style DEXs
        }
      }

      return gasEstimate;
    } catch (error) {
      logger.debug('Error estimating arbitrage gas:', error);
      return 200000n; // Conservative fallback
    }
  }

  /**
   * Build multi-step arbitrage transaction for complex routes
   */
  async buildMultiStepArbitrage(
    route: ArbitrageRoute,
    options: ExecutionOptions
  ): Promise<FlashbotsBundleTransaction[]> {
    try {
      const transactions: FlashbotsBundleTransaction[] = [];

      if (route.pools.length === 1) {
        // Single pool arbitrage - not typical but handle it
        const singleTx = await this.buildArbitrageTransaction(route, options);
        if (singleTx) {
          transactions.push(singleTx);
        }
        return transactions;
      }

      // Multi-step arbitrage: buy on first DEX, sell on second DEX
      for (let i = 0; i < route.pools.length; i++) {
        const pool = route.pools[i];
        const tokenIn = route.tokens[i];
        const tokenOut = route.tokens[i + 1];

        if (!tokenIn || !tokenOut) {
          logger.debug(`Missing tokens for step ${i}`);
          continue;
        }

        // Create a single-step route for this hop
        const stepRoute: ArbitrageRoute = {
          pools: [pool],
          tokens: [tokenIn, tokenOut],
          expectedProfit: 0n, // Will be calculated
          gasEstimate: 0n,
          confidence: route.confidence
        };

        // Build swap data for this step
        const swapData = await this.swapDataBuilder.buildArbitrageSwapData(
          stepRoute,
          options,
          this.wallet.address
        );

        if (!swapData || !this.swapDataBuilder.validateSwapData(swapData)) {
          logger.debug(`Failed to build swap data for step ${i}`);
          continue;
        }

        // Estimate gas
        const gasLimit = await this.gasOptimizer.estimateGasForTransaction(
          swapData.to,
          swapData.data,
          swapData.value
        );

        // Build transaction
        const transaction: ethers.TransactionRequest = {
          to: swapData.to,
          data: swapData.data,
          value: swapData.value,
          gasLimit
        };

        const bundleTx = this.flashbotsManager.createBundleTransaction(transaction, this.wallet);
        transactions.push(bundleTx);
      }

      logger.debug(`Built ${transactions.length} transactions for multi-step arbitrage`);
      return transactions;

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.buildMultiStepArbitrage');
      return [];
    }
  }

  /**
   * Build flashloan transaction
   */
  private async buildFlashloanTransaction(
    route: FlashloanRoute,
    options: ExecutionOptions
  ): Promise<FlashbotsBundleTransaction | null> {
    try {
      // Build the actual flashloan transaction data
      const hybridContractInterface = new ethers.Interface([
        'function executeOptimalFlashloan(address asset, uint256 amount, bytes calldata params) external'
      ]);

      // Get target token from arbitrage route
      const targetToken = route.arbitrageRoute.tokens.find(token =>
        token.address !== route.flashloanToken.address
      );

      if (!targetToken) {
        throw new Error('No target token found in arbitrage route');
      }

      // Get DEX routers from pools
      const buyPool = route.arbitrageRoute.pools[0];
      const sellPool = route.arbitrageRoute.pools[1] || buyPool;

      // Map protocol to router addresses (using Sepolia addresses for Hardhat fork)
      const getRouterAddress = (protocol: string) => {
        switch (protocol) {
          case 'uniswap-v2':
            return '******************************************'; // Sepolia Uniswap V2
          case 'uniswap-v3':
            return '******************************************'; // Sepolia Uniswap V3
          default:
            return '******************************************'; // Default to V2
        }
      };

      // Encode arbitrage parameters for the flashloan callback
      const arbitrageParams = ethers.AbiCoder.defaultAbiCoder().encode(
        ['uint8', 'address', 'address', 'address', 'address', 'uint24', 'uint256'],
        [
          0, // FlashloanProvider.BALANCER (0% fees)
          route.flashloanToken.address, // tokenA
          targetToken.address,          // tokenB
          getRouterAddress(buyPool.protocol),  // buyDex
          getRouterAddress(sellPool.protocol), // sellDex
          3000, // v3Fee (0.3% for most pairs)
          ethers.parseEther('0.0001') // minProfit
        ]
      );

      // Encode the executeOptimalFlashloan function call
      const flashloanData = hybridContractInterface.encodeFunctionData('executeOptimalFlashloan', [
        route.flashloanToken.address,
        route.flashloanAmount,
        arbitrageParams
      ]);

      // Validate contract address before gas estimation
      if (!config.hybridFlashloanContract || !ethers.isAddress(config.hybridFlashloanContract)) {
        logger.error('Invalid hybrid flashloan contract address', {
          address: config.hybridFlashloanContract
        });
        return null;
      }

      // Validate flashloan data
      if (!flashloanData || flashloanData === '0x') {
        logger.error('Invalid flashloan transaction data');
        return null;
      }

      const gasLimit = await this.gasOptimizer.estimateGasForTransaction(
        config.hybridFlashloanContract,
        flashloanData,
        0
      );

      const transaction: ethers.TransactionRequest = {
        to: config.hybridFlashloanContract,
        data: flashloanData,
        value: 0,
        gasLimit
      };

      return this.flashbotsManager.createBundleTransaction(transaction, this.wallet);

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.buildFlashloanTransaction');
      return null;
    }
  }

  /**
   * Check if execution conditions are favorable
   */
  async isExecutionFavorable(options: ExecutionOptions): Promise<boolean> {
    try {
      // Check gas conditions
      const gasFavorable = await this.gasEstimator.isGasFavorable(options.maxGasCostEth);
      
      // Check network congestion
      const currentBlock = await this.provider.getBlockNumber();
      const block = await this.provider.getBlock(currentBlock);
      const gasUsageRatio = Number(block?.gasUsed || 0) / Number(block?.gasLimit || 1);
      
      // Avoid execution during high congestion (>90% block utilization)
      const congestionOk = gasUsageRatio < 0.9;

      enhancedLogger.systemStatus(`⛽ Gas Favorable: ${gasFavorable}`);
      enhancedLogger.systemStatus(`🚦 Network Congestion: ${(gasUsageRatio * 100).toFixed(1)}%`);

      return gasFavorable && congestionOk;

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.isExecutionFavorable');
      return false;
    }
  }

  /**
   * Get execution statistics
   */
  async getExecutionStats(): Promise<{
    flashbotsAvailable: boolean;
    gasEstimates: any;
    networkCongestion: number;
    recommendedStrategy: 'flashbots' | 'mempool';
  }> {
    try {
      const gasEstimates = await this.gasEstimator.getGasEstimates();
      const currentBlock = await this.provider.getBlockNumber();
      const block = await this.provider.getBlock(currentBlock);
      const networkCongestion = Number(block?.gasUsed || 0) / Number(block?.gasLimit || 1);

      const recommendedStrategy = this.flashbotsManager.isAvailable() && networkCongestion > 0.7 
        ? 'flashbots' 
        : 'mempool';

      return {
        flashbotsAvailable: this.flashbotsManager.isAvailable(),
        gasEstimates,
        networkCongestion,
        recommendedStrategy
      };

    } catch (error) {
      logger.logError(error as Error, 'FlashbotsExecutor.getExecutionStats');
      return {
        flashbotsAvailable: false,
        gasEstimates: null,
        networkCongestion: 0,
        recommendedStrategy: 'mempool'
      };
    }
  }
}
