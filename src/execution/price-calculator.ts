import { ethers } from 'ethers';
import { Token, Pool } from '../types';
import { logger } from '../utils/logger';

/**
 * Price Calculator for accurate DEX pricing and arbitrage calculations
 * Supports Uniswap V2/V3, SushiSwap, and Curve pricing formulas
 */
export class PriceCalculator {
  private provider: ethers.JsonRpcProvider;

  constructor(provider: ethers.JsonRpcProvider) {
    this.provider = provider;
  }

  /**
   * Calculate expected output amount for a swap
   */
  async getAmountOut(
    pool: Pool,
    tokenIn: Token,
    tokenOut: Token,
    amountIn: bigint
  ): Promise<bigint> {
    try {
      switch (pool.protocol) {
        case 'uniswap-v2':
          return await this.getUniswapV2AmountOut(pool, tokenIn, tokenOut, amountIn);
        case 'uniswap-v3':
          return await this.getUniswapV3AmountOut(pool, tokenIn, tokenOut, amountIn);
        case 'curve':
          return await this.getCurveAmountOut(pool, tokenIn, tokenOut, amountIn);
        default:
          logger.debug(`Unsupported protocol for pricing: ${pool.protocol}`);
          return 0n;
      }
    } catch (error) {
      logger.debug('Error calculating amount out:', error);
      return 0n;
    }
  }

  /**
   * Calculate Uniswap V2 output amount using x*y=k formula
   */
  private async getUniswapV2AmountOut(
    pool: Pool,
    tokenIn: Token,
    tokenOut: Token,
    amountIn: bigint
  ): Promise<bigint> {
    try {
      if (!pool.reserves) {
        logger.debug('Pool reserves not available for V2 calculation');
        return 0n;
      }

      const { reserve0, reserve1 } = pool.reserves;
      
      // Determine which reserve corresponds to which token
      const isToken0In = tokenIn.address.toLowerCase() === pool.token0.address.toLowerCase();
      const reserveIn = BigInt(isToken0In ? reserve0.toString() : reserve1.toString());
      const reserveOut = BigInt(isToken0In ? reserve1.toString() : reserve0.toString());

      if (reserveIn <= 0n || reserveOut <= 0n) {
        logger.debug('Invalid reserves for V2 calculation');
        return 0n;
      }

      // Apply 0.3% fee (997/1000)
      const amountInWithFee = amountIn * 997n;
      const numerator = amountInWithFee * reserveOut;
      const denominator = (reserveIn * 1000n) + amountInWithFee;

      return numerator / denominator;
    } catch (error) {
      logger.debug('Error in V2 amount calculation:', error);
      return 0n;
    }
  }

  /**
   * Calculate Uniswap V3 output amount (simplified)
   */
  private async getUniswapV3AmountOut(
    pool: Pool,
    tokenIn: Token,
    tokenOut: Token,
    amountIn: bigint
  ): Promise<bigint> {
    try {
      // For V3, we need to query the pool directly for accurate pricing
      // This is a simplified implementation
      const poolContract = new ethers.Contract(
        pool.address,
        [
          'function slot0() external view returns (uint160 sqrtPriceX96, int24 tick, uint16 observationIndex, uint16 observationCardinality, uint16 observationCardinalityNext, uint8 feeProtocol, bool unlocked)',
          'function liquidity() external view returns (uint128)',
          'function fee() external view returns (uint24)'
        ],
        this.provider
      );

      const [slot0, liquidity] = await Promise.all([
        poolContract.slot0(),
        poolContract.liquidity()
      ]);

      if (liquidity === 0n) {
        logger.debug('No liquidity in V3 pool');
        return 0n;
      }

      // Simplified calculation - in production, use proper V3 math libraries
      // This approximates the output using current price and liquidity
      const sqrtPriceX96 = slot0.sqrtPriceX96;
      const price = this.sqrtPriceX96ToPrice(sqrtPriceX96, tokenIn.decimals, tokenOut.decimals);
      
      // Apply fee (pool.fee is in basis points, e.g., 3000 = 0.3%)
      const feeMultiplier = (1000000n - BigInt(pool.fee || 3000)) / 1000000n;
      const amountOut = (amountIn * price * feeMultiplier) / (10n ** BigInt(tokenIn.decimals));

      return amountOut;
    } catch (error) {
      logger.debug('Error in V3 amount calculation:', error);
      return 0n;
    }
  }

  /**
   * Calculate Curve output amount
   */
  private async getCurveAmountOut(
    pool: Pool,
    tokenIn: Token,
    tokenOut: Token,
    amountIn: bigint
  ): Promise<bigint> {
    try {
      if (!pool.curveTokenIndices) {
        logger.debug('Curve token indices not available');
        return 0n;
      }

      const poolContract = new ethers.Contract(
        pool.address,
        ['function get_dy(int128 i, int128 j, uint256 dx) external view returns (uint256)'],
        this.provider
      );

      const { i, j } = pool.curveTokenIndices;
      const amountOut = await poolContract.get_dy(i, j, amountIn);

      return BigInt(amountOut.toString());
    } catch (error) {
      logger.debug('Error in Curve amount calculation:', error);
      return 0n;
    }
  }

  /**
   * Calculate arbitrage profit for a route
   */
  async calculateArbitrageProfit(
    buyPool: Pool,
    sellPool: Pool,
    tokenA: Token,
    tokenB: Token,
    amountIn: bigint
  ): Promise<{ profit: bigint; buyAmount: bigint; sellAmount: bigint }> {
    try {
      // Calculate amount out from buy pool
      const buyAmount = await this.getAmountOut(buyPool, tokenA, tokenB, amountIn);
      
      if (buyAmount <= 0n) {
        return { profit: 0n, buyAmount: 0n, sellAmount: 0n };
      }

      // Calculate amount out from sell pool (reverse direction)
      const sellAmount = await this.getAmountOut(sellPool, tokenB, tokenA, buyAmount);
      
      if (sellAmount <= 0n) {
        return { profit: 0n, buyAmount, sellAmount: 0n };
      }

      // Calculate profit (should be positive for profitable arbitrage)
      const profit = sellAmount > amountIn ? sellAmount - amountIn : 0n;

      return { profit, buyAmount, sellAmount };
    } catch (error) {
      logger.debug('Error calculating arbitrage profit:', error);
      return { profit: 0n, buyAmount: 0n, sellAmount: 0n };
    }
  }

  /**
   * Find optimal arbitrage amount using binary search
   */
  async findOptimalArbitrageAmount(
    buyPool: Pool,
    sellPool: Pool,
    tokenA: Token,
    tokenB: Token,
    maxAmount: bigint = ethers.parseEther('10')
  ): Promise<{ optimalAmount: bigint; maxProfit: bigint }> {
    try {
      let left = ethers.parseEther('0.01'); // Start with 0.01 ETH
      let right = maxAmount;
      let maxProfit = 0n;
      let optimalAmount = 0n;

      // Binary search for optimal amount
      for (let i = 0; i < 20; i++) { // Max 20 iterations
        const mid = (left + right) / 2n;
        
        const { profit } = await this.calculateArbitrageProfit(
          buyPool,
          sellPool,
          tokenA,
          tokenB,
          mid
        );

        if (profit > maxProfit) {
          maxProfit = profit;
          optimalAmount = mid;
        }

        // Check if we should search left or right
        const leftProfit = await this.calculateArbitrageProfit(
          buyPool,
          sellPool,
          tokenA,
          tokenB,
          (left + mid) / 2n
        );

        if (leftProfit.profit > profit) {
          right = mid;
        } else {
          left = mid;
        }

        // Break if range is too small
        if (right - left < ethers.parseEther('0.001')) {
          break;
        }
      }

      return { optimalAmount, maxProfit };
    } catch (error) {
      logger.debug('Error finding optimal arbitrage amount:', error);
      return { optimalAmount: 0n, maxProfit: 0n };
    }
  }

  /**
   * Calculate price impact for a swap
   */
  async calculatePriceImpact(
    pool: Pool,
    tokenIn: Token,
    tokenOut: Token,
    amountIn: bigint
  ): Promise<number> {
    try {
      if (pool.protocol !== 'uniswap-v2' || !pool.reserves) {
        return 0; // Only implemented for V2 for now
      }

      const { reserve0, reserve1 } = pool.reserves;
      const isToken0In = tokenIn.address.toLowerCase() === pool.token0.address.toLowerCase();
      const reserveIn = BigInt(isToken0In ? reserve0.toString() : reserve1.toString());
      const reserveOut = BigInt(isToken0In ? reserve1.toString() : reserve0.toString());

      // Current price
      const currentPrice = Number(reserveOut) / Number(reserveIn);

      // Price after swap
      const amountOut = await this.getUniswapV2AmountOut(pool, tokenIn, tokenOut, amountIn);
      const newReserveIn = reserveIn + amountIn;
      const newReserveOut = reserveOut - amountOut;
      const newPrice = Number(newReserveOut) / Number(newReserveIn);

      // Price impact as percentage
      const priceImpact = Math.abs((newPrice - currentPrice) / currentPrice) * 100;
      
      return priceImpact;
    } catch (error) {
      logger.debug('Error calculating price impact:', error);
      return 0;
    }
  }

  /**
   * Convert sqrtPriceX96 to human-readable price
   */
  private sqrtPriceX96ToPrice(
    sqrtPriceX96: bigint,
    decimals0: number,
    decimals1: number
  ): bigint {
    try {
      const Q96 = 2n ** 96n;
      const price = (sqrtPriceX96 * sqrtPriceX96) / Q96;
      
      // Adjust for token decimals
      const decimalAdjustment = 10n ** BigInt(decimals1 - decimals0);
      
      return price * decimalAdjustment / Q96;
    } catch (error) {
      logger.debug('Error converting sqrtPriceX96:', error);
      return 1n; // Default 1:1 ratio
    }
  }

  /**
   * Check if arbitrage opportunity is profitable after gas costs
   */
  async isProfitableAfterGas(
    profit: bigint,
    gasEstimate: bigint,
    gasPrice: bigint
  ): Promise<boolean> {
    try {
      const gasCost = gasEstimate * gasPrice;
      const minProfitThreshold = gasCost * 2n; // Require 2x gas cost as minimum profit
      
      return profit > minProfitThreshold;
    } catch (error) {
      logger.debug('Error checking profitability:', error);
      return false;
    }
  }
}
