import winston from 'winston';
import { config } from '../config';
import { splitScreenDashboard } from './splitScreenDashboard';
import { LogLevel, LogEntry } from '../types';

class Logger {
  private winston: winston.Logger;
  private logEntries: LogEntry[] = [];

  constructor() {
    const transports: winston.transport[] = [];

    // Only add console transport if split screen dashboard is not active
    if (!splitScreenDashboard.isActive()) {
      transports.push(
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.timestamp(),
            winston.format.printf(({ timestamp, level, message, ...meta }) => {
              const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
              return `${timestamp} [${level}]: ${message} ${metaStr}`;
            })
          )
        })
      );
    }

    if (config.logToFile) {
      transports.push(
        new winston.transports.File({
          filename: 'logs/error.log',
          level: 'error',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          )
        }),
        new winston.transports.File({
          filename: 'logs/combined.log',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          )
        })
      );
    }

    this.winston = winston.createLogger({
      level: config.logLevel,
      transports
    });
  }

  private addLogEntry(level: LogLevel, message: string, data?: any): void {
    const entry: LogEntry = {
      level,
      message,
      timestamp: Date.now(),
      data
    };

    this.logEntries.push(entry);

    // Send to split screen dashboard if active
    if (splitScreenDashboard.isActive()) {
      splitScreenDashboard.addLogEntry(entry);
    }

    // Keep only last 1000 entries
    if (this.logEntries.length > 1000) {
      this.logEntries = this.logEntries.slice(-1000);
    }
  }

  error(message: string, data?: any): void {
    this.winston.error(message, data);
    this.addLogEntry(LogLevel.ERROR, message, data);
  }

  warn(message: string, data?: any): void {
    this.winston.warn(message, data);
    this.addLogEntry(LogLevel.WARN, message, data);
  }

  info(message: string, data?: any): void {
    this.winston.info(message, data);
    this.addLogEntry(LogLevel.INFO, message, data);
  }

  debug(message: string, data?: any): void {
    this.winston.debug(message, data);
    this.addLogEntry(LogLevel.DEBUG, message, data);
  }

  logTransaction(hash: string, type: string, profit?: string, gasUsed?: string): void {
    this.info(`Transaction ${type}`, {
      hash,
      type,
      profit,
      gasUsed,
      timestamp: new Date().toISOString()
    });
  }

  logOpportunity(opportunity: any): void {
    this.info('MEV Opportunity Detected', {
      type: opportunity.type,
      estimatedProfit: opportunity.estimatedProfit?.toString(),
      confidence: opportunity.confidence,
      victimTx: opportunity.victimTx?.hash
    });
  }

  logBundle(bundleHash: string, transactions: number, profit?: string): void {
    this.info('Bundle Submitted', {
      bundleHash,
      transactions,
      profit,
      timestamp: new Date().toISOString()
    });
  }

  logError(error: Error, context?: string): void {
    this.error(`${context ? `[${context}] ` : ''}${error.message}`, {
      stack: error.stack,
      context
    });
  }

  getRecentLogs(count: number = 100): LogEntry[] {
    return this.logEntries.slice(-count);
  }

  getLogsByLevel(level: LogLevel, count: number = 100): LogEntry[] {
    return this.logEntries
      .filter(entry => entry.level === level)
      .slice(-count);
  }

  /**
   * Update logger configuration when split screen dashboard starts/stops
   */
  updateForSplitScreen(splitScreenActive: boolean): void {
    // Clear existing transports
    this.winston.clear();

    const transports: winston.transport[] = [];

    // Only add console transport if split screen dashboard is not active
    if (!splitScreenActive) {
      transports.push(
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.timestamp(),
            winston.format.printf(({ timestamp, level, message, ...meta }) => {
              const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
              return `${timestamp} [${level}]: ${message} ${metaStr}`;
            })
          )
        })
      );
    }

    // Add file transports if enabled
    if (config.logToFile) {
      transports.push(
        new winston.transports.File({
          filename: 'logs/error.log',
          level: 'error',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          )
        }),
        new winston.transports.File({
          filename: 'logs/combined.log',
          format: winston.format.combine(
            winston.format.timestamp(),
            winston.format.json()
          )
        })
      );
    }

    // Add all transports to winston
    transports.forEach(transport => this.winston.add(transport));
  }
}

export const logger = new Logger();
