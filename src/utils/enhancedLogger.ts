import chalk from 'chalk';
import { splitScreenDashboard } from './splitScreenDashboard';
import { LogLevel } from '../types';
import { ethers } from 'ethers';
import { config } from '../config';

export class EnhancedLogger {
  private static instance: EnhancedLogger;
  private startTime: number;

  constructor() {
    this.startTime = Date.now();
  }

  static getInstance(): EnhancedLogger {
    if (!EnhancedLogger.instance) {
      EnhancedLogger.instance = new EnhancedLogger();
    }
    return EnhancedLogger.instance;
  }

  private getTimestamp(): string {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    const ms = String(now.getMilliseconds()).padStart(6, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${ms}`;
  }

  private formatCurrency(amount: string | number | bigint, symbol: string = '$'): string {
    const num = typeof amount === 'bigint' ? Number(ethers.formatEther(amount)) : Number(amount);
    return `${num.toFixed(8)} ${symbol}`;
  }

  /**
   * Check if a log level should be shown based on configured log level
   */
  private shouldShowLogLevel(level: LogLevel): boolean {
    const configuredLevel = config.logLevel.toLowerCase();
    const levelHierarchy = {
      'error': 0,
      'warn': 1,
      'info': 2,
      'debug': 3
    };

    const currentLevelValue = levelHierarchy[level.toLowerCase() as keyof typeof levelHierarchy];
    const configuredLevelValue = levelHierarchy[configuredLevel as keyof typeof levelHierarchy];

    // If level is not recognized, default to showing it
    if (currentLevelValue === undefined || configuredLevelValue === undefined) {
      return true;
    }

    // Show log if its level is at or above the configured level
    return currentLevelValue <= configuredLevelValue;
  }

  /**
   * Helper method to handle console output - routes to split screen if active
   */
  private logToConsoleOrSplitScreen(message: string, level: LogLevel = LogLevel.INFO, data?: any): void {
    // Filter logs based on configured log level
    if (!this.shouldShowLogLevel(level)) {
      return;
    }

    if (splitScreenDashboard.isActive()) {
      splitScreenDashboard.addLog(level, message, data);
    } else {
      console.log(message);
    }
  }

  // System status logs
  systemStatus(message: string, data?: any): void {
    // Filter logs based on configured log level
    if (!this.shouldShowLogLevel(LogLevel.INFO)) {
      return;
    }

    const timestamp = chalk.gray(this.getTimestamp());
    const status = chalk.blue('Current');
    const alpha = chalk.yellow('ALPHA');
    const liquidity = data?.liquidity ? chalk.green(`Liquidity = ${this.formatCurrency(data.liquidity)}`) : '';

    const logMessage = `${timestamp} ${status} ${alpha} ${liquidity} ${chalk.white(message)}`;

    // Send to split screen dashboard if active, otherwise use console
    if (splitScreenDashboard.isActive()) {
      splitScreenDashboard.addLog(LogLevel.INFO, `SYSTEM: ${message}`, data);
    } else {
      console.log(logMessage);
    }
  }

  // Bot status and configuration
  botStatus(message: string, data?: any): void {
    // Filter logs based on configured log level
    if (!this.shouldShowLogLevel(LogLevel.INFO)) {
      return;
    }

    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.cyan('Bot');

    const logMessage = data?.seconds
      ? `${timestamp} ${prefix} ${chalk.yellow(`will wait ${data.seconds} seconds before buy`)} ${chalk.white(message)}`
      : `${timestamp} ${prefix} ${chalk.white(message)}`;

    // Send to split screen dashboard if active, otherwise use console
    if (splitScreenDashboard.isActive()) {
      splitScreenDashboard.addLog(LogLevel.INFO, `BOT: ${message}`, data);
    } else {
      console.log(logMessage);
    }
  }

  // Transaction monitoring
  transactionHash(hash: string, description?: string): void {
    // Filter logs based on configured log level
    if (!this.shouldShowLogLevel(LogLevel.INFO)) {
      return;
    }

    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.magenta('Transaction Hash');
    const hashFormatted = chalk.cyan(hash);
    const desc = description ? chalk.gray(description) : '';
    const logMessage = `${timestamp} ${prefix} = ${hashFormatted} ${desc}`;

    // Send to split screen dashboard if active, otherwise use console
    if (splitScreenDashboard.isActive()) {
      splitScreenDashboard.addLog(LogLevel.INFO, `TRANSACTION: ${hash} ${description || ''}`);
    } else {
      console.log(logMessage);
    }
  }

  // Transaction confirmation
  transactionConfirm(message: string, waitTime?: number): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.yellow('Checking for Transaction confirmation');
    const wait = waitTime ? chalk.gray(`(waiting ${waitTime} seconds)...`) : '';
    const logMessage = `${timestamp} ${prefix} ${wait}`;

    // Send to split screen dashboard if active, otherwise use console
    if (splitScreenDashboard.isActive()) {
      splitScreenDashboard.addLog(LogLevel.INFO, `CONFIRMATION: ${message} ${waitTime ? `(${waitTime}s)` : ''}`);
    } else {
      console.log(logMessage);
    }
  }

  // Success messages
  success(message: string): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.green('SUCCESS');
    const logMessage = `${timestamp} ${prefix} --> ${chalk.green(message)}`;

    // Send to split screen dashboard if active, otherwise use console
    if (splitScreenDashboard.isActive()) {
      splitScreenDashboard.addLog(LogLevel.INFO, `SUCCESS: ${message}`);
    } else {
      console.log(logMessage);
    }
  }

  // Wallet balance
  walletBalance(balance: string | number, currency: string = 'ETH'): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('Current Wallet Balance is:');
    const amount = chalk.yellow(this.formatCurrency(balance, currency));
    const logMessage = `${timestamp} ${prefix} ${amount}`;

    // Send to split screen dashboard if active, otherwise use console
    if (splitScreenDashboard.isActive()) {
      splitScreenDashboard.addLog(LogLevel.INFO, `WALLET: Balance ${this.formatCurrency(balance, currency)}`);
    } else {
      console.log(logMessage);
    }
  }

  // Token purchase
  tokenPurchase(amount: string | number, tokenSymbol: string): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const message = chalk.green(`You bought ${this.formatCurrency(amount)} ${tokenSymbol} tokens`);

    this.logToConsoleOrSplitScreen(`${timestamp} ${message}`, LogLevel.INFO, { amount, tokenSymbol });
  }

  // Approval status
  approvalStatus(tokenAddress: string, status: string): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.yellow('Checking Approval Status');
    const address = chalk.cyan(tokenAddress);
    const statusMsg = status === 'approved' ?
      chalk.green('Token is already approved --> You can use this token') :
      chalk.red('Token needs approval');

    this.logToConsoleOrSplitScreen(`${timestamp} ${prefix} ${address}`, LogLevel.INFO, { tokenAddress, status });
    this.logToConsoleOrSplitScreen(`${timestamp} ${statusMsg}`, LogLevel.INFO);
  }

  // Sell signals
  sellSignal(type: string = 'Sell Signal Found'): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const signal = chalk.red(`${type} --> ${type} --> ${type}`);

    this.logToConsoleOrSplitScreen(`${timestamp} ${signal}`, LogLevel.INFO, { type });
  }

  // Price information
  sellPrice(price: string | number): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.yellow('Sell price in');
    const priceFormatted = chalk.white(`: ${price}`);
    const dots = chalk.gray('................................................................');

    this.logToConsoleOrSplitScreen(`${timestamp} ${prefix} ${priceFormatted}`, LogLevel.INFO, { price });
    this.logToConsoleOrSplitScreen(`${timestamp} ${dots}`, LogLevel.DEBUG);
  }

  // Liquidity detection
  liquidityDetected(amount: string | number): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('Current');
    const liquidity = chalk.green(`Liquidity = ${this.formatCurrency(amount)}`);
    const detection = chalk.green('1 --> Enough liquidity detected : let\'s go!');

    this.logToConsoleOrSplitScreen(`${timestamp} ${prefix} ${liquidity}`, LogLevel.INFO, { amount });
    this.logToConsoleOrSplitScreen(`${timestamp} ${detection}`, LogLevel.INFO);
  }

  // Order placement
  placingOrder(type: string = 'Sell'): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const order = chalk.yellow(`Placing ${type} Order`);

    this.logToConsoleOrSplitScreen(`${timestamp} ${order}`, LogLevel.INFO, { type });
  }

  // MEV specific logs
  victimTransaction(method: string, tokenIn: string, tokenOut: string, amount: string): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.red('🔍 Victim Transaction Detected:');
    const methodFormatted = chalk.yellow(method);
    const tokens = chalk.cyan(`${tokenIn} -> ${tokenOut}`);
    const amountFormatted = chalk.green(this.formatCurrency(amount));

    this.logToConsoleOrSplitScreen(`${timestamp} ${prefix} ${methodFormatted}`, LogLevel.INFO, { method, tokenIn, tokenOut, amount });
    this.logToConsoleOrSplitScreen(`${timestamp} ${chalk.gray('Tokens:')} ${tokens} ${chalk.gray('Amount:')} ${amountFormatted}`, LogLevel.INFO);
  }

  // Bundle simulation
  bundleSimulation(result: 'success' | 'error', details?: string): void {
    const timestamp = chalk.gray(this.getTimestamp());

    if (result === 'success') {
      const prefix = chalk.green('✅ Bundle Simulation:');
      const message = chalk.green('SUCCESS --> Bundle is profitable');
      this.logToConsoleOrSplitScreen(`${timestamp} ${prefix} ${message}`, LogLevel.INFO, { result, details });
    } else {
      const prefix = chalk.red('❌ Bundle Simulation:');
      const message = chalk.red(`ERROR --> ${details || 'Simulation failed'}`);
      this.logToConsoleOrSplitScreen(`${timestamp} ${prefix} ${message}`, LogLevel.ERROR, { result, details });
    }
  }

  // Profit calculation
  profitCalculation(profit: string | number, profitable: boolean): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('Profit Analysis:');
    const profitFormatted = this.formatCurrency(profit);

    if (profitable) {
      const message = chalk.green(`PROFITABLE --> Expected profit: ${profitFormatted}`);
      this.logToConsoleOrSplitScreen(`${timestamp} ${prefix} ${message}`, LogLevel.INFO, { profit, profitable });
    } else {
      const message = chalk.red(`NOT PROFITABLE --> Loss: ${profitFormatted}`);
      this.logToConsoleOrSplitScreen(`${timestamp} ${prefix} ${message}`, LogLevel.WARN, { profit, profitable });
    }
  }

  // Bundle submission
  bundleSubmission(blockNumber: number, txCount: number): void {
    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('🚀 Bundle Submitted:');
    const details = chalk.yellow(`Block ${blockNumber}, ${txCount} transactions`);

    this.logToConsoleOrSplitScreen(`${timestamp} ${prefix} ${details}`, LogLevel.INFO, { blockNumber, txCount });
  }

  // Error logging with color
  error(message: string, error?: any): void {
    // Filter logs based on configured log level
    if (!this.shouldShowLogLevel(LogLevel.ERROR)) {
      return;
    }

    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.red('❌ ERROR:');
    const errorMsg = chalk.red(message);

    // Send to split screen dashboard if active, otherwise use console
    if (splitScreenDashboard.isActive()) {
      splitScreenDashboard.addLog(LogLevel.ERROR, message, error);
    } else {
      console.log(`${timestamp} ${prefix} ${errorMsg}`);
      if (error) {
        console.log(`${timestamp} ${chalk.gray('Details:')} ${chalk.red(error.message || error)}`);
      }
    }
  }

  // Warning logging
  warning(message: string): void {
    // Filter logs based on configured log level
    if (!this.shouldShowLogLevel(LogLevel.WARN)) {
      return;
    }

    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.yellow('⚠️  WARNING:');
    const warnMsg = chalk.yellow(message);
    const logMessage = `${timestamp} ${prefix} ${warnMsg}`;

    // Send to split screen dashboard if active, otherwise use console
    if (splitScreenDashboard.isActive()) {
      splitScreenDashboard.addLog(LogLevel.WARN, message);
    } else {
      console.log(logMessage);
    }
  }

  // Info logging
  info(message: string, data?: any): void {
    // Filter logs based on configured log level
    if (!this.shouldShowLogLevel(LogLevel.INFO)) {
      return;
    }

    const timestamp = chalk.gray(this.getTimestamp());
    const prefix = chalk.blue('ℹ️  INFO:');
    const infoMsg = chalk.white(message);

    // Send to split screen dashboard if active, otherwise use console
    if (splitScreenDashboard.isActive()) {
      splitScreenDashboard.addLog(LogLevel.INFO, message, data);
    } else {
      console.log(`${timestamp} ${prefix} ${infoMsg}`);
      if (data) {
        console.log(`${timestamp} ${chalk.gray('Data:')} ${chalk.cyan(JSON.stringify(data, null, 2))}`);
      }
    }
  }

  // Separator line
  separator(): void {
    const line = chalk.gray('═'.repeat(80));
    this.logToConsoleOrSplitScreen(line, LogLevel.DEBUG);
  }

  // Clear screen
  clear(): void {
    // Only clear console if split screen is not active
    if (!splitScreenDashboard.isActive()) {
      console.clear();
    }
  }
}

// Export singleton instance
export const enhancedLogger = EnhancedLogger.getInstance();
