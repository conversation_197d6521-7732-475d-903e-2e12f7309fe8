import { ethers } from 'ethers';
import { Pool, Token } from '../types';
import { ADDRESSES } from '../config';
import { logger } from '../utils/logger';

/**
 * Curve Finance integration for low-slippage stablecoin swaps
 * Focuses on the 3pool (USDC/DAI/USDT) for optimal stablecoin arbitrage
 */
export class CurveSwapper {
  private provider: ethers.JsonRpcProvider;
  private wallet: ethers.Wallet;

  // Curve 3pool ABI
  private static readonly CURVE_3POOL_ABI = [
    'function balances(uint256) view returns (uint256)',
    'function get_dy(int128 i, int128 j, uint256 dx) view returns (uint256)',
    'function exchange(int128 i, int128 j, uint256 dx, uint256 min_dy) returns (uint256)',
    'function get_virtual_price() view returns (uint256)',
    'function A() view returns (uint256)',
    'function fee() view returns (uint256)'
  ];

  constructor(provider: ethers.JsonRpcProvider, wallet: ethers.Wallet) {
    this.provider = provider;
    this.wallet = wallet;
  }

  /**
   * Get expected output amount for a Curve swap
   */
  async getAmountOut(
    pool: Pool,
    tokenIn: Token,
    tokenOut: Token,
    amountIn: bigint
  ): Promise<bigint> {
    try {
      if (!pool.curveTokenIndices) {
        throw new Error('Pool missing Curve token indices');
      }

      const contract = new ethers.Contract(
        pool.address,
        CurveSwapper.CURVE_3POOL_ABI,
        this.provider
      );

      const { i, j } = pool.curveTokenIndices;
      
      // Get expected output amount
      const amountOut = await contract.get_dy(i, j, amountIn);
      
      return BigInt(amountOut.toString());
    } catch (error) {
      logger.logError(error as Error, 'CurveSwapper.getAmountOut');
      return BigInt(0);
    }
  }

  /**
   * Execute a Curve swap
   */
  async executeSwap(
    pool: Pool,
    tokenIn: Token,
    tokenOut: Token,
    amountIn: bigint,
    minAmountOut: bigint
  ): Promise<bigint> {
    try {
      if (!pool.curveTokenIndices) {
        throw new Error('Pool missing Curve token indices');
      }

      const contract = new ethers.Contract(
        pool.address,
        CurveSwapper.CURVE_3POOL_ABI,
        this.wallet
      );

      const { i, j } = pool.curveTokenIndices;

      // Approve token spending
      const tokenContract = new ethers.Contract(
        tokenIn.address,
        ['function approve(address spender, uint256 amount) returns (bool)'],
        this.wallet
      );

      const approveTx = await tokenContract.approve(pool.address, amountIn);
      await approveTx.wait();

      // Execute the swap
      const swapTx = await contract.exchange(i, j, amountIn, minAmountOut);
      const receipt = await swapTx.wait();

      // Parse the actual amount received from logs
      const amountOut = await this.parseSwapOutput(receipt, tokenOut.address);
      
      return amountOut;
    } catch (error) {
      logger.logError(error as Error, 'CurveSwapper.executeSwap');
      throw error;
    }
  }

  /**
   * Calculate price impact for a Curve swap
   */
  async calculatePriceImpact(
    pool: Pool,
    tokenIn: Token,
    tokenOut: Token,
    amountIn: bigint
  ): Promise<number> {
    try {
      if (!pool.curveTokenIndices) {
        return 0;
      }

      const contract = new ethers.Contract(
        pool.address,
        CurveSwapper.CURVE_3POOL_ABI,
        this.provider
      );

      const { i, j } = pool.curveTokenIndices;

      // Get expected output for the trade
      const amountOut = await contract.get_dy(i, j, amountIn);
      
      // Get expected output for a small trade (to estimate fair price)
      const smallAmount = ethers.parseUnits('1', tokenIn.decimals);
      const smallAmountOut = await contract.get_dy(i, j, smallAmount);
      
      // Calculate fair price (price for small trade)
      const fairPrice = Number(smallAmountOut) / Number(smallAmount);
      
      // Calculate actual price for this trade
      const actualPrice = Number(amountOut) / Number(amountIn);
      
      // Price impact = (fair_price - actual_price) / fair_price
      const priceImpact = (fairPrice - actualPrice) / fairPrice;
      
      return Math.max(0, priceImpact * 100); // Return as percentage
    } catch (error) {
      logger.logError(error as Error, 'CurveSwapper.calculatePriceImpact');
      return 0;
    }
  }

  /**
   * Get Curve pool information
   */
  async getPoolInfo(poolAddress: string): Promise<{
    virtualPrice: bigint;
    amplificationParameter: bigint;
    fee: bigint;
    balances: bigint[];
  }> {
    try {
      const contract = new ethers.Contract(
        poolAddress,
        CurveSwapper.CURVE_3POOL_ABI,
        this.provider
      );

      const [virtualPrice, amplificationParameter, fee] = await Promise.all([
        contract.get_virtual_price(),
        contract.A(),
        contract.fee()
      ]);

      // Get balances for all 3 tokens in the pool
      const balances = await Promise.all([
        contract.balances(0), // DAI
        contract.balances(1), // USDC
        contract.balances(2)  // USDT
      ]);

      return {
        virtualPrice: BigInt(virtualPrice.toString()),
        amplificationParameter: BigInt(amplificationParameter.toString()),
        fee: BigInt(fee.toString()),
        balances: balances.map(b => BigInt(b.toString()))
      };
    } catch (error) {
      logger.logError(error as Error, 'CurveSwapper.getPoolInfo');
      throw error;
    }
  }

  /**
   * Check if a token pair is supported by Curve 3pool
   */
  static isSupportedPair(token0Symbol: string, token1Symbol: string): boolean {
    const supportedTokens = ['USDC', 'DAI', 'USDT'];
    return supportedTokens.includes(token0Symbol) && 
           supportedTokens.includes(token1Symbol) && 
           token0Symbol !== token1Symbol;
  }

  /**
   * Get the optimal route for stablecoin arbitrage
   * Returns the best path considering Curve's low slippage
   */
  async getOptimalStablecoinRoute(
    fromToken: Token,
    toToken: Token,
    amount: bigint
  ): Promise<{
    useDirectSwap: boolean;
    intermediateToken?: Token;
    expectedOutput: bigint;
    priceImpact: number;
  }> {
    try {
      if (!CurveSwapper.isSupportedPair(fromToken.symbol, toToken.symbol)) {
        throw new Error(`Unsupported token pair: ${fromToken.symbol}/${toToken.symbol}`);
      }

      const pool = await this.getCurve3Pool();
      if (!pool) {
        throw new Error('Curve 3pool not available');
      }

      // For stablecoins, direct swap is usually optimal due to Curve's design
      const expectedOutput = await this.getAmountOut(pool, fromToken, toToken, amount);
      const priceImpact = await this.calculatePriceImpact(pool, fromToken, toToken, amount);

      return {
        useDirectSwap: true,
        expectedOutput,
        priceImpact
      };
    } catch (error) {
      logger.logError(error as Error, 'CurveSwapper.getOptimalStablecoinRoute');
      throw error;
    }
  }

  private async getCurve3Pool(): Promise<Pool | null> {
    try {
      if (!ADDRESSES.CURVE_3POOL || ADDRESSES.CURVE_3POOL === '') {
        return null;
      }

      // This would typically come from the PoolManager
      // For now, return a basic pool structure
      return {
        address: ADDRESSES.CURVE_3POOL,
        token0: { address: ADDRESSES.USDC, symbol: 'USDC', decimals: 6, name: 'USD Coin' },
        token1: { address: ADDRESSES.DAI, symbol: 'DAI', decimals: 18, name: 'Dai Stablecoin' },
        fee: 4, // 0.04%
        protocol: 'curve',
        curveTokenIndices: { i: 1, j: 0 } // USDC to DAI
      };
    } catch (error) {
      logger.logError(error as Error, 'CurveSwapper.getCurve3Pool');
      return null;
    }
  }

  private async parseSwapOutput(receipt: any, tokenAddress: string): Promise<bigint> {
    try {
      // Parse Transfer events to get the actual amount received
      const transferTopic = ethers.id('Transfer(address,address,uint256)');
      
      for (const log of receipt.logs) {
        if (log.topics[0] === transferTopic && log.address.toLowerCase() === tokenAddress.toLowerCase()) {
          // Decode the transfer amount
          const amount = ethers.AbiCoder.defaultAbiCoder().decode(['uint256'], log.data)[0];
          return BigInt(amount.toString());
        }
      }
      
      return BigInt(0);
    } catch (error) {
      logger.logError(error as Error, 'CurveSwapper.parseSwapOutput');
      return BigInt(0);
    }
  }
}
