import { Worker, isMainThread, parentPort, workerData } from 'worker_threads';
import { ethers } from 'ethers';
import { ArbitrageScanTask, TriangularArbitrageTask, WorkerMessage, ArbitrageScanResult } from './types';
import { Token, Pool, ArbitrageRoute } from '../types';
import { PoolManager } from '../dex/pools';
import { GasOptimizer } from '../gas/optimizer';
import { config } from '../config';

class ArbitrageWorker {
  private workerId: number;
  private poolManager: PoolManager;
  private gasOptimizer: GasOptimizer;
  private readonly MIN_PROFIT_THRESHOLD = 0.001;
  private tasksProcessed = 0;
  private startTime = Date.now();

  constructor(workerId: number) {
    this.workerId = workerId;
    this.poolManager = new PoolManager();
    this.gasOptimizer = new GasOptimizer();
  }

  async initialize(): Promise<void> {
    // Initialize worker-specific resources
    // Note: PoolManager doesn't have initialize method, so we'll skip this
    this.sendMessage({
      type: 'result',
      data: { initialized: true },
      timestamp: Date.now()
    });
  }

  async processTask(task: ArbitrageScanTask | TriangularArbitrageTask): Promise<void> {
    const startTime = Date.now();

    try {
      let result: any;

      switch (task.type) {
        case 'arbitrage-scan':
          result = await this.processArbitrageScan(task as ArbitrageScanTask);
          break;
        case 'triangular-arbitrage':
          result = await this.processTriangularArbitrage(task as TriangularArbitrageTask);
          break;
        default:
          throw new Error(`Unknown task type: ${(task as any).type}`);
      }

      const processingTime = Date.now() - startTime;
      this.tasksProcessed++;

      this.sendMessage({
        type: 'result',
        taskId: task.id,
        data: result,
        timestamp: Date.now()
      });

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      this.sendMessage({
        type: 'error',
        taskId: task.id,
        data: {
          error: (error as Error).message,
          stack: (error as Error).stack
        },
        timestamp: Date.now()
      });
    }
  }

  private async processArbitrageScan(task: ArbitrageScanTask): Promise<ArbitrageScanResult['data']> {
    const opportunities: ArbitrageRoute[] = [];
    let processedPairs = 0;
    let profitableCount = 0;

    for (const pair of task.data.tokenPairs) {
      try {
        // Check V2/V3 arbitrage for this pair
        const v2v3Arbitrage = await this.findV2V3Arbitrage(
          pair.token0, 
          pair.token1, 
          task.data.minProfitThreshold,
          task.data.gasPrice
        );

        if (v2v3Arbitrage) {
          opportunities.push(v2v3Arbitrage);
          profitableCount++;
        }

        processedPairs++;
      } catch (error) {
        // Log error but continue processing other pairs
        console.error(`Worker ${this.workerId}: Error processing pair ${pair.token0.symbol}/${pair.token1.symbol}:`, error);
      }
    }

    // Sort opportunities by expected profit
    opportunities.sort((a, b) =>
      Number(BigInt(b.expectedProfit.toString()) - BigInt(a.expectedProfit.toString()))
    );

    return {
      opportunities: opportunities.slice(0, 5), // Return top 5 from this worker
      processedPairs,
      profitableCount
    };
  }

  private async processTriangularArbitrage(task: TriangularArbitrageTask): Promise<ArbitrageRoute[]> {
    const routes: ArbitrageRoute[] = [];
    const { token0, token1, wethToken, minProfitThreshold } = task.data;

    try {
      // Get all required pools for triangular arbitrage
      const pool1 = await this.poolManager.getPool(token0.address, wethToken.address, 'uniswap-v2');
      const pool2 = await this.poolManager.getPool(wethToken.address, token1.address, 'uniswap-v2');
      const pool3 = await this.poolManager.getPool(token1.address, token0.address, 'uniswap-v2');

      if (!pool1 || !pool2 || !pool3) {
        return routes;
      }

      // Calculate triangular arbitrage profitability
      const testAmount = ethers.parseUnits('1', token0.decimals);
      const step1Out = await this.calculateAmountOut(pool1, testAmount, token0, wethToken);
      const step2Out = await this.calculateAmountOut(pool2, step1Out, wethToken, token1);
      const step3Out = await this.calculateAmountOut(pool3, step2Out, token1, token0);

      const profit = BigInt(step3Out.toString()) - testAmount;
      const profitPercentage = Number(profit * BigInt(10000) / testAmount) / 100;

      if (profitPercentage > minProfitThreshold) {
        const optimalAmount = await this.calculateOptimalTriangularAmount(
          [pool1, pool2, pool3],
          [token0, wethToken, token1, token0]
        );

        const gasEstimate = await this.estimateTriangularGasCost([pool1, pool2, pool3], optimalAmount);
        const expectedProfit = await this.calculateTriangularProfit(
          [pool1, pool2, pool3],
          [token0, wethToken, token1, token0],
          optimalAmount,
          gasEstimate
        );

        if (BigInt(expectedProfit.toString()) > BigInt(0)) {
          routes.push({
            pools: [pool1, pool2, pool3],
            tokens: [token0, wethToken, token1, token0],
            expectedProfit,
            gasEstimate,
            confidence: this.calculateArbitrageConfidence(profitPercentage, expectedProfit)
          });
        }
      }

      return routes;
    } catch (error) {
      console.error(`Worker ${this.workerId}: Error in triangular arbitrage:`, error);
      return routes;
    }
  }

  private async findV2V3Arbitrage(
    token0: Token, 
    token1: Token, 
    minProfitThreshold: number,
    gasPrice: string
  ): Promise<ArbitrageRoute | null> {
    try {
      // Get pools from both V2 and V3
      const v2Pool = await this.poolManager.getPool(token0.address, token1.address, 'uniswap-v2');
      const v3Pool = await this.poolManager.getPool(token0.address, token1.address, 'uniswap-v3', 3000);

      if (!v2Pool || !v3Pool) {
        return null;
      }

      // Calculate prices on both pools
      const v2Price = this.calculatePoolPrice(v2Pool, token0, token1);
      const v3Price = this.calculatePoolPrice(v3Pool, token0, token1);

      if (!v2Price || !v3Price || v2Price <= 0 || v3Price <= 0) {
        return null;
      }

      // Check for price difference
      const priceDifference = Math.abs(v2Price - v3Price) / Math.min(v2Price, v3Price);

      if (priceDifference < minProfitThreshold) {
        return null;
      }

      // Determine direction (buy low, sell high)
      const buyPool = v2Price < v3Price ? v2Pool : v3Pool;
      const sellPool = v2Price < v3Price ? v3Pool : v2Pool;

      // Calculate optimal amount
      const optimalAmount = await this.calculateOptimalArbitrageAmount(buyPool, sellPool, token0, token1);

      if (optimalAmount === BigInt(0)) {
        return null;
      }

      // Estimate gas costs
      const gasEstimate = await this.estimateArbitrageGasCost(buyPool, sellPool, optimalAmount, gasPrice);

      // Calculate expected profit
      const expectedProfit = await this.calculateArbitrageProfit(
        buyPool, sellPool, token0, token1, optimalAmount, gasEstimate
      );

      // Use minimum profit threshold
      const minProfitWei = ethers.parseEther('0.001');
      if (BigInt(expectedProfit.toString()) <= minProfitWei) {
        return null;
      }

      return {
        pools: [buyPool, sellPool],
        tokens: [token0, token1],
        expectedProfit,
        gasEstimate,
        confidence: this.calculateArbitrageConfidence(priceDifference, expectedProfit)
      };
    } catch (error) {
      return null;
    }
  }

  private calculatePoolPrice(pool: Pool, token0: Token, token1: Token): number | null {
    try {
      if (pool.protocol === 'uniswap-v2' && pool.reserves) {
        const actualToken0 = pool.token0;
        const isToken0First = actualToken0.address.toLowerCase() === token0.address.toLowerCase();

        const token0Reserve = isToken0First ? pool.reserves.reserve0 : pool.reserves.reserve1;
        const token1Reserve = isToken0First ? pool.reserves.reserve1 : pool.reserves.reserve0;

        const reserve0 = Number(ethers.formatUnits(token0Reserve, token0.decimals));
        const reserve1 = Number(ethers.formatUnits(token1Reserve, token1.decimals));

        if (reserve0 === 0) return null;
        return reserve1 / reserve0;
      } else if (pool.protocol === 'uniswap-v3' && pool.tick !== undefined) {
        const actualToken0 = pool.token0;
        const isToken0First = actualToken0.address.toLowerCase() === token0.address.toLowerCase();

        const tickNumber = Number(pool.tick);
        let rawPrice = Math.pow(1.0001, tickNumber);

        if (!isToken0First) {
          rawPrice = 1 / rawPrice;
        }

        const decimalsAdjustment = Math.pow(10, token0.decimals - token1.decimals);
        return rawPrice * decimalsAdjustment;
      }

      return null;
    } catch (error) {
      return null;
    }
  }

  private async calculateOptimalArbitrageAmount(
    buyPool: Pool,
    sellPool: Pool,
    buyToken: Token,
    sellToken: Token
  ): Promise<ethers.BigNumberish> {
    // Simplified optimal amount calculation for worker
    const maxAmount = ethers.parseUnits('10', buyToken.decimals);
    let optimalAmount = BigInt(0);
    let maxProfit = BigInt(0);

    // Test different amounts to find optimal
    for (let i = 1; i <= 10; i++) {
      const testAmount = (maxAmount * BigInt(i)) / BigInt(10);

      const buyAmountOut = await this.calculateAmountOut(buyPool, testAmount, buyToken, sellToken);
      const sellAmountOut = await this.calculateAmountOut(sellPool, buyAmountOut, sellToken, buyToken);

      const profit = BigInt(sellAmountOut.toString()) - testAmount;

      if (profit > maxProfit) {
        maxProfit = profit;
        optimalAmount = testAmount;
      }
    }

    return optimalAmount;
  }

  private async calculateAmountOut(
    pool: Pool,
    amountIn: ethers.BigNumberish,
    tokenIn: Token,
    tokenOut: Token
  ): Promise<ethers.BigNumberish> {
    if (pool.protocol === 'uniswap-v2' && pool.reserves) {
      const amountInBN = typeof amountIn === 'string' ? ethers.parseUnits(amountIn, tokenIn.decimals) : BigInt(amountIn.toString());
      const reserve0 = BigInt(pool.reserves.reserve0.toString());
      const reserve1 = BigInt(pool.reserves.reserve1.toString());

      const isToken0 = pool.token0.address.toLowerCase() === tokenIn.address.toLowerCase();
      const reserveIn = isToken0 ? reserve0 : reserve1;
      const reserveOut = isToken0 ? reserve1 : reserve0;

      // Uniswap V2 formula with 0.3% fee
      const amountInWithFee = amountInBN * BigInt(997);
      const numerator = amountInWithFee * reserveOut;
      const denominator = reserveIn * BigInt(1000) + amountInWithFee;

      return numerator / denominator;
    }

    // Simplified for V3
    return typeof amountIn === 'string' ? ethers.parseUnits(amountIn, tokenOut.decimals) : amountIn;
  }

  private async estimateArbitrageGasCost(
    buyPool: Pool,
    sellPool: Pool,
    amount: ethers.BigNumberish,
    gasPrice: string
  ): Promise<ethers.BigNumberish> {
    // Simplified gas estimation for worker
    const gasPerSwap = BigInt(150000); // Estimated gas per swap
    const gasPriceBN = BigInt(gasPrice);
    return gasPerSwap * BigInt(2) * gasPriceBN; // Two swaps
  }

  private async calculateArbitrageProfit(
    buyPool: Pool,
    sellPool: Pool,
    buyToken: Token,
    sellToken: Token,
    amount: ethers.BigNumberish,
    gasCost: ethers.BigNumberish
  ): Promise<ethers.BigNumberish> {
    const buyAmountOut = await this.calculateAmountOut(buyPool, amount, buyToken, sellToken);
    const sellAmountOut = await this.calculateAmountOut(sellPool, buyAmountOut, sellToken, buyToken);

    const grossProfit = BigInt(sellAmountOut.toString()) - BigInt(amount.toString());
    return grossProfit - BigInt(gasCost.toString());
  }

  private calculateArbitrageConfidence(profitPercentage: number, expectedProfit: ethers.BigNumberish): number {
    let confidence = 0;

    // Profit percentage factor
    confidence += Math.min(profitPercentage * 20, 50);

    // Absolute profit factor
    const profitEth = Number(ethers.formatEther(expectedProfit));
    confidence += Math.min(profitEth * 25, 30);

    // Base confidence for arbitrage
    confidence += 20;

    return Math.min(confidence, 100);
  }

  private async calculateOptimalTriangularAmount(pools: Pool[], tokens: Token[]): Promise<ethers.BigNumberish> {
    // Simplified calculation for triangular arbitrage
    return ethers.parseUnits('1', tokens[0].decimals);
  }

  private async estimateTriangularGasCost(pools: Pool[], amount: ethers.BigNumberish): Promise<ethers.BigNumberish> {
    // Simplified gas estimation for triangular arbitrage
    const gasPerSwap = BigInt(150000);
    const gasPrice = BigInt(ethers.parseUnits('20', 'gwei').toString());
    return gasPerSwap * BigInt(3) * gasPrice; // Three swaps
  }

  private async calculateTriangularProfit(
    pools: Pool[],
    tokens: Token[],
    amount: ethers.BigNumberish,
    gasCost: ethers.BigNumberish
  ): Promise<ethers.BigNumberish> {
    let currentAmount = amount;

    // Execute the triangular path
    for (let i = 0; i < pools.length; i++) {
      currentAmount = await this.calculateAmountOut(
        pools[i],
        currentAmount,
        tokens[i],
        tokens[i + 1]
      );
    }

    const grossProfit = BigInt(currentAmount.toString()) - BigInt(amount.toString());
    return grossProfit - BigInt(gasCost.toString());
  }

  private sendMessage(message: Omit<WorkerMessage, 'workerId'>): void {
    if (parentPort) {
      parentPort.postMessage({
        ...message,
        workerId: this.workerId
      });
    }
  }
}

// Worker thread entry point
if (!isMainThread && parentPort) {
  const worker = new ArbitrageWorker(workerData.workerId);

  parentPort.on('message', async (message: WorkerMessage) => {
    switch (message.type) {
      case 'task':
        await worker.processTask(message.data);
        break;
      case 'ping':
        (worker as any).sendMessage({
          type: 'result',
          data: { pong: true, tasksProcessed: (worker as any).tasksProcessed },
          timestamp: Date.now()
        });
        break;
      case 'shutdown':
        process.exit(0);
        break;
    }
  });

  // Initialize worker
  worker.initialize().catch(console.error);
}

export { ArbitrageWorker };
