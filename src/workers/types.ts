import { Token, Pool, ArbitrageRoute } from '../types';

export interface WorkerTask {
  id: string;
  type: 'arbitrage-scan' | 'triangular-arbitrage' | 'price-calculation';
  data: any;
  priority: number;
  timestamp: number;
}

export interface ArbitrageScanTask extends WorkerTask {
  type: 'arbitrage-scan';
  data: {
    tokenPairs: Array<{
      token0: Token;
      token1: Token;
      pairIndex: number;
    }>;
    minProfitThreshold: number;
    gasPrice: string;
  };
}

export interface TriangularArbitrageTask extends WorkerTask {
  type: 'triangular-arbitrage';
  data: {
    token0: Token;
    token1: Token;
    wethToken: Token;
    minProfitThreshold: number;
    gasPrice: string;
  };
}

export interface PriceCalculationTask extends WorkerTask {
  type: 'price-calculation';
  data: {
    pools: Pool[];
    tokens: Token[];
    amounts: string[];
  };
}

export interface WorkerResult {
  taskId: string;
  success: boolean;
  data?: any;
  error?: string;
  processingTime: number;
  workerId: number;
}

export interface ArbitrageScanResult extends WorkerResult {
  data: {
    opportunities: ArbitrageRoute[];
    processedPairs: number;
    profitableCount: number;
  };
}

export interface WorkerStats {
  workerId: number;
  tasksProcessed: number;
  totalProcessingTime: number;
  averageProcessingTime: number;
  errorCount: number;
  isActive: boolean;
  lastTaskTime: number;
}

export interface WorkerPoolConfig {
  maxWorkers: number;
  taskTimeout: number;
  maxQueueSize: number;
  workerIdleTimeout: number;
  enableLoadBalancing: boolean;
  priorityLevels: number;
}

export interface WorkerMessage {
  type: 'task' | 'result' | 'error' | 'ping' | 'shutdown';
  data?: any;
  taskId?: string;
  workerId?: number;
  timestamp: number;
}

export interface WorkerHealthCheck {
  workerId: number;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: number;
  taskQueueSize: number;
  isResponsive: boolean;
  lastHeartbeat: number;
}
