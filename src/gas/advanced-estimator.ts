import axios from 'axios';
import { ethers } from 'ethers';
import { config } from '../config';
import { logger } from '../utils/logger';
import { enhancedLogger } from '../utils/enhancedLogger';

export interface GasEstimate {
  slow: bigint;
  standard: bigint;
  fast: bigint;
  instant: bigint;
  baseFee?: bigint;
  priorityFee?: bigint;
  source: string;
  timestamp: number;
}

export interface GasEstimationConfig {
  blocknativeApiKey?: string;
  enableBlocknative: boolean;
  enable0xApi: boolean;
  enableEthGasStation: boolean;
  fallbackGasPrice: bigint;
}

/**
 * Advanced Gas Estimation using multiple APIs
 * Supports Blocknative, 0x API, and ETH Gas Station
 */
export class AdvancedGasEstimator {
  private provider: ethers.JsonRpcProvider;
  private config: GasEstimationConfig;
  private cache: Map<string, { estimate: GasEstimate; expiry: number }> = new Map();
  private readonly CACHE_DURATION = 15000; // 15 seconds

  constructor(provider: ethers.JsonRpcProvider, gasConfig?: Partial<GasEstimationConfig>) {
    this.provider = provider;
    this.config = {
      blocknativeApiKey: process.env.BLOCKNATIVE_API_KEY,
      enableBlocknative: !!process.env.BLOCKNATIVE_API_KEY,
      enable0xApi: true,
      enableEthGasStation: true,
      fallbackGasPrice: ethers.parseUnits('20', 'gwei'),
      ...gasConfig
    };
  }

  /**
   * Get comprehensive gas estimates from multiple sources
   */
  async getGasEstimates(): Promise<GasEstimate> {
    const cacheKey = 'gas_estimates';
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() < cached.expiry) {
      return cached.estimate;
    }

    try {
      // Fetching gas estimates - removed verbose logging to reduce spam

      const estimates = await Promise.allSettled([
        this.getBlocknativeEstimate(),
        this.get0xApiEstimate(),
        this.getEthGasStationEstimate(),
        this.getProviderEstimate()
      ]);

      const validEstimates = estimates
        .filter((result): result is PromiseFulfilledResult<GasEstimate> => 
          result.status === 'fulfilled' && result.value !== null
        )
        .map(result => result.value);

      if (validEstimates.length === 0) {
        enhancedLogger.systemStatus('⚠️  All gas estimation sources failed, using fallback');
        return this.getFallbackEstimate();
      }

      // Aggregate estimates using median values
      const aggregated = this.aggregateEstimates(validEstimates);
      
      // Cache the result
      this.cache.set(cacheKey, {
        estimate: aggregated,
        expiry: Date.now() + this.CACHE_DURATION
      });

      // Gas estimates aggregated - removed verbose logging to reduce spam

      return aggregated;

    } catch (error) {
      logger.logError(error as Error, 'AdvancedGasEstimator.getGasEstimates');
      return this.getFallbackEstimate();
    }
  }

  /**
   * Get gas estimate from Blocknative API
   */
  private async getBlocknativeEstimate(): Promise<GasEstimate | null> {
    if (!this.config.enableBlocknative || !this.config.blocknativeApiKey) {
      return null;
    }

    try {
      const response = await axios.get('https://api.blocknative.com/gasprices/blockprices', {
        headers: {
          'Authorization': this.config.blocknativeApiKey
        },
        timeout: 5000
      });

      const data = response.data;
      if (!data.blockPrices || data.blockPrices.length === 0) {
        return null;
      }

      const prices = data.blockPrices[0].estimatedPrices;
      const baseFee = ethers.parseUnits(data.blockPrices[0].baseFeePerGas.toString(), 'gwei');

      return {
        slow: ethers.parseUnits(prices.find((p: any) => p.confidence === 70)?.maxFeePerGas?.toString() || '20', 'gwei'),
        standard: ethers.parseUnits(prices.find((p: any) => p.confidence === 80)?.maxFeePerGas?.toString() || '25', 'gwei'),
        fast: ethers.parseUnits(prices.find((p: any) => p.confidence === 90)?.maxFeePerGas?.toString() || '30', 'gwei'),
        instant: ethers.parseUnits(prices.find((p: any) => p.confidence === 95)?.maxFeePerGas?.toString() || '35', 'gwei'),
        baseFee,
        priorityFee: ethers.parseUnits(prices.find((p: any) => p.confidence === 80)?.maxPriorityFeePerGas?.toString() || '2', 'gwei'),
        source: 'Blocknative',
        timestamp: Date.now()
      };

    } catch (error) {
      logger.debug('Blocknative gas estimation failed', { error: (error as Error).message });
      return null;
    }
  }

  /**
   * Get gas estimate from 0x API
   */
  private async get0xApiEstimate(): Promise<GasEstimate | null> {
    if (!this.config.enable0xApi) {
      return null;
    }

    try {
      const response = await axios.get('https://api.0x.org/gasinfo', {
        timeout: 5000
      });

      const data = response.data;

      return {
        slow: ethers.parseUnits(data.slow.toString(), 'gwei'),
        standard: ethers.parseUnits(data.standard.toString(), 'gwei'),
        fast: ethers.parseUnits(data.fast.toString(), 'gwei'),
        instant: ethers.parseUnits(data.instant.toString(), 'gwei'),
        source: '0x API',
        timestamp: Date.now()
      };

    } catch (error) {
      logger.debug('0x API gas estimation failed', { error: (error as Error).message });
      return null;
    }
  }

  /**
   * Get gas estimate from ETH Gas Station
   */
  private async getEthGasStationEstimate(): Promise<GasEstimate | null> {
    if (!this.config.enableEthGasStation) {
      return null;
    }

    try {
      const response = await axios.get('https://ethgasstation.info/api/ethgasAPI.json', {
        timeout: 5000
      });

      const data = response.data;

      // ETH Gas Station returns prices in 10 gwei units
      return {
        slow: ethers.parseUnits((data.safeLow / 10).toString(), 'gwei'),
        standard: ethers.parseUnits((data.standard / 10).toString(), 'gwei'),
        fast: ethers.parseUnits((data.fast / 10).toString(), 'gwei'),
        instant: ethers.parseUnits((data.fastest / 10).toString(), 'gwei'),
        source: 'ETH Gas Station',
        timestamp: Date.now()
      };

    } catch (error) {
      logger.debug('ETH Gas Station estimation failed', { error: (error as Error).message });
      return null;
    }
  }

  /**
   * Get gas estimate from provider
   */
  private async getProviderEstimate(): Promise<GasEstimate | null> {
    try {
      const feeData = await this.provider.getFeeData();
      
      if (!feeData.gasPrice && !feeData.maxFeePerGas) {
        return null;
      }

      const gasPrice = feeData.gasPrice || feeData.maxFeePerGas || this.config.fallbackGasPrice;
      const baseFee = feeData.maxFeePerGas ? feeData.maxFeePerGas - (feeData.maxPriorityFeePerGas || BigInt(0)) : undefined;

      return {
        slow: gasPrice * BigInt(80) / BigInt(100), // 80% of current
        standard: gasPrice,
        fast: gasPrice * BigInt(120) / BigInt(100), // 120% of current
        instant: gasPrice * BigInt(150) / BigInt(100), // 150% of current
        baseFee,
        priorityFee: feeData.maxPriorityFeePerGas || undefined,
        source: 'Provider',
        timestamp: Date.now()
      };

    } catch (error) {
      logger.debug('Provider gas estimation failed', { error: (error as Error).message });
      return null;
    }
  }

  /**
   * Aggregate multiple gas estimates using median values
   */
  private aggregateEstimates(estimates: GasEstimate[]): GasEstimate {
    const getMedian = (values: bigint[]): bigint => {
      const sorted = values.sort((a, b) => a < b ? -1 : a > b ? 1 : 0);
      const mid = Math.floor(sorted.length / 2);
      return sorted.length % 2 === 0 
        ? (sorted[mid - 1] + sorted[mid]) / BigInt(2)
        : sorted[mid];
    };

    const slowValues = estimates.map(e => e.slow);
    const standardValues = estimates.map(e => e.standard);
    const fastValues = estimates.map(e => e.fast);
    const instantValues = estimates.map(e => e.instant);

    const baseFees = estimates.filter(e => e.baseFee).map(e => e.baseFee!);
    const priorityFees = estimates.filter(e => e.priorityFee).map(e => e.priorityFee!);

    return {
      slow: getMedian(slowValues),
      standard: getMedian(standardValues),
      fast: getMedian(fastValues),
      instant: getMedian(instantValues),
      baseFee: baseFees.length > 0 ? getMedian(baseFees) : undefined,
      priorityFee: priorityFees.length > 0 ? getMedian(priorityFees) : undefined,
      source: `Aggregated (${estimates.map(e => e.source).join(', ')})`,
      timestamp: Date.now()
    };
  }

  /**
   * Get fallback gas estimate
   */
  private getFallbackEstimate(): GasEstimate {
    return {
      slow: this.config.fallbackGasPrice * BigInt(80) / BigInt(100),
      standard: this.config.fallbackGasPrice,
      fast: this.config.fallbackGasPrice * BigInt(120) / BigInt(100),
      instant: this.config.fallbackGasPrice * BigInt(150) / BigInt(100),
      source: 'Fallback',
      timestamp: Date.now()
    };
  }

  /**
   * Get optimal gas price for MEV transactions
   */
  async getOptimalGasPrice(urgency: 'slow' | 'standard' | 'fast' | 'instant' = 'fast'): Promise<{
    gasPrice: bigint;
    maxFeePerGas?: bigint;
    maxPriorityFeePerGas?: bigint;
  }> {
    const estimates = await this.getGasEstimates();
    
    const gasPrice = estimates[urgency];
    
    if (estimates.baseFee && estimates.priorityFee) {
      // EIP-1559 transaction
      return {
        gasPrice,
        maxFeePerGas: gasPrice,
        maxPriorityFeePerGas: estimates.priorityFee
      };
    } else {
      // Legacy transaction
      return {
        gasPrice
      };
    }
  }

  /**
   * Calculate gas cost in ETH
   */
  async calculateGasCost(gasLimit: bigint, urgency: 'slow' | 'standard' | 'fast' | 'instant' = 'fast'): Promise<bigint> {
    const gasPrice = await this.getOptimalGasPrice(urgency);
    return gasLimit * gasPrice.gasPrice;
  }

  /**
   * Check if current gas prices are favorable for MEV
   */
  async isGasFavorable(maxGasCostEth: number = 0.01): Promise<boolean> {
    try {
      const estimates = await this.getGasEstimates();
      const standardGasCost = estimates.standard * BigInt(300000); // Assume 300k gas limit
      const gasCostEth = Number(ethers.formatEther(standardGasCost));
      
      return gasCostEth <= maxGasCostEth;
    } catch (error) {
      logger.logError(error as Error, 'AdvancedGasEstimator.isGasFavorable');
      return false;
    }
  }

  /**
   * Get gas price trend (increasing/decreasing/stable)
   */
  async getGasTrend(): Promise<'increasing' | 'decreasing' | 'stable'> {
    try {
      // This would require historical data - simplified implementation
      const current = await this.getGasEstimates();
      
      // For now, return stable - in production, you'd compare with historical data
      return 'stable';
    } catch (error) {
      logger.logError(error as Error, 'AdvancedGasEstimator.getGasTrend');
      return 'stable';
    }
  }
}
