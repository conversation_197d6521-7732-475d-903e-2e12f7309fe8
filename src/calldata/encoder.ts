import { ethers } from 'ethers';
import { DecodedSwap, Token } from '../types';
import { ADDRESSES } from '../config';

export class CalldataEncoder {
  private static readonly UNISWAP_V2_ROUTER_ABI = [
    'function swapExactTokensForTokens(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline)',
    'function swapTokensForExactTokens(uint amountOut, uint amountInMax, address[] calldata path, address to, uint deadline)',
    'function swapExactETHForTokens(uint amountOutMin, address[] calldata path, address to, uint deadline)',
    'function swapTokensForExactETH(uint amountOut, uint amountInMax, address[] calldata path, address to, uint deadline)',
    'function swapExactTokensForETH(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline)',
    'function swapETHForExactTokens(uint amountOut, address[] calldata path, address to, uint deadline)'
  ];

  private static readonly UNISWAP_V3_ROUTER_ABI = [
    'function exactInputSingle((address tokenIn, address tokenOut, uint24 fee, address recipient, uint256 deadline, uint256 amountIn, uint256 amountOutMinimum, uint160 sqrtPriceLimitX96))',
    'function exactOutputSingle((address tokenIn, address tokenOut, uint24 fee, address recipient, uint256 deadline, uint256 amountOut, uint256 amountInMaximum, uint160 sqrtPriceLimitX96))',
    'function exactInput((bytes path, address recipient, uint256 deadline, uint256 amountIn, uint256 amountOutMinimum))',
    'function exactOutput((bytes path, address recipient, uint256 deadline, uint256 amountOut, uint256 amountInMaximum))'
  ];

  private static readonly ERC20_ABI = [
    'function approve(address spender, uint256 amount) returns (bool)',
    'function transfer(address to, uint256 amount) returns (bool)',
    'function balanceOf(address account) view returns (uint256)'
  ];

  private v2Interface: ethers.Interface;
  private v3Interface: ethers.Interface;
  private erc20Interface: ethers.Interface;

  constructor() {
    this.v2Interface = new ethers.Interface(CalldataEncoder.UNISWAP_V2_ROUTER_ABI);
    this.v3Interface = new ethers.Interface(CalldataEncoder.UNISWAP_V3_ROUTER_ABI);
    this.erc20Interface = new ethers.Interface(CalldataEncoder.ERC20_ABI);
  }

  encodeSwap(swap: DecodedSwap, recipient: string, deadline?: number): string {
    const swapDeadline = deadline || Math.floor(Date.now() / 1000) + 300; // 5 minutes

    if (swap.protocol === 'uniswap-v2') {
      return this.encodeUniswapV2Swap(swap, recipient, swapDeadline);
    } else if (swap.protocol === 'uniswap-v3') {
      return this.encodeUniswapV3Swap(swap, recipient, swapDeadline);
    }

    throw new Error(`Unsupported protocol: ${swap.protocol}`);
  }

  private encodeUniswapV2Swap(swap: DecodedSwap, recipient: string, deadline: number): string {
    const isETHIn = swap.tokenIn.address.toLowerCase() === ADDRESSES.WETH.toLowerCase();
    const isETHOut = swap.tokenOut.address.toLowerCase() === ADDRESSES.WETH.toLowerCase();

    if (isETHIn && !isETHOut) {
      // ETH -> Token
      return this.v2Interface.encodeFunctionData('swapExactETHForTokens', [
        swap.amountOutMin,
        swap.path,
        recipient,
        deadline
      ]);
    } else if (!isETHIn && isETHOut) {
      // Token -> ETH
      return this.v2Interface.encodeFunctionData('swapExactTokensForETH', [
        swap.amountIn,
        swap.amountOutMin,
        swap.path,
        recipient,
        deadline
      ]);
    } else {
      // Token -> Token
      return this.v2Interface.encodeFunctionData('swapExactTokensForTokens', [
        swap.amountIn,
        swap.amountOutMin,
        swap.path,
        recipient,
        deadline
      ]);
    }
  }

  private encodeUniswapV3Swap(swap: DecodedSwap, recipient: string, deadline: number): string {
    if (swap.path.length === 2) {
      // Single hop swap
      const params = {
        tokenIn: swap.tokenIn.address,
        tokenOut: swap.tokenOut.address,
        fee: swap.fee || 3000, // Default to 0.3%
        recipient,
        deadline,
        amountIn: swap.amountIn,
        amountOutMinimum: swap.amountOutMin,
        sqrtPriceLimitX96: 0 // No price limit
      };

      return this.v3Interface.encodeFunctionData('exactInputSingle', [params]);
    } else {
      // Multi-hop swap
      const encodedPath = this.encodeV3Path(swap.path, swap.fee || 3000);
      const params = {
        path: encodedPath,
        recipient,
        deadline,
        amountIn: swap.amountIn,
        amountOutMinimum: swap.amountOutMin
      };

      return this.v3Interface.encodeFunctionData('exactInput', [params]);
    }
  }

  private encodeV3Path(path: string[], fee: number): string {
    // Encode path for Uniswap V3
    // Format: token0 + fee + token1 + fee + token2 + ...
    let encodedPath = '0x';

    for (let i = 0; i < path.length; i++) {
      // Add token address (20 bytes)
      encodedPath += path[i].slice(2);

      // Add fee (3 bytes) if not the last token
      if (i < path.length - 1) {
        const feeHex = fee.toString(16).padStart(6, '0');
        encodedPath += feeHex;
      }
    }

    return encodedPath;
  }

  encodeApproval(tokenAddress: string, spender: string, amount: ethers.BigNumberish): string {
    return this.erc20Interface.encodeFunctionData('approve', [spender, amount]);
  }

  encodeTransfer(tokenAddress: string, to: string, amount: ethers.BigNumberish): string {
    return this.erc20Interface.encodeFunctionData('transfer', [to, amount]);
  }

  // Create a sandwich attack front-run transaction
  encodeFrontRunSwap(
    victimSwap: DecodedSwap,
    frontRunAmount: ethers.BigNumberish,
    recipient: string,
    slippageTolerance: number = 0.005
  ): string {
    // Create a swap in the same direction as the victim but with our amount
    const frontRunSwap: DecodedSwap = {
      ...victimSwap,
      amountIn: frontRunAmount,
      amountOutMin: this.calculateMinAmountOut(frontRunAmount, slippageTolerance)
    };

    return this.encodeSwap(frontRunSwap, recipient);
  }

  // Create a sandwich attack back-run transaction
  encodeBackRunSwap(
    victimSwap: DecodedSwap,
    backRunAmount: ethers.BigNumberish,
    recipient: string,
    slippageTolerance: number = 0.005
  ): string {
    // Create a swap in the opposite direction to the victim
    const backRunSwap: DecodedSwap = {
      method: this.getOppositeMethod(victimSwap.method),
      protocol: victimSwap.protocol,
      tokenIn: victimSwap.tokenOut, // Reverse the tokens
      tokenOut: victimSwap.tokenIn,
      amountIn: backRunAmount,
      amountOutMin: this.calculateMinAmountOut(backRunAmount, slippageTolerance),
      recipient: victimSwap.recipient,
      deadline: victimSwap.deadline,
      path: [...victimSwap.path].reverse(), // Reverse the path
      fee: victimSwap.fee
    };

    return this.encodeSwap(backRunSwap, recipient);
  }

  // Create an arbitrage transaction
  encodeArbitrageSwap(
    tokenIn: Token,
    tokenOut: Token,
    amountIn: ethers.BigNumberish,
    protocol: 'uniswap-v2' | 'uniswap-v3',
    recipient: string,
    fee?: number
  ): string {
    const arbitrageSwap: DecodedSwap = {
      method: protocol === 'uniswap-v2' ? 'swapExactTokensForTokens' : 'exactInputSingle',
      protocol,
      tokenIn,
      tokenOut,
      amountIn,
      amountOutMin: ethers.parseUnits('0', tokenOut.decimals), // Will be calculated elsewhere
      recipient,
      deadline: Math.floor(Date.now() / 1000) + 300,
      path: [tokenIn.address, tokenOut.address],
      fee
    };

    return this.encodeSwap(arbitrageSwap, recipient);
  }

  // Calculate minimum amount out with slippage tolerance
  private calculateMinAmountOut(
    amountIn: ethers.BigNumberish,
    slippageTolerance: number
  ): ethers.BigNumberish {
    // This is a simplified calculation
    // In practice, you'd use pool reserves and pricing formulas
    const amount = typeof amountIn === 'string' ? ethers.parseUnits(amountIn, 18) : BigInt(amountIn.toString());
    const slippageMultiplier = Math.floor((1 - slippageTolerance) * 10000);
    return (amount * BigInt(slippageMultiplier)) / BigInt(10000);
  }

  // Encode multiple transactions for a bundle
  encodeBundle(swaps: DecodedSwap[], recipient: string): string[] {
    return swaps.map(swap => this.encodeSwap(swap, recipient));
  }

  // Encode WETH wrap/unwrap
  encodeWETHWrap(amount: ethers.BigNumberish): string {
    const wethInterface = new ethers.Interface(['function deposit()']);
    return wethInterface.encodeFunctionData('deposit');
  }

  encodeWETHUnwrap(amount: ethers.BigNumberish): string {
    const wethInterface = new ethers.Interface(['function withdraw(uint256 amount)']);
    return wethInterface.encodeFunctionData('withdraw', [amount]);
  }

  // Create a flashloan transaction
  encodeFlashloan(
    assets: string[],
    amounts: ethers.BigNumberish[],
    modes: number[],
    onBehalfOf: string,
    params: string,
    referralCode: number = 0
  ): string {
    const flashloanInterface = new ethers.Interface([
      'function flashLoan(address[] assets, uint256[] amounts, uint256[] interestRateModes, address onBehalfOf, bytes params, uint16 referralCode)'
    ]);

    return flashloanInterface.encodeFunctionData('flashLoan', [
      assets,
      amounts,
      modes,
      onBehalfOf,
      params,
      referralCode
    ]);
  }

  // Encode flashloan callback parameters
  encodeFlashloanParams(arbitrageData: {
    routers: string[];
    amounts: ethers.BigNumberish[];
    swapData: string[];
  }): string {
    return ethers.AbiCoder.defaultAbiCoder().encode(
      ['address[]', 'uint256[]', 'bytes[]'],
      [arbitrageData.routers, arbitrageData.amounts, arbitrageData.swapData]
    );
  }

  private getOppositeMethod(method: string): string {
    // Return the opposite method for back-run transactions
    switch (method) {
      case 'swapExactTokensForTokens':
        return 'swapExactTokensForTokens'; // Same method, different direction
      case 'exactInputSingle':
        return 'exactInputSingle'; // Same method, different direction
      default:
        return method;
    }
  }
}
