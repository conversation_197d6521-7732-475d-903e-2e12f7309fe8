# 🧪 Hardhat Local Simulation Setup Guide

## 🎯 Why Use Hardhat for MEV Bot Testing?

Hardhat provides the **perfect testing environment** for MEV bot development:

- **🚀 Fast Execution**: Instant transactions and block mining
- **💰 Free Gas**: No transaction costs for rapid testing
- **🔄 Mainnet Forking**: Test with real liquidity and contracts
- **⏰ Time Control**: Manipulate block time and mining
- **👥 Account Impersonation**: Test with whale accounts
- **🔄 Reset Capability**: Start fresh anytime
- **🐛 Advanced Debugging**: Detailed transaction traces

## 🚀 Quick Start

### Option 1: Mainnet Fork (Recommended for Realistic Testing)

```bash
# 1. Set up environment for Hardhat
cp .env.hardhat .env

# 2. Update your Alchemy API key in .env
# MAINNET_FORK_URL=https://eth-mainnet.g.alchemy.com/v2/YOUR_KEY

# 3. Start Hardhat node with mainnet fork
npm run hardhat:fork:mainnet

# 4. In a new terminal, set up the environment
npm run hardhat:setup

# 5. Deploy flashloan contracts
npm run hardhat:deploy

# 6. Fund accounts with tokens
npm run hardhat:fund

# 7. Start the MEV bot
npm run dev:hardhat
```

### Option 2: Sepolia Fork (For Testnet Simulation)

```bash
# 1. Set up environment
cp .env.hardhat .env

# 2. Edit .env and set FORK_NETWORK=sepolia

# 3. Start Hardhat node with Sepolia fork
npm run hardhat:fork:sepolia

# 4. Continue with setup steps 4-7 above
```

### Option 3: Isolated Network (No Fork)

```bash
# 1. Set up environment
cp .env.hardhat .env

# 2. Edit .env and set FORK_NETWORK=none

# 3. Start isolated Hardhat node
npm run hardhat:node

# 4. Continue with setup steps 4-7 above
```

## 🔧 Detailed Configuration

### Environment Variables (.env.hardhat)

```env
# Network Configuration
CHAIN_ID=31337
FORK_NETWORK=mainnet  # mainnet, sepolia, or none

# Fork URLs (use your best RPC provider)
MAINNET_FORK_URL=https://eth-mainnet.g.alchemy.com/v2/YOUR_KEY
SEPOLIA_FORK_URL=https://eth-sepolia.g.alchemy.com/v2/YOUR_KEY

# Hardhat RPC
RPC_URL=http://localhost:8545
MEMPOOL_WEBSOCKET_URL=ws://localhost:8545

# Test accounts (pre-funded with 10,000 ETH each)
PRIVATE_KEY=0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80
FLASHBOTS_SIGNER_KEY=0x59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d

# Testing optimizations
MIN_PROFIT_WEI=****************  # 0.001 ETH
DRY_RUN=false  # Safe on local network
LOG_LEVEL=debug
```

## 🏗️ Network Options Explained

### 1. 🌐 Mainnet Fork
**Best for realistic testing**

- **Pros**: Real liquidity, real contracts, realistic conditions
- **Cons**: Requires good RPC provider, slower sync
- **Use case**: Final testing before mainnet deployment

```bash
# Start mainnet fork
npm run hardhat:fork:mainnet

# Automatically provides:
# - Real Uniswap/Curve/Balancer contracts
# - Real token liquidity (USDC, DAI, USDT, WETH)
# - Real whale accounts for testing
# - Current market conditions
```

### 2. 🧪 Sepolia Fork
**Best for testnet simulation**

- **Pros**: Testnet safety, some real contracts
- **Cons**: Limited liquidity, fewer protocols
- **Use case**: Testing with testnet conditions

```bash
# Start Sepolia fork
npm run hardhat:fork:sepolia

# Provides:
# - Sepolia Uniswap contracts
# - Limited token selection
# - Testnet environment safety
```

### 3. 🏝️ Isolated Network
**Best for unit testing**

- **Pros**: Fastest, completely controlled
- **Cons**: No real contracts, mock everything
- **Use case**: Testing bot logic without external dependencies

```bash
# Start isolated network
npm run hardhat:node

# Provides:
# - Clean slate environment
# - Mock contracts (if needed)
# - Maximum control
```

## 🛠️ Available Scripts

### Core Scripts

```bash
# Start different network types
npm run hardhat:node              # Isolated network
npm run hardhat:fork:mainnet      # Fork mainnet
npm run hardhat:fork:sepolia      # Fork Sepolia

# Setup and deployment
npm run hardhat:setup             # Test network connection
npm run hardhat:deploy            # Deploy flashloan contracts
npm run hardhat:fund              # Fund accounts with tokens

# Development
npm run dev:hardhat               # Start bot with Hardhat config
```

### Manual Commands

```bash
# Deploy to specific network
npx hardhat run scripts/deploy-hybrid-flashloan.js --network localhost

# Run setup script
npx hardhat run scripts/setup-hardhat-fork.js --network localhost

# Fund accounts
npx hardhat run scripts/fund-hardhat-accounts.js --network localhost

# Compile contracts
npx hardhat compile
```

## 🧪 Testing Features

### Account Impersonation

```javascript
// Impersonate a whale account for testing
await ethers.provider.send("hardhat_impersonateAccount", ["******************************************"]);
const whale = await ethers.getSigner("******************************************");

// Transfer tokens from whale to test account
const usdc = await ethers.getContractAt("IERC20", "******************************************");
await usdc.connect(whale).transfer(testAccount, ethers.parseUnits("100000", 6));
```

### Time Manipulation

```javascript
// Fast forward time
await ethers.provider.send("evm_increaseTime", [3600]); // 1 hour
await ethers.provider.send("evm_mine", []);

// Set specific timestamp
await ethers.provider.send("evm_setNextBlockTimestamp", [**********]);
await ethers.provider.send("evm_mine", []);
```

### Block Mining Control

```javascript
// Mine a single block
await ethers.provider.send("evm_mine", []);

// Mine multiple blocks
await ethers.provider.send("hardhat_mine", ["0x100"]); // Mine 256 blocks

// Set auto-mining interval
await ethers.provider.send("evm_setIntervalMining", [1000]); // 1 second
```

## 📊 Pre-funded Test Accounts

Hardhat provides 20 accounts with 10,000 ETH each:

```
Account 0: ****************************************** (Deployer)
Account 1: ****************************************** (Flashbots Signer)
Account 2: ******************************************
Account 3: ******************************************
...
```

Each account starts with:
- **10,000 ETH** for gas and testing
- **Access to impersonation** for whale testing
- **Free transactions** (no gas costs)

## 🎯 Testing Strategies

### 1. Arbitrage Testing

```bash
# 1. Start mainnet fork
npm run hardhat:fork:mainnet

# 2. Fund accounts with stablecoins
npm run hardhat:fund

# 3. Create artificial price differences
# (Use Hardhat console or custom scripts)

# 4. Test arbitrage detection and execution
npm run dev:hardhat
```

### 2. Flashloan Testing

```bash
# 1. Deploy flashloan contracts
npm run hardhat:deploy

# 2. Test flashloan execution with different amounts
# 3. Verify profit calculations
# 4. Test edge cases (failed swaps, insufficient liquidity)
```

### 3. Sandwich Attack Testing

```bash
# 1. Create large pending transactions
# 2. Test sandwich detection
# 3. Verify front-run and back-run execution
# 4. Test profit calculations
```

## 🚨 Common Issues & Solutions

### Issue: Fork fails to start
```bash
# Solution: Check your RPC provider
# Make sure ALCHEMY_API_KEY is set correctly
echo $ALCHEMY_API_KEY

# Try with a different provider
MAINNET_FORK_URL=https://eth-mainnet.g.alchemy.com/v2/demo npm run hardhat:fork:mainnet
```

### Issue: Contracts not found
```bash
# Solution: Make sure you're forking the right network
# Mainnet contracts won't exist on Sepolia fork
grep FORK_NETWORK .env
```

### Issue: Token funding fails
```bash
# Solution: Check whale account balances
# Some whale accounts may not have enough tokens
# Try different whale addresses in fund-hardhat-accounts.js
```

### Issue: Bot doesn't find opportunities
```bash
# Solution: Enable mock opportunities for testing
echo "ENABLE_MOCK_OPPORTUNITIES=true" >> .env

# Or create artificial arbitrage opportunities
# by manipulating DEX prices manually
```

## 🔄 Reset and Restart

```bash
# Stop Hardhat node (Ctrl+C)
# Restart with fresh state
npm run hardhat:fork:mainnet

# Re-run setup
npm run hardhat:setup
npm run hardhat:deploy
npm run hardhat:fund
```

## 📈 Performance Tips

1. **Use SSD storage** for faster fork syncing
2. **Increase Node.js memory**: `export NODE_OPTIONS="--max-old-space-size=8192"`
3. **Use local Ethereum node** for fastest forking
4. **Cache fork state** for repeated testing
5. **Use specific block numbers** to avoid re-syncing

## 🎓 Next Steps

After successful Hardhat testing:

1. **Test on Sepolia testnet** with real network conditions
2. **Deploy to mainnet** with conservative settings
3. **Monitor performance** and optimize strategies
4. **Scale up** gradually with proven strategies

---

## 💡 Pro Tips

- **Always test edge cases** on Hardhat first
- **Use time manipulation** to test time-sensitive strategies
- **Impersonate different accounts** to test various scenarios
- **Create custom test scenarios** with artificial opportunities
- **Monitor gas usage** even though it's free (for mainnet preparation)
