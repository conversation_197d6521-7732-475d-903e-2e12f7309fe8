# Uniswap V3 Price Calculation Fix

## Problem Description

The MEV bot was generating warnings: **"WETH/USDC price seems unrealistic"** when running on ETH mainnet. The calculated prices were extremely high (e.g., 407,238,175,925,596,800,000) instead of realistic values.

## Root Cause

The issue was in the `calculateV3Price` method in several strategy files. The decimal adjustment formula was **incorrect**:

### ❌ Incorrect Formula (Before Fix)
```javascript
const decimalsAdjustment = Math.pow(10, pool.token1.decimals - pool.token0.decimals);
let adjustedPrice = rawPrice * decimalsAdjustment; // WRONG: Multiplying
```

### ✅ Correct Formula (After Fix)
```javascript
const decimalsAdjustment = Math.pow(10, pool.token1.decimals - pool.token0.decimals);
let adjustedPrice = rawPrice / decimalsAdjustment; // CORRECT: Dividing
```

## Technical Details

### Uniswap V3 Price Calculation
According to the [Uniswap V3 Math Primer](https://blog.uniswap.org/uniswap-v3-math-primer):

1. **Raw Price from Tick**: `price = 1.0001^tick`
2. **Decimal Adjustment**: `adjustedPrice = rawPrice / 10^(token1.decimals - token0.decimals)`

### Example: USDC/WETH Pool
- **Token0**: USDC (6 decimals)
- **Token1**: WETH (18 decimals)
- **Tick**: 198259
- **Decimal Difference**: 18 - 6 = 12

#### Before Fix (Incorrect)
```
rawPrice = 1.0001^198259 = 407,238,175.93
adjustedPrice = 407,238,175.93 * 10^12 = 407,238,175,925,596,800,000
```
**Result**: Unrealistic price of ~407 trillion

#### After Fix (Correct)
```
rawPrice = 1.0001^198259 = 407,238,175.93
adjustedPrice = 407,238,175.93 / 10^12 = 0.000407238
```
**Result**: Realistic price of ~0.0004 WETH per USDC (ETH ≈ $2,456)

## Files Modified

### 1. `src/strategies/flashloan.ts`
- Fixed `calculateV3Price()` method
- Updated price validation logic
- Improved logging and error handling

### 2. `src/strategies/arbitrage.ts`
- Fixed `calculatePoolPrice()` method for V3 pools
- Added tick-based calculation as primary method
- Kept sqrtPriceX96 as fallback

### 3. `src/strategies/balancer-flashloan.ts`
- Fixed `calculatePoolPrice()` method
- Made consistent with other strategies

## Verification

### Test Results
Created verification scripts that confirm:

- **Old calculation**: 407,238,175,925,596,800,000 (unrealistic)
- **New calculation**: 0.000407238 (realistic)
- **Improvement**: 1.00e+24x reduction in price
- **Implied ETH Price**: $2,455.57 (matches market conditions)

### Expected Behavior
After the fix:
- ✅ No more "unrealistic price" warnings
- ✅ Prices match current market conditions
- ✅ Arbitrage opportunities are calculated correctly
- ✅ Bot can run successfully on mainnet

## Impact

### Before Fix
- Bot generated constant warnings about unrealistic prices
- Price differences were calculated incorrectly (16,595,265,194,308,005,888%)
- Arbitrage opportunities were filtered out as "unrealistic"
- Bot couldn't function properly on mainnet

### After Fix
- Prices are calculated correctly according to Uniswap V3 specification
- Price differences are realistic (typically 0.1% - 5%)
- Arbitrage opportunities are properly detected
- Bot can operate normally on mainnet

## References

1. [Uniswap V3 Math Primer](https://blog.uniswap.org/uniswap-v3-math-primer)
2. [Uniswap V3 Documentation](https://docs.uniswap.org/protocol/concepts/V3-overview/concentrated-liquidity)
3. [Ethereum Stack Exchange - Computing V3 Price](https://ethereum.stackexchange.com/questions/98685/computing-the-uniswap-v3-pair-price-from-q64-96-number)

## Testing

Run the verification scripts to confirm the fix:

```bash
# Test the price calculation fix
node test/verify-v3-price-fix.js

# Test with mock pool data
node test/test-price-fix.js
```

Both scripts demonstrate the dramatic improvement in price calculation accuracy.
