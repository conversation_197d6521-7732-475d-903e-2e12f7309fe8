# 🚀 RPC Provider Setup Guide - No More Rate Limits!

## 🚨 Why Not Infura?

Infura has **severe rate limits** that make it unsuitable for MEV bots:
- **100,000 requests/day** on free tier
- **Rate limiting** kicks in quickly with mempool monitoring
- **Not MEV-friendly** - designed for regular dApps

## ✅ Best Alternatives (No Rate Limits)

### 1. 🥇 **Alchemy** (Recommended)
**Best for beginners and production MEV bots**

**Advantages:**
- ✅ **300M compute units/month** (FREE) - 30x more than Infura
- ✅ **MEV-friendly** infrastructure
- ✅ **Enhanced APIs** for better performance
- ✅ **Reliable** and fast

**Setup:**
1. Go to [alchemy.com](https://www.alchemy.com/)
2. Create account → "Create App"
3. Select "Ethereum" → "Sepolia" (for testing)
4. Copy your API key
5. Add to `.env`: `ALCHEMY_API_KEY=your_key_here`

**URLs:**
- Sepolia: `https://eth-sepolia.g.alchemy.com/v2/YOUR_KEY`
- Mainnet: `https://eth-mainnet.g.alchemy.com/v2/YOUR_KEY`

---

### 2. 🥈 **QuickNode** (Best for Production)
**Best for serious MEV operations**

**Advantages:**
- ✅ **No rate limits** on paid plans
- ✅ **MEV-optimized** endpoints
- ✅ **Global infrastructure** (low latency)
- ✅ **Dedicated support** for MEV

**Setup:**
1. Go to [quicknode.com](https://www.quicknode.com/)
2. Create account → "Create Endpoint"
3. Select "Ethereum" → "Sepolia"
4. Choose plan (paid plans = no limits)
5. Copy endpoint URL
6. Add to `.env`: `QUICKNODE_ENDPOINT=your_endpoint_url`

**Pricing:**
- Discover: $9/month - No rate limits
- Build: $49/month - Enhanced features
- Scale: $299/month - Maximum performance

---

### 3. 🥉 **Ankr** (Great Free Option)
**Best free alternative to Infura**

**Advantages:**
- ✅ **Very generous** free tier
- ✅ **Premium unlimited** plans available
- ✅ **Multi-chain** support
- ✅ **Good performance**

**Setup:**
1. Go to [ankr.com](https://www.ankr.com/)
2. Create account → Get API key
3. Add to `.env`: `ANKR_API_KEY=your_key_here`

**URLs:**
- Sepolia: `https://rpc.ankr.com/eth_sepolia/YOUR_KEY`
- Mainnet: `https://rpc.ankr.com/eth/YOUR_KEY`

---

### 4. 🏆 **Local Node** (Ultimate Solution)
**Best performance, no limits, complete control**

**Advantages:**
- ✅ **No rate limits** at all
- ✅ **Fastest** possible performance
- ✅ **Complete control** over your infrastructure
- ✅ **Most reliable** for MEV

**Setup Options:**

#### Option A: Geth (Most Popular)
```bash
# Install Geth
# macOS: brew install ethereum
# Ubuntu: sudo apt-get install ethereum

# Sepolia Testnet
geth --sepolia --http --http.api eth,net,web3 --ws --ws.api eth,net,web3

# Mainnet (requires ~1TB storage)
geth --http --http.api eth,net,web3 --ws --ws.api eth,net,web3
```

#### Option B: Erigon (Faster Sync)
```bash
# Install Erigon
git clone https://github.com/ledgerwatch/erigon.git
cd erigon && make erigon

# Sepolia Testnet
./build/bin/erigon --chain sepolia --http.api eth,net,web3 --ws

# Mainnet
./build/bin/erigon --http.api eth,net,web3 --ws
```

**URLs:**
- HTTP: `http://localhost:8545`
- WebSocket: `ws://localhost:8546`

---

## 🔧 Configuration

### Multiple Provider Setup (Recommended)
The bot automatically switches between providers for maximum reliability:

```env
# Primary providers (no rate limits)
ALCHEMY_API_KEY=your_alchemy_key
QUICKNODE_ENDPOINT=https://your-endpoint.quiknode.pro/key/
ANKR_API_KEY=your_ankr_key
LOCAL_NODE_URL=http://localhost:8545

# The bot will automatically:
# 1. Try Local Node first (if available)
# 2. Fall back to Alchemy
# 3. Fall back to QuickNode
# 4. Fall back to Ankr
# 5. Use public RPCs as last resort
```

### Single Provider Setup
If you only want to use one provider:

```env
# Just set one of these:
ALCHEMY_API_KEY=your_key_here
# OR
QUICKNODE_ENDPOINT=your_endpoint_here
# OR
ANKR_API_KEY=your_key_here
```

---

## 📊 Performance Comparison

| Provider | Rate Limit | Cost | MEV Optimized | Reliability |
|----------|------------|------|---------------|-------------|
| **Infura** | ❌ 100k/day | Free | ❌ No | ⭐⭐⭐ |
| **Alchemy** | ✅ 300M/month | Free/Paid | ✅ Yes | ⭐⭐⭐⭐⭐ |
| **QuickNode** | ✅ Unlimited* | Paid | ✅ Yes | ⭐⭐⭐⭐⭐ |
| **Ankr** | ✅ Very High | Free/Paid | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| **Local Node** | ✅ None | Hardware | ✅ Yes | ⭐⭐⭐⭐⭐ |

*Unlimited on paid plans

---

## 🚀 Quick Start

### For Testing (Sepolia):
1. **Sign up for Alchemy** (easiest)
2. **Create Sepolia app**
3. **Copy API key** to `.env`
4. **Start bot** - no rate limits!

### For Production (Mainnet):
1. **Set up local node** (best performance)
2. **Add Alchemy backup** (reliability)
3. **Add QuickNode backup** (redundancy)
4. **Monitor performance**

---

## 🔍 Monitoring

The bot automatically logs provider status:
```
✅ Switched to RPC provider: Alchemy
✅ WebSocket connection established: Alchemy
⚠️  Rate limit hit for Public RPC, switching provider...
✅ Switched to RPC provider: QuickNode
```

---

## 💡 Pro Tips

1. **Use multiple providers** for maximum uptime
2. **Local node** gives best performance for serious MEV
3. **Alchemy** is perfect for development and testing
4. **QuickNode** is ideal for production MEV bots
5. **Avoid Infura** for MEV - too restrictive

---

## 🆘 Troubleshooting

**Problem:** Still getting rate limited
**Solution:** Check you're using the new RPC manager, not hardcoded Infura URLs

**Problem:** WebSocket connection fails
**Solution:** Bot will fall back to HTTP automatically

**Problem:** All providers failing
**Solution:** Check your API keys and network connection

**Problem:** Slow performance
**Solution:** Use a local node or upgrade to paid provider plans
