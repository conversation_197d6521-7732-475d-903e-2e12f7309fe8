# 🚨 MAINNET FLASHLOAN SETUP GUIDE

## ⚠️ CRITICAL WARNING

**THIS IS FOR ETHEREUM MAINNET - REAL MONEY AT RISK!**

Only proceed if you:
- ✅ Have thoroughly tested on Sepolia testnet
- ✅ Understand MEV risks and competition
- ✅ Have sufficient funds for gas and potential losses
- ✅ Are prepared for high-stakes trading environment

## 🔧 Prerequisites

### 1. Technical Requirements
- Node.js 18+
- Hardhat development environment
- Secure private key management
- Reliable RPC provider (Alchemy/Infura)
- Monitoring and alerting setup

### 2. Financial Requirements
- **Minimum 1 ETH** for gas fees and testing
- **Additional capital** for potential arbitrage opportunities
- **Emergency fund** for unexpected costs

### 3. Knowledge Requirements
- Understanding of MEV and flashloans
- Smart contract interaction experience
- Risk management principles
- Ethereum mainnet operations

## 🚀 Step-by-Step Setup

### Step 1: Environment Configuration

1. **Copy mainnet environment file:**
```bash
cp .env.mainnet .env
```

2. **Update critical settings:**
```bash
# Network
CHAIN_ID=1
RPC_URL=https://eth-mainnet.g.alchemy.com/v2/YOUR_REAL_KEY

# Security (NEVER commit real keys!)
PRIVATE_KEY=0xYOUR_REAL_PRIVATE_KEY
FLASHBOTS_SIGNER_KEY=0xYOUR_FLASHBOTS_KEY

# Safety first!
DRY_RUN=true  # ALWAYS start with dry run!
```

### Step 2: Deploy Flashloan Contract

1. **Ensure sufficient ETH balance:**
```bash
# Check your balance
npx hardhat console --network mainnet
> const balance = await ethers.provider.getBalance("YOUR_ADDRESS")
> console.log(ethers.formatEther(balance), "ETH")
```

2. **Deploy the contract:**
```bash
npx hardhat run scripts/deploy-flashloan-mainnet.js --network mainnet
```

3. **Verify deployment:**
```bash
# The script will output the contract address
# Update your .env file:
FLASHLOAN_CONTRACT_ADDRESS=0xYOUR_DEPLOYED_CONTRACT
```

### Step 3: Initial Testing

1. **Start with dry run mode:**
```bash
# Ensure DRY_RUN=true in .env
npm run dev
```

2. **Monitor the logs:**
```bash
# Look for flashloan opportunities
🔍 Scanning for flashloan arbitrage opportunities...
🔥 High-confidence flashloan opportunity detected (85%)
DRY RUN: Simulating flashloan arbitrage execution...
```

3. **Verify configuration:**
```bash
# Check that mainnet addresses are loaded
🚨 MAINNET MODE: Using conservative flashloan parameters
   Min Profit: 0.5%
   Max Amount: 100000.0 USDC
```

### Step 4: Live Trading (Proceed with Extreme Caution)

1. **Start with small amounts:**
```bash
# Update .env for live trading
DRY_RUN=false
FLASHLOAN_MAX_AMOUNT_USDC=1000  # Start small!
```

2. **Monitor closely:**
```bash
# Run with verbose logging
LOG_LEVEL=debug npm run dev
```

3. **Scale gradually:**
```bash
# Only increase after proven success
FLASHLOAN_MAX_AMOUNT_USDC=5000  # Gradual increase
```

## 📊 Mainnet Configuration Differences

### Profit Thresholds
| Setting | Testnet | Mainnet | Reason |
|---------|---------|---------|---------|
| Min Profit | 2% | 0.5% | Higher competition |
| Max Gas Price | 50 gwei | 100 gwei | Network congestion |
| Slippage Tolerance | 1% | 0.3% | Tighter execution |
| Confidence Threshold | 70% | 75% | Higher standards |

### Risk Management
```bash
# Mainnet-specific settings
MAX_POSITION_SIZE_ETH=5
MIN_PROFIT_WEI=3300000000000000  # ~$10 minimum profit (0.0033 ETH)
FLASHLOAN_MIN_PROFIT_BPS=33       # 0.33% (equivalent to ~$10 at typical volumes)
MEMPOOL_FILTER_MIN_VALUE=1000000000000000000  # 1 ETH
```

## 🎯 Mainnet Strategy Optimization

### 1. Gas Optimization
- **Priority Fees**: Use dynamic priority fee calculation
- **Gas Limits**: Optimize gas limits for each transaction type
- **Timing**: Execute during optimal gas price windows

### 2. Competition Handling
- **Speed**: Minimize transaction latency
- **MEV Protection**: Use Flashbots for sensitive transactions
- **Diversification**: Target multiple opportunity types

### 3. Profit Maximization
- **Amount Optimization**: Calculate optimal flashloan amounts
- **Route Selection**: Choose most profitable arbitrage paths
- **Timing**: Execute during high volatility periods

## 🔍 Monitoring and Alerts

### Key Metrics to Monitor
```bash
# Performance metrics
- Success rate (target: >80%)
- Average profit per trade
- Gas efficiency ratio
- Opportunity frequency

# Risk metrics
- Maximum drawdown
- Failed transaction rate
- Gas cost vs profit ratio
- Slippage impact
```

### Recommended Alerts
- Failed transactions > 20%
- Gas prices > 200 gwei
- Profit margins < 0.3%
- Contract balance changes
- Unusual transaction patterns

## 🚨 Emergency Procedures

### 1. Emergency Stop
```bash
# Set in .env immediately
EMERGENCY_STOP=true
```

### 2. Fund Recovery
```bash
# Withdraw profits from contract
npx hardhat run scripts/withdraw-profits.js --network mainnet
```

### 3. Contract Pause
```bash
# If contract has pause functionality
npx hardhat run scripts/pause-contract.js --network mainnet
```

## 📈 Performance Optimization

### 1. RPC Provider Optimization
```bash
# Use multiple providers for redundancy
RPC_URL=https://eth-mainnet.g.alchemy.com/v2/KEY1
BACKUP_RPC_URL=https://mainnet.infura.io/v3/KEY2
```

### 2. Mempool Monitoring
```bash
# Enable multiple mempool sources
ENABLE_FLASHBOTS_MEMPOOL=true
ENABLE_ETHERS_MEMPOOL=true
MEMPOOL_WEBSOCKET_URL=wss://eth-mainnet.g.alchemy.com/v2/KEY
```

### 3. Strategy Tuning
```bash
# Adjust based on market conditions
ARBITRAGE_MIN_PROFIT_BPS=20  # 0.2% minimum
SANDWICH_MIN_VICTIM_AMOUNT=10000000000000000000  # 10 ETH
```

## 🔒 Security Best Practices

### 1. Private Key Security
- Use hardware wallets when possible
- Never commit keys to version control
- Rotate keys regularly
- Use separate keys for different functions

### 2. Contract Security
- Verify all contract interactions
- Monitor for unusual activity
- Keep emergency withdrawal ready
- Regular security audits

### 3. Operational Security
- Secure server environment
- VPN for remote access
- Encrypted communication
- Regular backups

## 📊 Expected Performance

### Realistic Expectations
- **Success Rate**: 60-80% (high competition)
- **Daily Opportunities**: 5-20 (market dependent)
- **Average Profit**: 0.01-0.1 ETH per trade
- **Gas Costs**: 0.005-0.02 ETH per transaction

### Market Conditions Impact
- **High Volatility**: More opportunities, higher profits
- **Low Volatility**: Fewer opportunities, lower profits
- **High Gas**: Reduced profitability
- **Network Congestion**: Execution challenges

## 🚨 Risk Warnings

### Financial Risks
- **Loss of Capital**: MEV trading can result in losses
- **Gas Costs**: High gas prices can eliminate profits
- **Competition**: Other bots may front-run your trades
- **Market Risk**: Crypto market volatility

### Technical Risks
- **Smart Contract Bugs**: Code vulnerabilities
- **Network Issues**: RPC provider failures
- **Slippage**: Price impact on large trades
- **MEV Extraction**: Being MEV'd by other bots

### Regulatory Risks
- **Legal Compliance**: MEV may face future regulation
- **Tax Implications**: Trading profits are taxable
- **Jurisdiction**: Laws vary by location

## 📞 Support and Resources

### Documentation
- [Aave V3 Documentation](https://docs.aave.com/developers/)
- [Uniswap V3 Documentation](https://docs.uniswap.org/)
- [Flashbots Documentation](https://docs.flashbots.net/)

### Community
- [MEV Research](https://github.com/flashbots/mev-research)
- [Ethereum MEV Community](https://collective.flashbots.net/)

### Emergency Contacts
- Have technical support contacts ready
- Know your RPC provider's support channels
- Maintain emergency fund access

---

**Remember: Mainnet trading involves real money and significant risks. Start small, monitor closely, and never risk more than you can afford to lose.**
