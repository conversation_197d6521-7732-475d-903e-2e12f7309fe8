# 🔧 Environment Variables Guide

## 📋 Flashloan Contract Addresses

### **HYBRID_FLASHLOAN_CONTRACT**
- **Purpose**: Address of the deployed HybridFlashloanArbitrage contract
- **Used by**: `FlashloanStrategy` class
- **Functionality**: 
  - Automatically selects optimal provider (Aave vs Balancer)
  - Executes flashloans with best fees and liquidity
  - Provides unified interface for both providers

```bash
# Example usage
HYBRID_FLASHLOAN_CONTRACT=******************************************
```

**How it's used in code:**
```typescript
// src/strategies/flashloan.ts
if (config.hybridFlashloanContract) {
  this.hybridContract = new ethers.Contract(
    config.hybridFlashloanContract,
    this.getHybridContractABI(),
    this.wallet
  );
  // Contract automatically chooses Aave or Balancer
  const success = await this.executeWithHybridContract(route);
}
```

### **BALANCER_FLASHLOAN_CONTRACT**
- **Purpose**: Address of the deployed BalancerFlashloanArbitrage contract
- **Used by**: `BalancerFlashloanStrategy` class
- **Functionality**:
  - Executes 0% fee flashloans via Balancer V2
  - Uses official Balancer API
  - Best for smaller amounts

```bash
# Example usage
BALANCER_FLASHLOAN_CONTRACT=******************************************
```

**How it's used in code:**
```typescript
// src/strategies/balancer-flashloan.ts
if (config.balancerFlashloanContract) {
  this.balancerContract = new ethers.Contract(
    config.balancerFlashloanContract,
    this.getBalancerContractABI(),
    this.wallet
  );
  // Execute 0% fee flashloan
  const success = await this.executeWithBalancerContract(route);
}
```

### **AAVE_FLASHLOAN_CONTRACT**
- **Purpose**: Address of the deployed FlashloanArbitrage contract (Aave-only)
- **Used by**: Future Aave-specific strategy
- **Functionality**:
  - Executes flashloans via Aave V3 (0.09% fees)
  - Better for larger amounts due to higher liquidity

```bash
# Example usage
AAVE_FLASHLOAN_CONTRACT=******************************************
```

## 🔄 Strategy Selection Logic

### **Hybrid Strategy (Recommended)**
When `HYBRID_FLASHLOAN_CONTRACT` is set:

```typescript
// Automatic provider selection
const optimalProvider = await this.hybridContract.getOptimalProvider(
  route.flashloanToken.address,
  route.flashloanAmount
);

// 0 = Aave, 1 = Balancer
if (optimalProvider === 1) {
  // Use Balancer (0% fees)
} else {
  // Use Aave (better liquidity)
}
```

### **Individual Strategies**
When specific contract addresses are set:

```typescript
// Balancer-only execution
if (config.balancerFlashloanContract) {
  await balancerStrategy.executeBalancerFlashloan(route);
}

// Aave-only execution  
if (config.aaveFlashloanContract) {
  await aaveStrategy.executeAaveFlashloan(route);
}
```

## 📊 Contract Deployment Workflow

### 1. **Deploy Contracts**
```bash
# Deploy hybrid contract (recommended)
npx hardhat run scripts/deploy-hybrid-flashloan.js --network sepolia

# Or deploy individual contracts
npx hardhat run scripts/deploy-balancer-flashloan.js --network sepolia
npx hardhat run scripts/deploy-flashloan.js --network sepolia
```

### 2. **Update Environment Variables**
```bash
# Copy addresses from deployment output
HYBRID_FLASHLOAN_CONTRACT=0x...    # From hybrid deployment
BALANCER_FLASHLOAN_CONTRACT=0x...  # From Balancer deployment
AAVE_FLASHLOAN_CONTRACT=0x...      # From Aave deployment
```

### 3. **Enable Flashloan Attacks**
```bash
ENABLE_FLASHLOAN_ATTACKS=true
```

### 4. **Test Configuration**
```bash
# Start with dry run
DRY_RUN=true
npm run dev

# Look for contract initialization logs:
🔄 Hybrid Contract: 0x...
🔵 Balancer Contract: 0x...
```

## 🎯 Recommended Configuration

### **For Maximum Profit (Hybrid Approach)**
```bash
# Deploy and use hybrid contract
HYBRID_FLASHLOAN_CONTRACT=0x...
BALANCER_FLASHLOAN_CONTRACT=      # Leave empty
AAVE_FLASHLOAN_CONTRACT=          # Leave empty
ENABLE_FLASHLOAN_ATTACKS=true
```

**Benefits:**
- ✅ Automatic provider selection
- ✅ 0% fees for smaller amounts (Balancer)
- ✅ Better liquidity for larger amounts (Aave)
- ✅ Single contract deployment

### **For Balancer-Only (Zero Fees)**
```bash
# Deploy and use Balancer contract only
HYBRID_FLASHLOAN_CONTRACT=        # Leave empty
BALANCER_FLASHLOAN_CONTRACT=0x... # Set this
AAVE_FLASHLOAN_CONTRACT=          # Leave empty
ENABLE_FLASHLOAN_ATTACKS=true
```

**Benefits:**
- ✅ 0% flashloan fees
- ✅ Official Balancer V2 API
- ❌ Limited to smaller amounts

### **For Aave-Only (High Liquidity)**
```bash
# Deploy and use Aave contract only
HYBRID_FLASHLOAN_CONTRACT=        # Leave empty
BALANCER_FLASHLOAN_CONTRACT=      # Leave empty
AAVE_FLASHLOAN_CONTRACT=0x...     # Set this
ENABLE_FLASHLOAN_ATTACKS=true
```

**Benefits:**
- ✅ Massive liquidity
- ✅ Battle-tested security
- ❌ 0.09% fees

## 🚨 Error Handling

### **Missing Contract Address**
```bash
# Log output when contract address is missing:
⚠️  No hybrid contract address provided - using individual strategies
⚠️  Balancer flashloan execution requires contract deployment
   Deploy with: npx hardhat run scripts/deploy-balancer-flashloan.js
```

### **Invalid Contract Address**
```bash
# Error when contract address is invalid:
❌ Hybrid contract execution failed: Contract not deployed
❌ Balancer contract execution failed: Invalid address
```

### **Contract Not Funded**
```bash
# Error when contract has no ETH for gas:
❌ Transaction failed: insufficient funds for gas
```

## 🔍 Debugging

### **Check Contract Status**
```typescript
// Verify contract is properly initialized
console.log('Hybrid contract:', config.hybridFlashloanContract);
console.log('Balancer contract:', config.balancerFlashloanContract);
console.log('Aave contract:', config.aaveFlashloanContract);
```

### **Test Contract Interaction**
```bash
# Use Hardhat console to test contract
npx hardhat console --network sepolia

> const contract = await ethers.getContractAt("HybridFlashloanArbitrage", "0x...");
> await contract.owner();
> await contract.getOptimalProvider("0x...", ethers.parseUnits("1000", 6));
```

### **Monitor Contract Events**
```typescript
// Listen for flashloan execution events
contract.on("FlashloanExecuted", (provider, asset, amount, premium, profit) => {
  console.log(`Flashloan executed: ${provider}, profit: ${profit}`);
});
```

## 📈 Performance Impact

### **Contract vs Direct Execution**
| Method | Gas Cost | Complexity | Profit Optimization |
|--------|----------|------------|-------------------|
| Hybrid Contract | +20k gas | Low | Automatic |
| Individual Contracts | +15k gas | Medium | Manual |
| Direct Vault Calls | Baseline | High | Manual |

### **Recommended for Production**
- **Mainnet**: Use `HYBRID_FLASHLOAN_CONTRACT` for automatic optimization
- **Testnet**: Use individual contracts for testing specific providers
- **Development**: Use direct vault calls for debugging

---

**💡 Pro Tip**: Always deploy and test contracts on Sepolia before mainnet deployment. The hybrid approach provides the best balance of profit optimization and simplicity.
