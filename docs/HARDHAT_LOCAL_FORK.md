# 🏠 Hardhat Fork with Local ETH RPC Node

## 🎯 Overview

This setup gives you the **best of both worlds**:
- **Hardhat's testing features**: Time manipulation, account impersonation, fast mining
- **Your local node's data**: Real blockchain state, real liquidity, no external dependencies
- **Maximum performance**: No rate limits, fastest possible testing

## 🚀 Quick Start

### Method 1: Automated Setup (Recommended)
```bash
# Run the setup script and choose option 2
npm run setup:local
# Select: "2) Hardhat fork with local node as source"
```

### Method 2: Manual Setup
```bash
# 1. Start Hardhat fork from your local node
npm run hardhat:fork:local

# 2. In a new terminal, deploy contracts
npm run hardhat:deploy

# 3. Start MEV bot
npm run dev:hardhat
```

### Method 3: Step-by-Step
```bash
# 1. Make sure your local ETH node is running
# Check: curl -X POST -H "Content-Type: application/json" --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' http://localhost:8545

# 2. Start Hardhat fork
npx hardhat node --fork http://localhost:8545

# 3. In new terminal, configure environment
cp .env.hardhat-local .env

# 4. Deploy contracts
npm run hardhat:deploy

# 5. Start bot
npm run dev:hardhat
```

## 🔧 How It Works

### Architecture
```
Your Local ETH Node (localhost:8545) 
    ↓ (forks from)
Hardhat Network (localhost:8546 or 8547)
    ↓ (connects to)
MEV Bot
```

### What Happens
1. **Your local node** provides real blockchain data (Mainnet or Sepolia)
2. **Hardhat forks** from your local node, creating a test environment
3. **MEV bot** connects to Hardhat for testing with real data

## 🎯 Benefits

### ✅ **Advantages**
- **Real Data**: Uses your local node's blockchain state
- **Hardhat Features**: Time manipulation, account impersonation, debugging
- **No Rate Limits**: Your local node has no restrictions
- **Fast Testing**: Instant transactions and block mining
- **Offline Capable**: No internet required after initial sync
- **Complete Control**: Your infrastructure, your rules

### 🔄 **vs Other Setups**
| Feature | Local Fork | External Fork | Direct Local |
|---------|------------|---------------|--------------|
| Real Data | ✅ | ✅ | ✅ |
| Hardhat Features | ✅ | ✅ | ❌ |
| No Rate Limits | ✅ | ❌ | ✅ |
| Fast Testing | ✅ | ⚠️ | ❌ |
| Offline | ✅ | ❌ | ✅ |

## 📋 Prerequisites

### 1. Local ETH Node Running
Your local Ethereum node must be running and accessible:

**Check if running:**
```bash
curl -X POST -H "Content-Type: application/json" \
  --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
  http://localhost:8545
```

**Start if needed:**
```bash
# Geth Mainnet
geth --http --http.api eth,net,web3 --ws --ws.api eth,net,web3

# Geth Sepolia
geth --sepolia --http --http.api eth,net,web3 --ws --ws.api eth,net,web3

# Erigon
./build/bin/erigon --http.api eth,net,web3 --ws
```

### 2. Node Must Be Synced
Your local node should be fully synced for realistic testing.

## 🛠️ Configuration

### Environment Variables (.env.hardhat-local)
```env
# Hardhat configuration
CHAIN_ID=31337
FORK_NETWORK=mainnet  # or sepolia

# Fork source (your local node)
MAINNET_FORK_URL=http://localhost:8545
SEPOLIA_FORK_URL=http://localhost:8545

# Hardhat RPC (different port to avoid conflict)
RPC_URL=http://localhost:8546
MEMPOOL_WEBSOCKET_URL=ws://localhost:8546

# Test accounts (Hardhat defaults)
PRIVATE_KEY=0xac0974bec39a17e36ba4a6b4d238ff944bacb478cbed5efcae784d7bf4f2ff80
FLASHBOTS_SIGNER_KEY=0x59c6995e998f97a5a0044966f0945389dc9e86dae88c7a8412f4603b6b78690d

# Testing optimizations
MIN_PROFIT_WEI=****************  # 0.001 ETH
DRY_RUN=false
ENABLE_FLASHBOTS=false  # Not available on Hardhat
```

## 🧪 Testing Features

### Account Impersonation
```javascript
// Impersonate any account (like whales)
await ethers.provider.send("hardhat_impersonateAccount", ["******************************************"]);
const whale = await ethers.getSigner("******************************************");

// Use whale account for testing
const usdc = await ethers.getContractAt("IERC20", "******************************************");
await usdc.connect(whale).transfer(testAccount, ethers.parseUnits("100000", 6));
```

### Time Manipulation
```javascript
// Fast forward time
await ethers.provider.send("evm_increaseTime", [3600]); // 1 hour
await ethers.provider.send("evm_mine", []);

// Mine blocks instantly
await ethers.provider.send("evm_mine", []);
await ethers.provider.send("hardhat_mine", ["0x100"]); // Mine 256 blocks
```

### Balance Manipulation
```javascript
// Set ETH balance for any account
await ethers.provider.send("hardhat_setBalance", [
  "******************************************",
  "0x56BC75E2D63100000" // 100 ETH
]);
```

## 📊 Available Commands

```bash
# Start Hardhat fork from local node
npm run hardhat:fork:local        # Auto-detects your local node

# Manual fork commands
npx hardhat node --fork http://localhost:8545
npx hardhat node --fork http://localhost:8545 --port 8546

# Deployment
npm run hardhat:deploy            # Deploy to Hardhat network
npm run hardhat:setup             # Test connection
npm run hardhat:fund              # Fund accounts

# Development
npm run dev:hardhat               # Start bot with Hardhat config
```

## 🔍 Troubleshooting

### Issue: "Local node not found"
```bash
# Check if your local node is running
curl -X POST -H "Content-Type: application/json" \
  --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
  http://localhost:8545

# Start your local node if needed
geth --http --http.api eth,net,web3 --ws --ws.api eth,net,web3
```

### Issue: "Port already in use"
```bash
# Check what's using the port
lsof -i :8545

# Kill if needed
kill -9 $(lsof -t -i:8545)

# Or use different port
npx hardhat node --fork http://localhost:8545 --port 8547
```

### Issue: "Chain ID mismatch"
```bash
# Check your local node's chain ID
curl -X POST -H "Content-Type: application/json" \
  --data '{"jsonrpc":"2.0","method":"eth_chainId","params":[],"id":1}' \
  http://localhost:8545

# Update .env accordingly
# Mainnet: CHAIN_ID=1
# Sepolia: CHAIN_ID=********
```

### Issue: "Fork fails"
```bash
# Make sure your local node is fully synced
# Check latest block vs network
curl -X POST -H "Content-Type: application/json" \
  --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
  http://localhost:8545

# Try forking from specific block
npx hardhat node --fork http://localhost:8545 --fork-block-number ********
```

## 🎯 Best Practices

1. **Keep local node synced** for realistic testing
2. **Use different ports** to avoid conflicts
3. **Test with whale accounts** for realistic liquidity
4. **Create artificial opportunities** for testing
5. **Monitor both nodes** (local + Hardhat) during testing
6. **Reset Hardhat frequently** for clean testing

## 💡 Pro Tips

- **Use your local mainnet node** for most realistic testing
- **Switch to Sepolia** for safer experimentation
- **Create test scenarios** with time manipulation
- **Test edge cases** with account impersonation
- **Monitor gas usage** even though it's free
- **Test with real MEV opportunities** from your local mempool

This setup gives you **maximum testing power** with **real blockchain data** from your own infrastructure! 🚀
