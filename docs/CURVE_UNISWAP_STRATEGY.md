# 🔄 Curve + Uniswap V3 Flashloan Arbitrage Strategy

## Overview

This configuration optimizes the MEV bot for **stablecoin arbitrage** using **Curve Finance** (low-slippage stablecoin swaps) and **Uniswap V3** (high liquidity), targeting **USDC ↔ DAI ↔ USDT** arbitrage opportunities.

## 🎯 Strategy Configuration

### **DEX Selection**
- **DEX 1**: **Curve Finance** - Ultra-low slippage stablecoin swaps (0.04% fees)
- **DEX 2**: **Uniswap V3** - High liquidity with concentrated positions (0.05%, 0.3%, 1% fees)

### **Token Focus**
- **Primary Token**: **USDC** (highest liquidity for flashloans)
- **Target Tokens**: **DAI**, **USDT** (stablecoin arbitrage pairs)
- **All Pairs Enabled**: USDC↔DAI, USDC↔USDT, DAI↔USDT

### **Arbitrage Threshold**
- **Minimum Spread**: **0.1%** (optimized for stablecoin price differences)
- **Liquidity Threshold**: **$500,000** (ensures sufficient depth)

## 📊 Why This Strategy Works

### **Curve Finance Advantages**
- ✅ **Ultra-Low Slippage**: Optimized for stablecoin swaps
- ✅ **Low Fees**: 0.04% vs 0.3% on other DEXs
- ✅ **Stable Pricing**: Designed for 1:1 stablecoin ratios
- ✅ **Deep Liquidity**: $100M+ in 3pool (USDC/DAI/USDT)

### **Uniswap V3 Advantages**
- ✅ **High Liquidity**: Concentrated liquidity positions
- ✅ **Multiple Fee Tiers**: 0.05%, 0.3%, 1% options
- ✅ **Price Discovery**: Real-time market pricing
- ✅ **Fast Execution**: Optimized for MEV strategies

### **Arbitrage Opportunities**
1. **Curve → Uniswap V3**: Buy cheap on Curve, sell higher on Uniswap
2. **Uniswap V3 → Curve**: Buy cheap on Uniswap, sell higher on Curve
3. **Cross-Stablecoin**: USDC→DAI→USDT arbitrage chains

## 🔧 Current Configuration

```bash
# DEX Configuration - Curve + Uniswap V3
FLASHLOAN_DEX_PAIRS=CURVE,UNISWAP_V3
FLASHLOAN_BUY_DEX=CURVE
FLASHLOAN_SELL_DEX=UNISWAP_V3
ENABLE_CROSS_DEX_ARBITRAGE=true
MIN_ARBITRAGE_SPREAD=0.1

# Token Configuration - Stablecoin Focus
FLASHLOAN_TOKENS=USDC,DAI,USDT
FLASHLOAN_PRIMARY_TOKEN=USDC
FLASHLOAN_TARGET_TOKENS=DAI,USDT
ENABLE_ALL_TOKEN_PAIRS=true
MIN_TOKEN_LIQUIDITY_USD=500000
```

## 🚀 Execution Flow

### **Step 1: Opportunity Detection**
1. **Scan Curve 3pool**: Check USDC/DAI/USDT prices
2. **Scan Uniswap V3**: Check corresponding pairs across fee tiers
3. **Calculate Spread**: Identify price differences ≥ 0.1%
4. **Validate Liquidity**: Ensure sufficient depth for trade size

### **Step 2: Flashloan Execution**
1. **Borrow USDC**: Flashloan from Aave V3 (0.09% fee) or Balancer (0% fee)
2. **Buy on Curve**: Swap USDC → DAI/USDT (0.04% fee, low slippage)
3. **Sell on Uniswap V3**: Swap DAI/USDT → USDC (0.05-1% fee, market price)
4. **Repay Loan**: Return borrowed USDC + premium
5. **Keep Profit**: Net profit after all fees

### **Step 3: Profit Calculation**
```
Profit = (Sell_Price - Buy_Price) * Amount - Flashloan_Fee - Gas_Costs
```

## 📈 Expected Performance

### **Profit Margins**
- **Conservative**: 0.1-0.3% per trade
- **Moderate**: 0.3-0.7% per trade  
- **Aggressive**: 0.7-2% per trade (during volatility)

### **Trade Frequency**
- **High**: 10-50 opportunities per day
- **Stablecoin Volatility**: More opportunities during market stress
- **Cross-Chain Events**: Increased arbitrage during bridge delays

### **Risk Factors**
- **Low Risk**: Stablecoin pairs are inherently stable
- **Slippage Risk**: Minimized by Curve's design
- **Gas Risk**: Offset by profit margins
- **Liquidity Risk**: Mitigated by high thresholds

## 🔍 Market Scenarios

### **Scenario 1: Normal Market Conditions**
- **Spread**: 0.1-0.2%
- **Frequency**: 10-20 trades/day
- **Profit**: $50-200 per trade (on $50k flashloan)

### **Scenario 2: Market Volatility**
- **Spread**: 0.3-1%
- **Frequency**: 30-50 trades/day
- **Profit**: $150-500 per trade

### **Scenario 3: Depegging Events**
- **Spread**: 1-5%
- **Frequency**: Continuous opportunities
- **Profit**: $500-2500 per trade
- **Risk**: Higher, but still manageable with stablecoins

## ⚙️ Technical Implementation

### **Curve Integration**
- **3pool Contract**: `0xbEbc44782C7dB0a1A60Cb6fe97d0b483032FF1C7`
- **Token Indices**: DAI=0, USDC=1, USDT=2
- **Functions**: `get_dy()`, `exchange()`, `balances()`
- **Fee**: 0.04% (4 basis points)

### **Uniswap V3 Integration**
- **Router**: `0xE592427A0AEce92De3Edee1F18E0157C05861564`
- **Fee Tiers**: 500 (0.05%), 3000 (0.3%), 10000 (1%)
- **Functions**: `exactInputSingle()`, `quoteExactInputSingle()`
- **Concentrated Liquidity**: Optimized price ranges

### **Flashloan Providers**
- **Aave V3**: 0.09% fee, high reliability
- **Balancer V2**: 0% fee, optimal for small spreads
- **Hybrid Contract**: Automatically chooses best provider

## 🎯 Optimization Tips

### **For Maximum Profit**
1. **Monitor Multiple Fee Tiers**: Check all Uniswap V3 fee levels
2. **Dynamic Sizing**: Adjust flashloan amounts based on liquidity
3. **Gas Optimization**: Use optimal gas prices for timing
4. **Cross-Pair Arbitrage**: USDC→DAI→USDT→USDC chains

### **For Risk Management**
1. **Liquidity Checks**: Ensure sufficient pool depth
2. **Slippage Limits**: Set maximum acceptable slippage
3. **Position Sizing**: Don't exceed pool capacity
4. **Emergency Stops**: Halt during extreme volatility

### **For Efficiency**
1. **Batch Operations**: Combine multiple arbitrages
2. **MEV Protection**: Use private mempools
3. **Timing Optimization**: Execute during low gas periods
4. **Monitoring**: Track success rates and adjust parameters

## 📊 Performance Metrics

### **Success Indicators**
- **Profit Margin**: > 0.1% after all fees
- **Execution Speed**: < 15 seconds per trade
- **Success Rate**: > 80% of attempted trades
- **Gas Efficiency**: < 300k gas per flashloan

### **Monitoring Dashboard**
- **Real-time Spreads**: Curve vs Uniswap V3 prices
- **Liquidity Depth**: Available amounts at target spreads
- **Gas Tracker**: Current network congestion
- **Profit History**: Daily/weekly performance

## 🚨 Important Considerations

### **Network Requirements**
- **Mainnet Only**: Curve 3pool not available on testnets
- **High Liquidity**: Requires significant stablecoin pools
- **Gas Costs**: Factor in current network fees

### **Market Conditions**
- **Stablecoin Stability**: Strategy depends on stable pegs
- **Liquidity Availability**: Monitor pool depths
- **Competition**: Other MEV bots targeting same opportunities

### **Technical Requirements**
- **Fast RPC**: Low-latency blockchain access
- **Reliable Infrastructure**: Minimal downtime
- **Accurate Pricing**: Real-time price feeds

## 🔄 Alternative Configurations

### **Conservative (Lower Risk)**
```bash
MIN_ARBITRAGE_SPREAD=0.2
MIN_TOKEN_LIQUIDITY_USD=1000000
ENABLE_ALL_TOKEN_PAIRS=false
FLASHLOAN_TARGET_TOKENS=DAI  # Focus on USDC↔DAI only
```

### **Aggressive (Higher Profit)**
```bash
MIN_ARBITRAGE_SPREAD=0.05
MIN_TOKEN_LIQUIDITY_USD=200000
ENABLE_ALL_TOKEN_PAIRS=true
# Include more volatile stablecoins if available
```

---

**💡 This strategy leverages the best of both worlds: Curve's ultra-low slippage for stablecoins and Uniswap V3's high liquidity, creating consistent arbitrage opportunities in the stablecoin ecosystem.**
