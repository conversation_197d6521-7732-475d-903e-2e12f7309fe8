# 🔄 DEX Configuration for Flashloan Arbitrage

## Overview

The MEV bot now supports configurable DEX selection for flashloan arbitrage attacks. You can choose which DEX exchanges to use for arbitrage opportunities, allowing you to optimize for the best profit margins and liquidity.

## 🎯 Supported DEX Exchanges

### **Mainnet (CHAIN_ID=1)**
- ✅ **Uniswap V2** - 0.3% fees, high liquidity
- ✅ **Uniswap V3** - Variable fees (0.05%, 0.3%, 1%), concentrated liquidity
- ✅ **SushiSwap** - 0.3% fees, competitive with Uniswap V2
- ✅ **PancakeSwap** - 0.25% fees (Ethereum deployment)
- ✅ **Balancer** - Variable fees, multi-token pools

### **Sepolia Testnet (CHAIN_ID=11155111)**
- ✅ **Uniswap V2** - Available for testing
- ✅ **Uniswap V3** - Available for testing
- ❌ **SushiSwap** - Not deployed on Sepolia
- ❌ **PancakeSwap** - Not deployed on Sepolia
- ✅ **Balancer** - Available (same address on all networks)

## 📝 Environment Variables

### **FLASHLOAN_DEX_PAIRS**
Choose which DEXs to scan for arbitrage opportunities (comma-separated).

```bash
# Use Uniswap V2 and V3 (recommended for Sepolia)
FLASHLOAN_DEX_PAIRS=UNISWAP_V2,UNISWAP_V3

# Use all available DEXs on mainnet
FLASHLOAN_DEX_PAIRS=UNISWAP_V2,UNISWAP_V3,SUSHISWAP,PANCAKESWAP

# Focus on specific DEXs for targeted arbitrage
FLASHLOAN_DEX_PAIRS=UNISWAP_V2,SUSHISWAP
```

### **FLASHLOAN_BUY_DEX** & **FLASHLOAN_SELL_DEX**
Specify exact DEXs for buy/sell operations when `ENABLE_CROSS_DEX_ARBITRAGE=false`.

```bash
# Buy on Uniswap V2, sell on Uniswap V3
FLASHLOAN_BUY_DEX=UNISWAP_V2
FLASHLOAN_SELL_DEX=UNISWAP_V3

# Buy on SushiSwap, sell on Uniswap V2 (mainnet only)
FLASHLOAN_BUY_DEX=SUSHISWAP
FLASHLOAN_SELL_DEX=UNISWAP_V2
```

### **ENABLE_CROSS_DEX_ARBITRAGE**
Enable automatic scanning across all configured DEX combinations.

```bash
# Automatically find best DEX combinations
ENABLE_CROSS_DEX_ARBITRAGE=true

# Use specific buy/sell DEX configuration
ENABLE_CROSS_DEX_ARBITRAGE=false
```

### **MIN_ARBITRAGE_SPREAD**
Minimum price difference percentage to trigger arbitrage.

```bash
# Require at least 0.5% price difference
MIN_ARBITRAGE_SPREAD=0.5

# More aggressive: 0.2% minimum spread
MIN_ARBITRAGE_SPREAD=0.2

# Conservative: 1% minimum spread
MIN_ARBITRAGE_SPREAD=1.0
```

## 🚀 Configuration Examples

### **Example 1: Sepolia Testing**
```bash
CHAIN_ID=11155111
FLASHLOAN_DEX_PAIRS=UNISWAP_V2,UNISWAP_V3
FLASHLOAN_BUY_DEX=UNISWAP_V2
FLASHLOAN_SELL_DEX=UNISWAP_V3
ENABLE_CROSS_DEX_ARBITRAGE=true
MIN_ARBITRAGE_SPREAD=0.5
```

### **Example 2: Mainnet Multi-DEX**
```bash
CHAIN_ID=1
FLASHLOAN_DEX_PAIRS=UNISWAP_V2,UNISWAP_V3,SUSHISWAP,PANCAKESWAP
ENABLE_CROSS_DEX_ARBITRAGE=true
MIN_ARBITRAGE_SPREAD=0.3
```

### **Example 3: Targeted Uniswap V2 ↔ SushiSwap**
```bash
CHAIN_ID=1
FLASHLOAN_DEX_PAIRS=UNISWAP_V2,SUSHISWAP
FLASHLOAN_BUY_DEX=SUSHISWAP
FLASHLOAN_SELL_DEX=UNISWAP_V2
ENABLE_CROSS_DEX_ARBITRAGE=false
MIN_ARBITRAGE_SPREAD=0.4
```

## 🔍 How It Works

### **Cross-DEX Arbitrage Mode** (`ENABLE_CROSS_DEX_ARBITRAGE=true`)
1. **Scan All Combinations**: Bot checks all possible DEX pairs from `FLASHLOAN_DEX_PAIRS`
2. **Price Comparison**: Compares prices across all configured DEXs
3. **Best Route Selection**: Automatically selects the most profitable arbitrage route
4. **Dynamic Optimization**: Adapts to market conditions in real-time

### **Specific DEX Mode** (`ENABLE_CROSS_DEX_ARBITRAGE=false`)
1. **Fixed Route**: Uses `FLASHLOAN_BUY_DEX` → `FLASHLOAN_SELL_DEX`
2. **Predictable Execution**: Always uses the same DEX pair
3. **Lower Complexity**: Simpler logic, faster execution
4. **Targeted Strategy**: Focus on specific DEX inefficiencies

## 📊 DEX Characteristics

| DEX | Fee | Liquidity | Best For |
|-----|-----|-----------|----------|
| **Uniswap V2** | 0.3% | Very High | Stable pairs, large trades |
| **Uniswap V3** | 0.05-1% | Concentrated | Efficient capital, specific ranges |
| **SushiSwap** | 0.3% | High | Alternative to Uniswap V2 |
| **PancakeSwap** | 0.25% | Medium | Lower fees than Uniswap |
| **Balancer** | Variable | Medium | Multi-token arbitrage |

## 🎯 Strategy Recommendations

### **For Beginners (Sepolia)**
```bash
FLASHLOAN_DEX_PAIRS=UNISWAP_V2,UNISWAP_V3
ENABLE_CROSS_DEX_ARBITRAGE=true
MIN_ARBITRAGE_SPREAD=0.5
```

### **For Maximum Opportunities (Mainnet)**
```bash
FLASHLOAN_DEX_PAIRS=UNISWAP_V2,UNISWAP_V3,SUSHISWAP,PANCAKESWAP
ENABLE_CROSS_DEX_ARBITRAGE=true
MIN_ARBITRAGE_SPREAD=0.3
```

### **For Specific Strategies**
```bash
# Target Uniswap V2/V3 spread differences
FLASHLOAN_DEX_PAIRS=UNISWAP_V2,UNISWAP_V3
FLASHLOAN_BUY_DEX=UNISWAP_V2
FLASHLOAN_SELL_DEX=UNISWAP_V3
ENABLE_CROSS_DEX_ARBITRAGE=false
```

## 🔧 Advanced Configuration

### **Network-Specific Router Addresses**
The bot automatically selects the correct router addresses based on `CHAIN_ID`:

```typescript
// Mainnet (CHAIN_ID=1)
UNISWAP_V2_ROUTER: '0x7a250d5630B4cF539739dF2C5dAcb4c659F2488D'
UNISWAP_V3_ROUTER: '0xE592427A0AEce92De3Edee1F18E0157C05861564'
SUSHISWAP_ROUTER: '0xd9e1cE17f2641f24aE83637ab66a2cca9C378B9F'

// Sepolia (CHAIN_ID=11155111)
UNISWAP_V2_ROUTER: '0x86dcd3293C53Cf8EFd7303B57beb2a3F671dDE98'
UNISWAP_V3_ROUTER: '0x3bFA4769FB09eefC5a80d6E87c3B9C650f7Ae48E'
```

### **Fee Tier Selection**
For Uniswap V3, the bot automatically selects appropriate fee tiers:
- **500** (0.05%) - Stable pairs
- **3000** (0.3%) - Standard pairs
- **10000** (1%) - Exotic pairs

## 🚨 Important Notes

### **Network Availability**
- **Sepolia**: Only Uniswap V2/V3 and Balancer are available
- **Mainnet**: All DEXs are supported
- **Invalid DEXs**: Automatically filtered out based on network

### **Gas Considerations**
- **More DEXs** = More scanning = Higher gas costs
- **Cross-DEX mode** = More complex logic = Slightly higher gas
- **Specific DEX mode** = Simpler execution = Lower gas

### **Liquidity Requirements**
- Ensure sufficient liquidity on chosen DEXs
- Monitor slippage on smaller DEXs
- Consider pool depth for large trades

## 🔄 Migration Guide

### **From Old Configuration**
If you're upgrading from the previous version:

1. **Add new variables** to your `.env`:
```bash
FLASHLOAN_DEX_PAIRS=UNISWAP_V2,UNISWAP_V3
FLASHLOAN_BUY_DEX=UNISWAP_V2
FLASHLOAN_SELL_DEX=UNISWAP_V3
ENABLE_CROSS_DEX_ARBITRAGE=true
MIN_ARBITRAGE_SPREAD=0.5
```

2. **Keep existing settings** - they will continue to work
3. **Test on Sepolia** before mainnet deployment

### **Backward Compatibility**
- Old configurations continue to work
- New features are opt-in
- Default values maintain existing behavior

---

**💡 Pro Tip**: Start with `ENABLE_CROSS_DEX_ARBITRAGE=true` to discover the most profitable DEX combinations, then switch to specific DEX mode for optimized execution once you identify the best pairs.
