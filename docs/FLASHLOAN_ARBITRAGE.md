# 🔥 Flashloan-Based MEV Arbitrage

## Overview

The flashloan-based MEV attack is an advanced strategy that allows the bot to execute arbitrage opportunities without requiring upfront capital. It works by:

1. **💰 Flashloan USDC** from Aave V3
2. **🔄 Execute Arbitrage** between DEX A → DEX B
3. **💸 Repay Flashloan** with premium
4. **💎 Keep Profit** from the arbitrage

## How It Works

### Transaction Flow

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   1. Flashloan  │───▶│  2. Arbitrage   │───▶│  3. <PERSON>ay Loan  │
│   1000 USDC     │    │   DEX A → DEX B │    │  1000.9 USDC    │
│   from Aave     │    │   Profit: 20    │    │  Keep: 19.1     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Smart Contract Architecture

The flashloan arbitrage uses a dedicated smart contract (`FlashloanArbitrage.sol`) that:

- Implements Aave's `FlashLoanSimpleReceiverBase`
- Receives flashloan callback in `executeOperation()`
- Executes arbitrage between Uniswap V2 and V3
- Automatically repays the loan with premium
- Transfers profit to the bot owner

## Configuration

### Environment Variables

Add to your `.env` file:

```bash
# Enable flashloan attacks
ENABLE_FLASHLOAN_ATTACKS=true

# Flashloan contract address (deploy first)
FLASHLOAN_CONTRACT_ADDRESS=0x...
```

### Aave V3 Addresses (Sepolia)

```typescript
AAVE_POOL_ADDRESSES_PROVIDER: '******************************************'
AAVE_POOL: '******************************************'
```

## Deployment

### 1. Deploy the Flashloan Contract

```bash
# Install dependencies
npm install

# Deploy to Sepolia
npx hardhat run scripts/deploy-flashloan.js --network sepolia
```

### 2. Update Configuration

```bash
# Add contract address to .env
echo "FLASHLOAN_CONTRACT_ADDRESS=0x..." >> .env
```

### 3. Fund the Contract

The contract needs ETH for gas fees:

```bash
# Send some Sepolia ETH to the contract
# You can get Sepolia ETH from: https://sepoliafaucet.com/
```

## Strategy Details

### Opportunity Detection

The bot scans for arbitrage opportunities by:

1. **Price Comparison**: Comparing prices between Uniswap V2 and V3
2. **Profit Calculation**: Estimating profit after flashloan premium and gas
3. **Confidence Scoring**: Rating opportunities based on profit margin

### Execution Process

```typescript
// 1. Detect arbitrage opportunity
const opportunity = await flashloanStrategy.scanForFlashloanOpportunities();

// 2. Calculate optimal flashloan amount
const flashloanAmount = calculateOptimalAmount(opportunity);

// 3. Execute flashloan arbitrage
const success = await flashloanStrategy.executeFlashloan(route);
```

### Profit Calculation

```
Gross Profit = Arbitrage Profit
Net Profit = Gross Profit - Flashloan Premium - Gas Costs

Flashloan Premium = Amount × 0.09% (9 basis points)
```

## Risk Management

### Safety Features

- **Minimum Profit Threshold**: 2% minimum profit required
- **Confidence Scoring**: Only executes high-confidence opportunities (75%+)
- **Gas Estimation**: Accounts for gas costs in profit calculation
- **Slippage Protection**: Built-in slippage tolerance
- **Emergency Stop**: Can halt operations if needed

### Risk Factors

- **MEV Competition**: Other bots may compete for same opportunities
- **Gas Price Volatility**: High gas prices can eliminate profits
- **Slippage**: Large trades may experience price impact
- **Smart Contract Risk**: Contract bugs could cause losses

## Monitoring

### Log Messages

```bash
🔍 Scanning for flashloan arbitrage opportunities...
🔥 High-confidence flashloan opportunity detected (85%)
🚀 Executing Flashloan Arbitrage Attack
💰 Flashloan Amount: 5000.0 USDC
💎 Expected Profit: 0.05 ETH
✅ Flashloan arbitrage executed successfully
```

### Performance Metrics

- **Success Rate**: Percentage of successful executions
- **Average Profit**: Mean profit per successful trade
- **Gas Efficiency**: Gas cost vs profit ratio
- **Opportunity Frequency**: How often opportunities are found

## Testing

### Dry Run Mode

Test without real transactions:

```bash
# Enable dry run in .env
DRY_RUN=true

# Run the bot
npm run dev
```

### Simulation Output

```
DRY RUN: Simulating flashloan arbitrage execution...
Step 1: 💰 Flashloan USDC from Aave
  └─ Borrowing 5000.0 USDC
  └─ Premium: 4.5 USDC
Step 2: 🔄 Execute Arbitrage
  └─ Buy on UNISWAP-V2
  └─ Sell on UNISWAP-V3
Step 3: 💸 Repay Flashloan
  └─ Repaying 5004.5 USDC
Step 4: 💎 Keep Profit
  └─ Profit: 0.05 ETH
✅ Flashloan arbitrage simulation completed successfully
```

## Advanced Features

### Multi-Token Support

The strategy can be extended to support multiple tokens:

```typescript
const supportedTokens = ['USDC', 'USDT', 'DAI', 'WETH'];
```

### Cross-DEX Arbitrage

Currently supports:
- Uniswap V2 ↔ Uniswap V3
- Can be extended to Balancer, Curve, etc.

### Dynamic Amount Optimization

The bot automatically calculates optimal flashloan amounts based on:
- Pool liquidity
- Price impact
- Gas costs
- Profit margins

## Troubleshooting

### Common Issues

1. **"Insufficient balance to repay flashloan"**
   - Arbitrage wasn't profitable enough
   - Increase minimum profit threshold

2. **"Flashloan simulation failed"**
   - Check contract deployment
   - Verify Aave pool addresses

3. **"No flashloan opportunities found"**
   - Market conditions may not be favorable
   - Adjust scanning parameters

### Debug Mode

Enable detailed logging:

```bash
LOG_LEVEL=debug npm run dev
```

## Security Considerations

- **Contract Ownership**: Only owner can execute flashloans
- **Reentrancy Protection**: Built into Aave's flashloan system
- **Access Control**: Proper permission checks
- **Emergency Functions**: Withdraw capabilities for stuck funds

## Future Enhancements

- **Multi-hop Arbitrage**: Complex arbitrage paths
- **Cross-chain Arbitrage**: Bridge-based opportunities
- **Dynamic Fee Optimization**: Adjust for network congestion
- **ML-based Prediction**: Predict profitable opportunities

---

**⚠️ Disclaimer**: This is for educational purposes on Sepolia testnet. Always test thoroughly and understand the risks before using on mainnet.
