# 🏠 Local ETH RPC Node Setup Guide

## 🎯 Why Use a Local Ethereum Node?

Running your own Ethereum node provides **maximum performance** for MEV bots:

- **🚀 Zero Rate Limits**: No API restrictions whatsoever
- **⚡ Fastest Response**: Direct connection, no network latency
- **🔒 Complete Control**: Your infrastructure, your rules
- **💰 Cost Effective**: No API fees after initial setup
- **🎯 MEV Optimized**: Direct mempool access and custom optimizations
- **🛡️ Maximum Reliability**: No dependency on external services

## 🚀 Quick Setup

### Automated Setup (Recommended)
```bash
# Automatically detect and configure your local node
npm run setup:local
```

### Manual Setup
```bash
# Use local node configuration
cp .env.local .env

# Edit .env to match your node's ports and network
# Then start the bot
npm run dev:local
```

## 🔧 Local Node Options

### Option 1: Geth (Most Popular)

**Installation:**
```bash
# macOS
brew install ethereum

# Ubuntu/Debian
sudo apt-get install ethereum

# Windows
# Download from: https://geth.ethereum.org/downloads/
```

**Start Mainnet Node:**
```bash
geth --http --http.api eth,net,web3 --ws --ws.api eth,net,web3
```

**Start Sepolia Testnet Node:**
```bash
geth --sepolia --http --http.api eth,net,web3 --ws --ws.api eth,net,web3
```

**URLs:**
- HTTP: `http://localhost:8545`
- WebSocket: `ws://localhost:8546`

### Option 2: Erigon (Faster Sync)

**Installation:**
```bash
git clone https://github.com/ledgerwatch/erigon.git
cd erigon
make erigon
```

**Start Mainnet Node:**
```bash
./build/bin/erigon --http.api eth,net,web3 --ws
```

**Start Sepolia Testnet Node:**
```bash
./build/bin/erigon --chain sepolia --http.api eth,net,web3 --ws
```

**URLs:**
- HTTP: `http://localhost:8545`
- WebSocket: `ws://localhost:8546`

### Option 3: Nethermind

**Installation:**
```bash
# Download from: https://nethermind.io/
```

**Start Node:**
```bash
./Nethermind.Runner --config mainnet --JsonRpc.Enabled true --WebSocketsEnabled true
```

## ⚙️ Configuration

### Direct Local Node (.env.local)

```env
# Local node URLs (highest priority)
LOCAL_NODE_URL=http://localhost:8545
LOCAL_NODE_WS=ws://localhost:8546

# Primary RPC configuration
RPC_URL=http://localhost:8545
MEMPOOL_WEBSOCKET_URL=ws://localhost:8546

# Network configuration
CHAIN_ID=1  # 1 for mainnet, 11155111 for Sepolia

# Remove external providers (optional)
# ALCHEMY_API_KEY=
# QUICKNODE_ENDPOINT=
```

### Hardhat Fork with Local Node (.env.hardhat-local)

```env
# Use local node as fork source
MAINNET_FORK_URL=http://localhost:8545
SEPOLIA_FORK_URL=http://localhost:8545

# Hardhat configuration
CHAIN_ID=31337
RPC_URL=http://localhost:8545
```

## 🔍 Testing Your Setup

### Check Node Connection
```bash
# Test if your node is running
curl -X POST -H "Content-Type: application/json" \
  --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
  http://localhost:8545

# Should return something like:
# {"jsonrpc":"2.0","id":1,"result":"0x1234567"}
```

### Automated Testing
```bash
# Run the setup script to test connection
npm run setup:local
```

### Integration Test
```bash
# Test with the MEV bot
npm run dev:local
```

## 🚀 Usage Examples

### Production MEV Bot with Local Mainnet Node
```bash
# 1. Start your Geth mainnet node
geth --http --http.api eth,net,web3 --ws --ws.api eth,net,web3

# 2. Configure for local node
npm run setup:local

# 3. Deploy contracts (if needed)
npm run deploy:mainnet

# 4. Start MEV bot
npm run dev:local
```

### Testing with Local Sepolia Node
```bash
# 1. Start Sepolia node
geth --sepolia --http --http.api eth,net,web3 --ws --ws.api eth,net,web3

# 2. Configure for local node
npm run setup:local

# 3. Deploy contracts
npm run deploy:sepolia

# 4. Start MEV bot
npm run dev:local
```

### Hardhat Testing with Local Node Fork
```bash
# 1. Make sure your local node is running
# 2. Configure Hardhat to fork from local node
cp .env.hardhat-local .env

# 3. Start Hardhat fork
npx hardhat node --fork http://localhost:8545

# 4. Deploy and test
npm run hardhat:deploy
npm run dev:hardhat
```

## 📊 Performance Comparison

| Setup Type | Rate Limits | Latency | Control | Cost | MEV Performance |
|------------|-------------|---------|---------|------|-----------------|
| **Local Node** | ✅ None | ⚡ ~1ms | 🔒 Full | 💰 Hardware only | 🎯 Maximum |
| **Alchemy** | ⚠️ 300M/month | 🌐 ~50ms | ⚠️ Limited | 💳 API fees | ⭐⭐⭐⭐ |
| **QuickNode** | ✅ Unlimited* | 🌐 ~30ms | ⚠️ Limited | 💳 Subscription | ⭐⭐⭐⭐⭐ |
| **Public RPC** | ❌ Very low | 🌐 ~100ms | ❌ None | 💰 Free | ⭐⭐ |

*Unlimited on paid plans

## 🛠️ Node Optimization for MEV

### Geth Optimization
```bash
geth \
  --http --http.api eth,net,web3,txpool \
  --ws --ws.api eth,net,web3,txpool \
  --txpool.globalslots 10000 \
  --txpool.globalqueue 5000 \
  --cache 4096 \
  --maxpeers 100
```

### Erigon Optimization
```bash
./build/bin/erigon \
  --http.api eth,net,web3,txpool \
  --ws \
  --txpool.globalslots 10000 \
  --txpool.globalqueue 5000
```

### Hardware Recommendations

**Minimum:**
- CPU: 4 cores
- RAM: 16GB
- Storage: 2TB SSD
- Network: 100 Mbps

**Recommended for MEV:**
- CPU: 8+ cores
- RAM: 32GB+
- Storage: 4TB NVMe SSD
- Network: 1 Gbps

## 🔧 Troubleshooting

### Node Not Starting
```bash
# Check if port is in use
lsof -i :8545

# Kill existing process
kill -9 $(lsof -t -i:8545)

# Check disk space
df -h

# Check logs
tail -f ~/.ethereum/geth.log
```

### Connection Issues
```bash
# Test connection
curl -X POST -H "Content-Type: application/json" \
  --data '{"jsonrpc":"2.0","method":"net_version","params":[],"id":1}' \
  http://localhost:8545

# Check firewall
sudo ufw status

# Check if APIs are enabled
curl -X POST -H "Content-Type: application/json" \
  --data '{"jsonrpc":"2.0","method":"web3_clientVersion","params":[],"id":1}' \
  http://localhost:8545
```

### Sync Issues
```bash
# Check sync status
curl -X POST -H "Content-Type: application/json" \
  --data '{"jsonrpc":"2.0","method":"eth_syncing","params":[],"id":1}' \
  http://localhost:8545

# Fast sync (Geth)
geth --syncmode fast

# Snap sync (Geth - fastest)
geth --syncmode snap
```

## 📈 Monitoring Your Node

### Basic Monitoring
```bash
# Check latest block
curl -s -X POST -H "Content-Type: application/json" \
  --data '{"jsonrpc":"2.0","method":"eth_blockNumber","params":[],"id":1}' \
  http://localhost:8545 | jq -r .result | xargs printf "%d\n"

# Check peer count
curl -s -X POST -H "Content-Type: application/json" \
  --data '{"jsonrpc":"2.0","method":"net_peerCount","params":[],"id":1}' \
  http://localhost:8545 | jq -r .result | xargs printf "%d\n"
```

### Advanced Monitoring
- **Grafana + Prometheus**: For detailed metrics
- **Node monitoring tools**: Like `geth monitor`
- **Custom scripts**: Monitor block times, peer count, etc.

## 🎯 Best Practices

1. **Use SSD storage** for faster sync and operation
2. **Monitor disk space** - blockchain grows continuously
3. **Keep node updated** for latest optimizations
4. **Use fast internet** for better peer connectivity
5. **Monitor mempool** for MEV opportunities
6. **Backup node data** to avoid re-syncing
7. **Use multiple nodes** for redundancy

## 🔄 Maintenance

### Regular Tasks
```bash
# Update Geth
brew upgrade ethereum  # macOS
sudo apt update && sudo apt upgrade ethereum  # Ubuntu

# Clean up old data (if needed)
geth removedb

# Backup keystore
cp -r ~/.ethereum/keystore ~/backup/
```

### Emergency Procedures
```bash
# Quick restart
pkill geth
geth --http --http.api eth,net,web3 --ws --ws.api eth,net,web3

# Force resync (last resort)
geth removedb
geth --syncmode snap
```

---

## 💡 Pro Tips

- **Start with Sepolia** for testing before mainnet
- **Use snap sync** for fastest initial sync
- **Monitor system resources** during sync
- **Consider running multiple nodes** for different networks
- **Use local node for production**, external APIs for backup

Your local node gives you the **ultimate MEV advantage** - no limits, maximum speed, complete control! 🚀
