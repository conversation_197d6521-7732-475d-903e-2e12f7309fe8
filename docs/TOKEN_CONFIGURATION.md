# 💰 Token Configuration for Flashloan Arbitrage

## Overview

The MEV bot now supports configurable ERC-20 token selection for flashloan arbitrage attacks. You can choose which tokens to use for arbitrage opportunities, focusing on the most liquid and profitable pairs from the top tokens on Ethereum.

## 🎯 Supported ERC-20 Tokens

### **Stablecoins (Primary Flashloan Tokens)**
- ✅ **USDC** - USD Coin (6 decimals) - Most liquid, best for flashloans
- ✅ **USDT** - Tether USD (6 decimals) - High volume, good arbitrage opportunities  
- ✅ **DAI** - Dai Stablecoin (18 decimals) - Decentralized, stable value

### **Major Cryptocurrencies**
- ✅ **WETH** - Wrapped Ether (18 decimals) - Highest liquidity, best arbitrage target
- ✅ **WBTC** - Wrapped Bitcoin (8 decimals) - High value, good price movements

### **DeFi Tokens (High Liquidity)**
- ✅ **UNI** - Uniswap (18 decimals) - Native DEX token, high volume
- ✅ **LINK** - Chainlink (18 decimals) - Oracle token, consistent demand
- ✅ **AAVE** - Aave (18 decimals) - Lending protocol token
- ✅ **CRV** - Curve DAO Token (18 decimals) - DEX aggregator token
- ✅ **COMP** - Compound (18 decimals) - Lending protocol token

### **Layer 2 Tokens**
- ✅ **OP** - Optimism (18 decimals) - L2 scaling solution
- ✅ **MATIC** - Polygon (18 decimals) - Sidechain solution

### **Meme Tokens (High Volatility)**
- ✅ **SHIB** - Shiba Inu (18 decimals) - High volatility, good for arbitrage
- ✅ **APE** - ApeCoin (18 decimals) - NFT ecosystem token

### **Exchange Tokens**
- ✅ **BNB** - Binance Coin (18 decimals) - Exchange token
- ✅ **FTT** - FTX Token (18 decimals) - Exchange token

## 📝 Environment Variables

### **FLASHLOAN_TOKENS**
Choose which tokens to scan for arbitrage opportunities (comma-separated).

```bash
# Focus on stablecoins and major tokens (recommended)
FLASHLOAN_TOKENS=USDC,WETH,USDT,DAI

# Include DeFi tokens for more opportunities
FLASHLOAN_TOKENS=USDC,WETH,USDT,DAI,UNI,LINK,AAVE

# All available tokens (maximum opportunities)
FLASHLOAN_TOKENS=USDC,WETH,USDT,DAI,WBTC,UNI,LINK,AAVE,CRV,COMP
```

### **FLASHLOAN_PRIMARY_TOKEN**
The token to use for flashloans (should be highly liquid).

```bash
# USDC is recommended (highest liquidity, lowest fees)
FLASHLOAN_PRIMARY_TOKEN=USDC

# USDT alternative (also very liquid)
FLASHLOAN_PRIMARY_TOKEN=USDT

# WETH for ETH-based arbitrage
FLASHLOAN_PRIMARY_TOKEN=WETH
```

### **FLASHLOAN_TARGET_TOKENS**
Tokens to trade against the primary token for arbitrage.

```bash
# Conservative: Major tokens only
FLASHLOAN_TARGET_TOKENS=WETH,USDT,DAI

# Aggressive: Include DeFi tokens
FLASHLOAN_TARGET_TOKENS=WETH,USDT,DAI,UNI,LINK,AAVE

# Maximum: Include volatile tokens
FLASHLOAN_TARGET_TOKENS=WETH,USDT,DAI,UNI,LINK,AAVE,SHIB,APE
```

### **ENABLE_ALL_TOKEN_PAIRS**
Enable scanning all configured tokens against each other.

```bash
# Use specific primary → target token pairs
ENABLE_ALL_TOKEN_PAIRS=false

# Scan all possible token combinations
ENABLE_ALL_TOKEN_PAIRS=true
```

### **MIN_TOKEN_LIQUIDITY_USD**
Minimum liquidity threshold to consider a token pair.

```bash
# Conservative: High liquidity only
MIN_TOKEN_LIQUIDITY_USD=1000000

# Moderate: Medium liquidity
MIN_TOKEN_LIQUIDITY_USD=100000

# Aggressive: Lower liquidity (higher risk)
MIN_TOKEN_LIQUIDITY_USD=50000
```

## 🚀 Configuration Examples

### **Example 1: Conservative (Sepolia Testing)**
```bash
FLASHLOAN_TOKENS=USDC,WETH
FLASHLOAN_PRIMARY_TOKEN=USDC
FLASHLOAN_TARGET_TOKENS=WETH
ENABLE_ALL_TOKEN_PAIRS=false
MIN_TOKEN_LIQUIDITY_USD=100000
```

### **Example 2: Balanced (Mainnet)**
```bash
FLASHLOAN_TOKENS=USDC,WETH,USDT,DAI,UNI,LINK
FLASHLOAN_PRIMARY_TOKEN=USDC
FLASHLOAN_TARGET_TOKENS=WETH,USDT,DAI,UNI,LINK
ENABLE_ALL_TOKEN_PAIRS=false
MIN_TOKEN_LIQUIDITY_USD=500000
```

### **Example 3: Aggressive (Maximum Opportunities)**
```bash
FLASHLOAN_TOKENS=USDC,WETH,USDT,DAI,WBTC,UNI,LINK,AAVE,CRV,COMP
FLASHLOAN_PRIMARY_TOKEN=USDC
FLASHLOAN_TARGET_TOKENS=WETH,USDT,DAI,WBTC,UNI,LINK,AAVE
ENABLE_ALL_TOKEN_PAIRS=true
MIN_TOKEN_LIQUIDITY_USD=100000
```

### **Example 4: Stablecoin Focus**
```bash
FLASHLOAN_TOKENS=USDC,USDT,DAI
FLASHLOAN_PRIMARY_TOKEN=USDC
FLASHLOAN_TARGET_TOKENS=USDT,DAI
ENABLE_ALL_TOKEN_PAIRS=true
MIN_TOKEN_LIQUIDITY_USD=1000000
```

### **Example 5: DeFi Token Arbitrage**
```bash
FLASHLOAN_TOKENS=USDC,UNI,LINK,AAVE,CRV,COMP
FLASHLOAN_PRIMARY_TOKEN=USDC
FLASHLOAN_TARGET_TOKENS=UNI,LINK,AAVE,CRV,COMP
ENABLE_ALL_TOKEN_PAIRS=false
MIN_TOKEN_LIQUIDITY_USD=200000
```

## 🔍 How It Works

### **Token Selection Process**
1. **Network Detection**: Automatically selects available tokens based on network (Mainnet vs Sepolia)
2. **Configuration Filtering**: Filters tokens based on your `FLASHLOAN_TOKENS` setting
3. **Liquidity Validation**: Checks minimum liquidity requirements
4. **Arbitrage Scanning**: Scans for price differences across configured DEXs

### **Arbitrage Strategy**
1. **Primary Token**: Used for flashloan (e.g., USDC)
2. **Target Token**: Token to arbitrage against (e.g., WETH)
3. **Route**: Primary → Target → Primary (profit in primary token)
4. **Example**: USDC → WETH → USDC (profit in USDC)

## 📊 Token Categories & Priorities

| Category | Tokens | Best For | Risk Level |
|----------|--------|----------|------------|
| **Stablecoins** | USDC, USDT, DAI | Flashloans, stable arbitrage | Low |
| **Major** | WETH, WBTC | High liquidity arbitrage | Low-Medium |
| **DeFi** | UNI, LINK, AAVE, CRV, COMP | Protocol-specific opportunities | Medium |
| **Layer2** | OP, MATIC | Cross-chain arbitrage | Medium |
| **Meme** | SHIB, APE | High volatility arbitrage | High |
| **Exchange** | BNB, FTT | Exchange-specific opportunities | Medium-High |

## 🎯 Strategy Recommendations

### **For Beginners**
- **Primary**: USDC (most stable, highest liquidity)
- **Targets**: WETH, USDT (major tokens only)
- **Pairs**: Conservative, well-established tokens
- **Liquidity**: High threshold ($500k+)

### **For Experienced Users**
- **Primary**: USDC or USDT
- **Targets**: Include DeFi tokens (UNI, LINK, AAVE)
- **Pairs**: Mix of stable and volatile tokens
- **Liquidity**: Medium threshold ($100k+)

### **For Maximum Profit**
- **Primary**: USDC (for stability)
- **Targets**: All available tokens including meme tokens
- **Pairs**: Enable all token pairs
- **Liquidity**: Lower threshold ($50k+)

## 🔧 Network Differences

### **Mainnet (CHAIN_ID=1)**
- **Available**: All 16 tokens
- **Liquidity**: Very high across all major tokens
- **Opportunities**: Maximum arbitrage potential
- **Gas Costs**: Higher, but more profit potential

### **Sepolia Testnet (CHAIN_ID=11155111)**
- **Available**: WETH, USDC only (limited for testing)
- **Liquidity**: Limited testnet liquidity
- **Opportunities**: Basic testing only
- **Gas Costs**: Lower, perfect for testing

## ⚠️ Important Considerations

### **Liquidity Requirements**
- Higher liquidity = Lower slippage = Better profits
- Monitor pool depths before large trades
- Stablecoins generally have highest liquidity

### **Volatility Impact**
- **Stable tokens**: Consistent but smaller arbitrage opportunities
- **Volatile tokens**: Larger opportunities but higher risk
- **Meme tokens**: Highest volatility, highest potential profit/loss

### **Gas Optimization**
- More tokens = More scanning = Higher gas costs
- Focus on high-priority tokens for efficiency
- Use `ENABLE_ALL_TOKEN_PAIRS=false` for targeted strategies

## 🧪 Testing Your Configuration

```bash
# Test your token configuration
node test-dex-config.js

# Check token availability and validation
npm run test:tokens
```

## 🔄 Migration from Previous Version

### **Add New Variables**
```bash
# Add to your .env file
FLASHLOAN_TOKENS=USDC,WETH,USDT,DAI
FLASHLOAN_PRIMARY_TOKEN=USDC
FLASHLOAN_TARGET_TOKENS=WETH,USDT,DAI
ENABLE_ALL_TOKEN_PAIRS=false
MIN_TOKEN_LIQUIDITY_USD=100000
```

### **Backward Compatibility**
- Old configurations continue to work
- New features are opt-in with sensible defaults
- Gradual migration recommended

---

**💡 Pro Tip**: Start with conservative token selection (USDC, WETH, USDT) and gradually add more tokens as you gain experience. Monitor gas costs and adjust your token list based on profitability patterns.
