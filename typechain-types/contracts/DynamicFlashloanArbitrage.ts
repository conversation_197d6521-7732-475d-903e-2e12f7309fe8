/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from "ethers";
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from "../common";

export declare namespace DynamicFlashloanArbitrage {
  export type ArbitrageParamsStruct = {
    tokenA: AddressLike;
    tokenB: AddressLike;
    buyDex: AddressLike;
    sellDex: AddressLike;
    v3Fee: BigNumberish;
    minProfit: BigNumberish;
    provider: BigNumberish;
    extraData: BytesLike;
  };

  export type ArbitrageParamsStructOutput = [
    tokenA: string,
    tokenB: string,
    buyDex: string,
    sellDex: string,
    v3Fee: bigint,
    minProfit: bigint,
    provider: bigint,
    extraData: string
  ] & {
    tokenA: string;
    tokenB: string;
    buyDex: string;
    sellDex: string;
    v3Fee: bigint;
    minProfit: bigint;
    provider: bigint;
    extraData: string;
  };
}

export interface DynamicFlashloanArbitrageInterface extends Interface {
  getFunction(
    nameOrSignature:
      | "ADDRESSES_PROVIDER"
      | "BALANCER_VAULT"
      | "CHAIN_ID"
      | "POOL"
      | "UNISWAP_V2_ROUTER"
      | "UNISWAP_V3_FACTORY"
      | "UNISWAP_V3_ROUTER"
      | "emergencyWithdraw"
      | "executeArbitrage"
      | "executeOperation"
      | "owner"
      | "receiveFlashLoan"
      | "renounceOwnership"
      | "selectOptimalProvider"
      | "transferOwnership"
      | "uniswapV3FlashCallback"
      | "withdrawETH"
  ): FunctionFragment;

  getEvent(
    nameOrSignatureOrTopic:
      | "ArbitrageCompleted"
      | "FlashloanExecuted"
      | "OwnershipTransferred"
      | "ProviderSelected"
  ): EventFragment;

  encodeFunctionData(
    functionFragment: "ADDRESSES_PROVIDER",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "BALANCER_VAULT",
    values?: undefined
  ): string;
  encodeFunctionData(functionFragment: "CHAIN_ID", values?: undefined): string;
  encodeFunctionData(functionFragment: "POOL", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "UNISWAP_V2_ROUTER",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "UNISWAP_V3_FACTORY",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "UNISWAP_V3_ROUTER",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "emergencyWithdraw",
    values: [AddressLike]
  ): string;
  encodeFunctionData(
    functionFragment: "executeArbitrage",
    values: [DynamicFlashloanArbitrage.ArbitrageParamsStruct]
  ): string;
  encodeFunctionData(
    functionFragment: "executeOperation",
    values: [AddressLike, BigNumberish, BigNumberish, AddressLike, BytesLike]
  ): string;
  encodeFunctionData(functionFragment: "owner", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "receiveFlashLoan",
    values: [AddressLike[], BigNumberish[], BigNumberish[], BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "renounceOwnership",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "selectOptimalProvider",
    values: [DynamicFlashloanArbitrage.ArbitrageParamsStruct]
  ): string;
  encodeFunctionData(
    functionFragment: "transferOwnership",
    values: [AddressLike]
  ): string;
  encodeFunctionData(
    functionFragment: "uniswapV3FlashCallback",
    values: [BigNumberish, BigNumberish, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "withdrawETH",
    values?: undefined
  ): string;

  decodeFunctionResult(
    functionFragment: "ADDRESSES_PROVIDER",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "BALANCER_VAULT",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "CHAIN_ID", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "POOL", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "UNISWAP_V2_ROUTER",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "UNISWAP_V3_FACTORY",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "UNISWAP_V3_ROUTER",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "emergencyWithdraw",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "executeArbitrage",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "executeOperation",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "owner", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "receiveFlashLoan",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "renounceOwnership",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "selectOptimalProvider",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "transferOwnership",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "uniswapV3FlashCallback",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "withdrawETH",
    data: BytesLike
  ): Result;
}

export namespace ArbitrageCompletedEvent {
  export type InputTuple = [
    tokenA: AddressLike,
    tokenB: AddressLike,
    profit: BigNumberish
  ];
  export type OutputTuple = [tokenA: string, tokenB: string, profit: bigint];
  export interface OutputObject {
    tokenA: string;
    tokenB: string;
    profit: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace FlashloanExecutedEvent {
  export type InputTuple = [
    provider: BigNumberish,
    asset: AddressLike,
    amount: BigNumberish,
    premium: BigNumberish,
    profit: BigNumberish
  ];
  export type OutputTuple = [
    provider: bigint,
    asset: string,
    amount: bigint,
    premium: bigint,
    profit: bigint
  ];
  export interface OutputObject {
    provider: bigint;
    asset: string;
    amount: bigint;
    premium: bigint;
    profit: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace OwnershipTransferredEvent {
  export type InputTuple = [previousOwner: AddressLike, newOwner: AddressLike];
  export type OutputTuple = [previousOwner: string, newOwner: string];
  export interface OutputObject {
    previousOwner: string;
    newOwner: string;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace ProviderSelectedEvent {
  export type InputTuple = [provider: BigNumberish, reason: string];
  export type OutputTuple = [provider: bigint, reason: string];
  export interface OutputObject {
    provider: bigint;
    reason: string;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export interface DynamicFlashloanArbitrage extends BaseContract {
  connect(runner?: ContractRunner | null): DynamicFlashloanArbitrage;
  waitForDeployment(): Promise<this>;

  interface: DynamicFlashloanArbitrageInterface;

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>;
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent
  ): Promise<this>;

  ADDRESSES_PROVIDER: TypedContractMethod<[], [string], "view">;

  BALANCER_VAULT: TypedContractMethod<[], [string], "view">;

  CHAIN_ID: TypedContractMethod<[], [bigint], "view">;

  POOL: TypedContractMethod<[], [string], "view">;

  UNISWAP_V2_ROUTER: TypedContractMethod<[], [string], "view">;

  UNISWAP_V3_FACTORY: TypedContractMethod<[], [string], "view">;

  UNISWAP_V3_ROUTER: TypedContractMethod<[], [string], "view">;

  emergencyWithdraw: TypedContractMethod<
    [token: AddressLike],
    [void],
    "nonpayable"
  >;

  executeArbitrage: TypedContractMethod<
    [params: DynamicFlashloanArbitrage.ArbitrageParamsStruct],
    [void],
    "nonpayable"
  >;

  executeOperation: TypedContractMethod<
    [
      asset: AddressLike,
      amount: BigNumberish,
      premium: BigNumberish,
      initiator: AddressLike,
      params: BytesLike
    ],
    [boolean],
    "nonpayable"
  >;

  owner: TypedContractMethod<[], [string], "view">;

  receiveFlashLoan: TypedContractMethod<
    [
      tokens: AddressLike[],
      amounts: BigNumberish[],
      feeAmounts: BigNumberish[],
      userData: BytesLike
    ],
    [void],
    "nonpayable"
  >;

  renounceOwnership: TypedContractMethod<[], [void], "nonpayable">;

  selectOptimalProvider: TypedContractMethod<
    [params: DynamicFlashloanArbitrage.ArbitrageParamsStruct],
    [bigint],
    "view"
  >;

  transferOwnership: TypedContractMethod<
    [newOwner: AddressLike],
    [void],
    "nonpayable"
  >;

  uniswapV3FlashCallback: TypedContractMethod<
    [fee0: BigNumberish, fee1: BigNumberish, data: BytesLike],
    [void],
    "nonpayable"
  >;

  withdrawETH: TypedContractMethod<[], [void], "nonpayable">;

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment
  ): T;

  getFunction(
    nameOrSignature: "ADDRESSES_PROVIDER"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "BALANCER_VAULT"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "CHAIN_ID"
  ): TypedContractMethod<[], [bigint], "view">;
  getFunction(
    nameOrSignature: "POOL"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "UNISWAP_V2_ROUTER"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "UNISWAP_V3_FACTORY"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "UNISWAP_V3_ROUTER"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "emergencyWithdraw"
  ): TypedContractMethod<[token: AddressLike], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "executeArbitrage"
  ): TypedContractMethod<
    [params: DynamicFlashloanArbitrage.ArbitrageParamsStruct],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "executeOperation"
  ): TypedContractMethod<
    [
      asset: AddressLike,
      amount: BigNumberish,
      premium: BigNumberish,
      initiator: AddressLike,
      params: BytesLike
    ],
    [boolean],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "owner"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "receiveFlashLoan"
  ): TypedContractMethod<
    [
      tokens: AddressLike[],
      amounts: BigNumberish[],
      feeAmounts: BigNumberish[],
      userData: BytesLike
    ],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "renounceOwnership"
  ): TypedContractMethod<[], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "selectOptimalProvider"
  ): TypedContractMethod<
    [params: DynamicFlashloanArbitrage.ArbitrageParamsStruct],
    [bigint],
    "view"
  >;
  getFunction(
    nameOrSignature: "transferOwnership"
  ): TypedContractMethod<[newOwner: AddressLike], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "uniswapV3FlashCallback"
  ): TypedContractMethod<
    [fee0: BigNumberish, fee1: BigNumberish, data: BytesLike],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "withdrawETH"
  ): TypedContractMethod<[], [void], "nonpayable">;

  getEvent(
    key: "ArbitrageCompleted"
  ): TypedContractEvent<
    ArbitrageCompletedEvent.InputTuple,
    ArbitrageCompletedEvent.OutputTuple,
    ArbitrageCompletedEvent.OutputObject
  >;
  getEvent(
    key: "FlashloanExecuted"
  ): TypedContractEvent<
    FlashloanExecutedEvent.InputTuple,
    FlashloanExecutedEvent.OutputTuple,
    FlashloanExecutedEvent.OutputObject
  >;
  getEvent(
    key: "OwnershipTransferred"
  ): TypedContractEvent<
    OwnershipTransferredEvent.InputTuple,
    OwnershipTransferredEvent.OutputTuple,
    OwnershipTransferredEvent.OutputObject
  >;
  getEvent(
    key: "ProviderSelected"
  ): TypedContractEvent<
    ProviderSelectedEvent.InputTuple,
    ProviderSelectedEvent.OutputTuple,
    ProviderSelectedEvent.OutputObject
  >;

  filters: {
    "ArbitrageCompleted(address,address,uint256)": TypedContractEvent<
      ArbitrageCompletedEvent.InputTuple,
      ArbitrageCompletedEvent.OutputTuple,
      ArbitrageCompletedEvent.OutputObject
    >;
    ArbitrageCompleted: TypedContractEvent<
      ArbitrageCompletedEvent.InputTuple,
      ArbitrageCompletedEvent.OutputTuple,
      ArbitrageCompletedEvent.OutputObject
    >;

    "FlashloanExecuted(uint8,address,uint256,uint256,uint256)": TypedContractEvent<
      FlashloanExecutedEvent.InputTuple,
      FlashloanExecutedEvent.OutputTuple,
      FlashloanExecutedEvent.OutputObject
    >;
    FlashloanExecuted: TypedContractEvent<
      FlashloanExecutedEvent.InputTuple,
      FlashloanExecutedEvent.OutputTuple,
      FlashloanExecutedEvent.OutputObject
    >;

    "OwnershipTransferred(address,address)": TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >;
    OwnershipTransferred: TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >;

    "ProviderSelected(uint8,string)": TypedContractEvent<
      ProviderSelectedEvent.InputTuple,
      ProviderSelectedEvent.OutputTuple,
      ProviderSelectedEvent.OutputObject
    >;
    ProviderSelected: TypedContractEvent<
      ProviderSelectedEvent.InputTuple,
      ProviderSelectedEvent.OutputTuple,
      ProviderSelectedEvent.OutputObject
    >;
  };
}
