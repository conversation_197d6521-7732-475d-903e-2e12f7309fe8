/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from "ethers";
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from "../../common";

export interface BalancerFlashloanArbitrageInterface extends Interface {
  getFunction(
    nameOrSignature:
      | "CHAIN_ID"
      | "UNISWAP_V2_ROUTER"
      | "UNISWAP_V3_ROUTER"
      | "emergencyWithdraw"
      | "executeFlashloanArbitrage"
      | "getVault"
      | "owner"
      | "receiveFlashLoan"
      | "renounceOwnership"
      | "transferOwnership"
      | "withdrawProfits"
  ): FunctionFragment;

  getEvent(
    nameOrSignatureOrTopic:
      | "ArbitrageCompleted"
      | "FlashloanExecuted"
      | "OwnershipTransferred"
  ): EventFragment;

  encodeFunctionData(functionFragment: "CHAIN_ID", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "UNISWAP_V2_ROUTER",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "UNISWAP_V3_ROUTER",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "emergencyWithdraw",
    values: [AddressLike, BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: "executeFlashloanArbitrage",
    values: [AddressLike[], BigNumberish[], BytesLike]
  ): string;
  encodeFunctionData(functionFragment: "getVault", values?: undefined): string;
  encodeFunctionData(functionFragment: "owner", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "receiveFlashLoan",
    values: [AddressLike[], BigNumberish[], BigNumberish[], BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "renounceOwnership",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "transferOwnership",
    values: [AddressLike]
  ): string;
  encodeFunctionData(
    functionFragment: "withdrawProfits",
    values: [AddressLike]
  ): string;

  decodeFunctionResult(functionFragment: "CHAIN_ID", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "UNISWAP_V2_ROUTER",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "UNISWAP_V3_ROUTER",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "emergencyWithdraw",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "executeFlashloanArbitrage",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "getVault", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "owner", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "receiveFlashLoan",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "renounceOwnership",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "transferOwnership",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "withdrawProfits",
    data: BytesLike
  ): Result;
}

export namespace ArbitrageCompletedEvent {
  export type InputTuple = [
    tokenA: AddressLike,
    tokenB: AddressLike,
    profit: BigNumberish
  ];
  export type OutputTuple = [tokenA: string, tokenB: string, profit: bigint];
  export interface OutputObject {
    tokenA: string;
    tokenB: string;
    profit: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace FlashloanExecutedEvent {
  export type InputTuple = [
    asset: AddressLike,
    amount: BigNumberish,
    profit: BigNumberish
  ];
  export type OutputTuple = [asset: string, amount: bigint, profit: bigint];
  export interface OutputObject {
    asset: string;
    amount: bigint;
    profit: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace OwnershipTransferredEvent {
  export type InputTuple = [previousOwner: AddressLike, newOwner: AddressLike];
  export type OutputTuple = [previousOwner: string, newOwner: string];
  export interface OutputObject {
    previousOwner: string;
    newOwner: string;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export interface BalancerFlashloanArbitrage extends BaseContract {
  connect(runner?: ContractRunner | null): BalancerFlashloanArbitrage;
  waitForDeployment(): Promise<this>;

  interface: BalancerFlashloanArbitrageInterface;

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>;
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent
  ): Promise<this>;

  CHAIN_ID: TypedContractMethod<[], [bigint], "view">;

  UNISWAP_V2_ROUTER: TypedContractMethod<[], [string], "view">;

  UNISWAP_V3_ROUTER: TypedContractMethod<[], [string], "view">;

  emergencyWithdraw: TypedContractMethod<
    [token: AddressLike, amount: BigNumberish],
    [void],
    "nonpayable"
  >;

  executeFlashloanArbitrage: TypedContractMethod<
    [tokens: AddressLike[], amounts: BigNumberish[], userData: BytesLike],
    [void],
    "nonpayable"
  >;

  getVault: TypedContractMethod<[], [string], "view">;

  owner: TypedContractMethod<[], [string], "view">;

  receiveFlashLoan: TypedContractMethod<
    [
      tokens: AddressLike[],
      amounts: BigNumberish[],
      feeAmounts: BigNumberish[],
      userData: BytesLike
    ],
    [void],
    "nonpayable"
  >;

  renounceOwnership: TypedContractMethod<[], [void], "nonpayable">;

  transferOwnership: TypedContractMethod<
    [newOwner: AddressLike],
    [void],
    "nonpayable"
  >;

  withdrawProfits: TypedContractMethod<
    [token: AddressLike],
    [void],
    "nonpayable"
  >;

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment
  ): T;

  getFunction(
    nameOrSignature: "CHAIN_ID"
  ): TypedContractMethod<[], [bigint], "view">;
  getFunction(
    nameOrSignature: "UNISWAP_V2_ROUTER"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "UNISWAP_V3_ROUTER"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "emergencyWithdraw"
  ): TypedContractMethod<
    [token: AddressLike, amount: BigNumberish],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "executeFlashloanArbitrage"
  ): TypedContractMethod<
    [tokens: AddressLike[], amounts: BigNumberish[], userData: BytesLike],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "getVault"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "owner"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "receiveFlashLoan"
  ): TypedContractMethod<
    [
      tokens: AddressLike[],
      amounts: BigNumberish[],
      feeAmounts: BigNumberish[],
      userData: BytesLike
    ],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "renounceOwnership"
  ): TypedContractMethod<[], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "transferOwnership"
  ): TypedContractMethod<[newOwner: AddressLike], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "withdrawProfits"
  ): TypedContractMethod<[token: AddressLike], [void], "nonpayable">;

  getEvent(
    key: "ArbitrageCompleted"
  ): TypedContractEvent<
    ArbitrageCompletedEvent.InputTuple,
    ArbitrageCompletedEvent.OutputTuple,
    ArbitrageCompletedEvent.OutputObject
  >;
  getEvent(
    key: "FlashloanExecuted"
  ): TypedContractEvent<
    FlashloanExecutedEvent.InputTuple,
    FlashloanExecutedEvent.OutputTuple,
    FlashloanExecutedEvent.OutputObject
  >;
  getEvent(
    key: "OwnershipTransferred"
  ): TypedContractEvent<
    OwnershipTransferredEvent.InputTuple,
    OwnershipTransferredEvent.OutputTuple,
    OwnershipTransferredEvent.OutputObject
  >;

  filters: {
    "ArbitrageCompleted(address,address,uint256)": TypedContractEvent<
      ArbitrageCompletedEvent.InputTuple,
      ArbitrageCompletedEvent.OutputTuple,
      ArbitrageCompletedEvent.OutputObject
    >;
    ArbitrageCompleted: TypedContractEvent<
      ArbitrageCompletedEvent.InputTuple,
      ArbitrageCompletedEvent.OutputTuple,
      ArbitrageCompletedEvent.OutputObject
    >;

    "FlashloanExecuted(address,uint256,uint256)": TypedContractEvent<
      FlashloanExecutedEvent.InputTuple,
      FlashloanExecutedEvent.OutputTuple,
      FlashloanExecutedEvent.OutputObject
    >;
    FlashloanExecuted: TypedContractEvent<
      FlashloanExecutedEvent.InputTuple,
      FlashloanExecutedEvent.OutputTuple,
      FlashloanExecutedEvent.OutputObject
    >;

    "OwnershipTransferred(address,address)": TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >;
    OwnershipTransferred: TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >;
  };
}
