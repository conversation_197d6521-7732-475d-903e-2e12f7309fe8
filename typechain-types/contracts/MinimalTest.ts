/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigN<PERSON>berish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from "ethers";
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from "../common";

export interface MinimalTestInterface extends Interface {
  getFunction(
    nameOrSignature:
      | "deploymentTime"
      | "getDeploymentInfo"
      | "getMessage"
      | "message"
      | "owner"
      | "renounceOwnership"
      | "setMessage"
      | "transferOwnership"
  ): FunctionFragment;

  getEvent(
    nameOrSignatureOrTopic:
      | "ContractDeployed"
      | "MessageSet"
      | "OwnershipTransferred"
  ): EventFragment;

  encodeFunctionData(
    functionFragment: "deploymentTime",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "getDeploymentInfo",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "getMessage",
    values?: undefined
  ): string;
  encodeFunctionData(functionFragment: "message", values?: undefined): string;
  encodeFunctionData(functionFragment: "owner", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "renounceOwnership",
    values?: undefined
  ): string;
  encodeFunctionData(functionFragment: "setMessage", values: [string]): string;
  encodeFunctionData(
    functionFragment: "transferOwnership",
    values: [AddressLike]
  ): string;

  decodeFunctionResult(
    functionFragment: "deploymentTime",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "getDeploymentInfo",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "getMessage", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "message", data: BytesLike): Result;
  decodeFunctionResult(functionFragment: "owner", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "renounceOwnership",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "setMessage", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "transferOwnership",
    data: BytesLike
  ): Result;
}

export namespace ContractDeployedEvent {
  export type InputTuple = [deployer: AddressLike, timestamp: BigNumberish];
  export type OutputTuple = [deployer: string, timestamp: bigint];
  export interface OutputObject {
    deployer: string;
    timestamp: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace MessageSetEvent {
  export type InputTuple = [newMessage: string];
  export type OutputTuple = [newMessage: string];
  export interface OutputObject {
    newMessage: string;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace OwnershipTransferredEvent {
  export type InputTuple = [previousOwner: AddressLike, newOwner: AddressLike];
  export type OutputTuple = [previousOwner: string, newOwner: string];
  export interface OutputObject {
    previousOwner: string;
    newOwner: string;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export interface MinimalTest extends BaseContract {
  connect(runner?: ContractRunner | null): MinimalTest;
  waitForDeployment(): Promise<this>;

  interface: MinimalTestInterface;

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>;
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent
  ): Promise<this>;

  deploymentTime: TypedContractMethod<[], [bigint], "view">;

  getDeploymentInfo: TypedContractMethod<
    [],
    [
      [string, bigint, string] & {
        contractOwner: string;
        deployTime: bigint;
        currentMessage: string;
      }
    ],
    "view"
  >;

  getMessage: TypedContractMethod<[], [string], "view">;

  message: TypedContractMethod<[], [string], "view">;

  owner: TypedContractMethod<[], [string], "view">;

  renounceOwnership: TypedContractMethod<[], [void], "nonpayable">;

  setMessage: TypedContractMethod<[newMessage: string], [void], "nonpayable">;

  transferOwnership: TypedContractMethod<
    [newOwner: AddressLike],
    [void],
    "nonpayable"
  >;

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment
  ): T;

  getFunction(
    nameOrSignature: "deploymentTime"
  ): TypedContractMethod<[], [bigint], "view">;
  getFunction(
    nameOrSignature: "getDeploymentInfo"
  ): TypedContractMethod<
    [],
    [
      [string, bigint, string] & {
        contractOwner: string;
        deployTime: bigint;
        currentMessage: string;
      }
    ],
    "view"
  >;
  getFunction(
    nameOrSignature: "getMessage"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "message"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "owner"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "renounceOwnership"
  ): TypedContractMethod<[], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "setMessage"
  ): TypedContractMethod<[newMessage: string], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "transferOwnership"
  ): TypedContractMethod<[newOwner: AddressLike], [void], "nonpayable">;

  getEvent(
    key: "ContractDeployed"
  ): TypedContractEvent<
    ContractDeployedEvent.InputTuple,
    ContractDeployedEvent.OutputTuple,
    ContractDeployedEvent.OutputObject
  >;
  getEvent(
    key: "MessageSet"
  ): TypedContractEvent<
    MessageSetEvent.InputTuple,
    MessageSetEvent.OutputTuple,
    MessageSetEvent.OutputObject
  >;
  getEvent(
    key: "OwnershipTransferred"
  ): TypedContractEvent<
    OwnershipTransferredEvent.InputTuple,
    OwnershipTransferredEvent.OutputTuple,
    OwnershipTransferredEvent.OutputObject
  >;

  filters: {
    "ContractDeployed(address,uint256)": TypedContractEvent<
      ContractDeployedEvent.InputTuple,
      ContractDeployedEvent.OutputTuple,
      ContractDeployedEvent.OutputObject
    >;
    ContractDeployed: TypedContractEvent<
      ContractDeployedEvent.InputTuple,
      ContractDeployedEvent.OutputTuple,
      ContractDeployedEvent.OutputObject
    >;

    "MessageSet(string)": TypedContractEvent<
      MessageSetEvent.InputTuple,
      MessageSetEvent.OutputTuple,
      MessageSetEvent.OutputObject
    >;
    MessageSet: TypedContractEvent<
      MessageSetEvent.InputTuple,
      MessageSetEvent.OutputTuple,
      MessageSetEvent.OutputObject
    >;

    "OwnershipTransferred(address,address)": TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >;
    OwnershipTransferred: TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >;
  };
}
