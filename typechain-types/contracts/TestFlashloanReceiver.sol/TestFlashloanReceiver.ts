/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumberish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from "ethers";
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from "../../common";

export interface TestFlashloanReceiverInterface extends Interface {
  getFunction(
    nameOrSignature:
      | "emergencyWithdraw"
      | "executeTestFlashloan"
      | "flashloanBalances"
      | "flashloanCount"
      | "getTokenBalance"
      | "lastFlashloanSuccess"
      | "owner"
      | "receiveFlashLoan"
      | "renounceOwnership"
      | "transferOwnership"
      | "withdrawETH"
  ): FunctionFragment;

  getEvent(
    nameOrSignatureOrTopic:
      | "FlashloanReceived"
      | "FlashloanRepaid"
      | "OwnershipTransferred"
  ): EventFragment;

  encodeFunctionData(
    functionFragment: "emergencyWithdraw",
    values: [AddressLike]
  ): string;
  encodeFunctionData(
    functionFragment: "executeTestFlashloan",
    values: [AddressLike, AddressLike, BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: "flashloanBalances",
    values: [AddressLike]
  ): string;
  encodeFunctionData(
    functionFragment: "flashloanCount",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "getTokenBalance",
    values: [AddressLike]
  ): string;
  encodeFunctionData(
    functionFragment: "lastFlashloanSuccess",
    values?: undefined
  ): string;
  encodeFunctionData(functionFragment: "owner", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "receiveFlashLoan",
    values: [AddressLike[], BigNumberish[], BigNumberish[], BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: "renounceOwnership",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "transferOwnership",
    values: [AddressLike]
  ): string;
  encodeFunctionData(
    functionFragment: "withdrawETH",
    values?: undefined
  ): string;

  decodeFunctionResult(
    functionFragment: "emergencyWithdraw",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "executeTestFlashloan",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "flashloanBalances",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "flashloanCount",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "getTokenBalance",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "lastFlashloanSuccess",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "owner", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "receiveFlashLoan",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "renounceOwnership",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "transferOwnership",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "withdrawETH",
    data: BytesLike
  ): Result;
}

export namespace FlashloanReceivedEvent {
  export type InputTuple = [
    token: AddressLike,
    amount: BigNumberish,
    fee: BigNumberish
  ];
  export type OutputTuple = [token: string, amount: bigint, fee: bigint];
  export interface OutputObject {
    token: string;
    amount: bigint;
    fee: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace FlashloanRepaidEvent {
  export type InputTuple = [
    token: AddressLike,
    amount: BigNumberish,
    fee: BigNumberish
  ];
  export type OutputTuple = [token: string, amount: bigint, fee: bigint];
  export interface OutputObject {
    token: string;
    amount: bigint;
    fee: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace OwnershipTransferredEvent {
  export type InputTuple = [previousOwner: AddressLike, newOwner: AddressLike];
  export type OutputTuple = [previousOwner: string, newOwner: string];
  export interface OutputObject {
    previousOwner: string;
    newOwner: string;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export interface TestFlashloanReceiver extends BaseContract {
  connect(runner?: ContractRunner | null): TestFlashloanReceiver;
  waitForDeployment(): Promise<this>;

  interface: TestFlashloanReceiverInterface;

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>;
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent
  ): Promise<this>;

  emergencyWithdraw: TypedContractMethod<
    [token: AddressLike],
    [void],
    "nonpayable"
  >;

  executeTestFlashloan: TypedContractMethod<
    [vault: AddressLike, token: AddressLike, amount: BigNumberish],
    [void],
    "nonpayable"
  >;

  flashloanBalances: TypedContractMethod<[arg0: AddressLike], [bigint], "view">;

  flashloanCount: TypedContractMethod<[], [bigint], "view">;

  getTokenBalance: TypedContractMethod<[token: AddressLike], [bigint], "view">;

  lastFlashloanSuccess: TypedContractMethod<[], [boolean], "view">;

  owner: TypedContractMethod<[], [string], "view">;

  receiveFlashLoan: TypedContractMethod<
    [
      tokens: AddressLike[],
      amounts: BigNumberish[],
      feeAmounts: BigNumberish[],
      userData: BytesLike
    ],
    [void],
    "nonpayable"
  >;

  renounceOwnership: TypedContractMethod<[], [void], "nonpayable">;

  transferOwnership: TypedContractMethod<
    [newOwner: AddressLike],
    [void],
    "nonpayable"
  >;

  withdrawETH: TypedContractMethod<[], [void], "nonpayable">;

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment
  ): T;

  getFunction(
    nameOrSignature: "emergencyWithdraw"
  ): TypedContractMethod<[token: AddressLike], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "executeTestFlashloan"
  ): TypedContractMethod<
    [vault: AddressLike, token: AddressLike, amount: BigNumberish],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "flashloanBalances"
  ): TypedContractMethod<[arg0: AddressLike], [bigint], "view">;
  getFunction(
    nameOrSignature: "flashloanCount"
  ): TypedContractMethod<[], [bigint], "view">;
  getFunction(
    nameOrSignature: "getTokenBalance"
  ): TypedContractMethod<[token: AddressLike], [bigint], "view">;
  getFunction(
    nameOrSignature: "lastFlashloanSuccess"
  ): TypedContractMethod<[], [boolean], "view">;
  getFunction(
    nameOrSignature: "owner"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "receiveFlashLoan"
  ): TypedContractMethod<
    [
      tokens: AddressLike[],
      amounts: BigNumberish[],
      feeAmounts: BigNumberish[],
      userData: BytesLike
    ],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "renounceOwnership"
  ): TypedContractMethod<[], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "transferOwnership"
  ): TypedContractMethod<[newOwner: AddressLike], [void], "nonpayable">;
  getFunction(
    nameOrSignature: "withdrawETH"
  ): TypedContractMethod<[], [void], "nonpayable">;

  getEvent(
    key: "FlashloanReceived"
  ): TypedContractEvent<
    FlashloanReceivedEvent.InputTuple,
    FlashloanReceivedEvent.OutputTuple,
    FlashloanReceivedEvent.OutputObject
  >;
  getEvent(
    key: "FlashloanRepaid"
  ): TypedContractEvent<
    FlashloanRepaidEvent.InputTuple,
    FlashloanRepaidEvent.OutputTuple,
    FlashloanRepaidEvent.OutputObject
  >;
  getEvent(
    key: "OwnershipTransferred"
  ): TypedContractEvent<
    OwnershipTransferredEvent.InputTuple,
    OwnershipTransferredEvent.OutputTuple,
    OwnershipTransferredEvent.OutputObject
  >;

  filters: {
    "FlashloanReceived(address,uint256,uint256)": TypedContractEvent<
      FlashloanReceivedEvent.InputTuple,
      FlashloanReceivedEvent.OutputTuple,
      FlashloanReceivedEvent.OutputObject
    >;
    FlashloanReceived: TypedContractEvent<
      FlashloanReceivedEvent.InputTuple,
      FlashloanReceivedEvent.OutputTuple,
      FlashloanReceivedEvent.OutputObject
    >;

    "FlashloanRepaid(address,uint256,uint256)": TypedContractEvent<
      FlashloanRepaidEvent.InputTuple,
      FlashloanRepaidEvent.OutputTuple,
      FlashloanRepaidEvent.OutputObject
    >;
    FlashloanRepaid: TypedContractEvent<
      FlashloanRepaidEvent.InputTuple,
      FlashloanRepaidEvent.OutputTuple,
      FlashloanRepaidEvent.OutputObject
    >;

    "OwnershipTransferred(address,address)": TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >;
    OwnershipTransferred: TypedContractEvent<
      OwnershipTransferredEvent.InputTuple,
      OwnershipTransferredEvent.OutputTuple,
      OwnershipTransferredEvent.OutputObject
    >;
  };
}
