/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from "ethers";
import type {
  IAuthentication,
  IAuthenticationInterface,
} from "../../../../../../@balancer-labs/v2-interfaces/contracts/solidity-utils/helpers/IAuthentication";

const _abi = [
  {
    inputs: [
      {
        internalType: "bytes4",
        name: "selector",
        type: "bytes4",
      },
    ],
    name: "getActionId",
    outputs: [
      {
        internalType: "bytes32",
        name: "",
        type: "bytes32",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
] as const;

export class IAuthentication__factory {
  static readonly abi = _abi;
  static createInterface(): IAuthenticationInterface {
    return new Interface(_abi) as IAuthenticationInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): IAuthentication {
    return new Contract(address, _abi, runner) as unknown as <PERSON><PERSON>uthentica<PERSON>;
  }
}
