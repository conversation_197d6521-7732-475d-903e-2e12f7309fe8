/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from "ethers";
import type {
  ITemporarilyPausable,
  ITemporarilyPausableInterface,
} from "../../../../../../@balancer-labs/v2-interfaces/contracts/solidity-utils/helpers/ITemporarilyPausable";

const _abi = [
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: "bool",
        name: "paused",
        type: "bool",
      },
    ],
    name: "PausedStateChanged",
    type: "event",
  },
  {
    inputs: [],
    name: "getPausedState",
    outputs: [
      {
        internalType: "bool",
        name: "paused",
        type: "bool",
      },
      {
        internalType: "uint256",
        name: "pauseWindowEndTime",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "bufferPeriodEndTime",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
] as const;

export class ITemporarilyPausable__factory {
  static readonly abi = _abi;
  static createInterface(): ITemporarilyPausableInterface {
    return new Interface(_abi) as ITemporarilyPausableInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): ITemporarilyPausable {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as ITemporarilyPausable;
  }
}
