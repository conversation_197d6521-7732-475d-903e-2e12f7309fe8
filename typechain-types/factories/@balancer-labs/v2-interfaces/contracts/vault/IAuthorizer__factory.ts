/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from "ethers";
import type {
  IAuthorizer,
  IAuthorizerInterface,
} from "../../../../../@balancer-labs/v2-interfaces/contracts/vault/IAuthorizer";

const _abi = [
  {
    inputs: [
      {
        internalType: "bytes32",
        name: "actionId",
        type: "bytes32",
      },
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
      {
        internalType: "address",
        name: "where",
        type: "address",
      },
    ],
    name: "canPerform",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
] as const;

export class IAuthorizer__factory {
  static readonly abi = _abi;
  static createInterface(): IAuthorizerInterface {
    return new Interface(_abi) as IAuthorizerInterface;
  }
  static connect(address: string, runner?: ContractRunner | null): IAuthorizer {
    return new Contract(address, _abi, runner) as unknown as IAuthorizer;
  }
}
