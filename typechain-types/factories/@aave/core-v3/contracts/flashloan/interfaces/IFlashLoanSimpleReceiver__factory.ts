/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from "ethers";
import type {
  IFlashLoanSimpleReceiver,
  IFlashLoanSimpleReceiverInterface,
} from "../../../../../../@aave/core-v3/contracts/flashloan/interfaces/IFlashLoanSimpleReceiver";

const _abi = [
  {
    inputs: [],
    name: "ADDRESSES_PROVIDER",
    outputs: [
      {
        internalType: "contract IPoolAddressesProvider",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "POOL",
    outputs: [
      {
        internalType: "contract IPool",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "initiator",
        type: "address",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeOperation",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

export class IFlashLoanSimpleReceiver__factory {
  static readonly abi = _abi;
  static createInterface(): IFlashLoanSimpleReceiverInterface {
    return new Interface(_abi) as IFlashLoanSimpleReceiverInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): IFlashLoanSimpleReceiver {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as IFlashLoanSimpleReceiver;
  }
}
