/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from "ethers";
import type {
  FlashLoanSimpleReceiverBase,
  FlashLoanSimpleReceiverBaseInterface,
} from "../../../../../../@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase";

const _abi = [
  {
    inputs: [],
    name: "ADDRESSES_PROVIDER",
    outputs: [
      {
        internalType: "contract IPoolAddressesProvider",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "POOL",
    outputs: [
      {
        internalType: "contract IPool",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "initiator",
        type: "address",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeOperation",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

export class FlashLoanSimpleReceiverBase__factory {
  static readonly abi = _abi;
  static createInterface(): FlashLoanSimpleReceiverBaseInterface {
    return new Interface(_abi) as FlashLoanSimpleReceiverBaseInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): FlashLoanSimpleReceiverBase {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as FlashLoanSimpleReceiverBase;
  }
}
