/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from "ethers";
import type {
  IUniswapV3FlashCallback,
  IUniswapV3FlashCallbackInterface,
} from "../../../../../../@uniswap/v3-core/contracts/interfaces/callback/IUniswapV3FlashCallback";

const _abi = [
  {
    inputs: [
      {
        internalType: "uint256",
        name: "fee0",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "fee1",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "data",
        type: "bytes",
      },
    ],
    name: "uniswapV3FlashCallback",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

export class IUniswapV3FlashCallback__factory {
  static readonly abi = _abi;
  static createInterface(): IUniswapV3FlashCallbackInterface {
    return new Interface(_abi) as IUniswapV3FlashCallbackInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): IUniswapV3FlashCallback {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as IUniswapV3FlashCallback;
  }
}
