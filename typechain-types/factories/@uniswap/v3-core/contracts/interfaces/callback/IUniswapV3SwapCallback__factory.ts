/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */

import { Contract, Interface, type ContractRunner } from "ethers";
import type {
  IUniswapV3SwapCallback,
  IUniswapV3SwapCallbackInterface,
} from "../../../../../../@uniswap/v3-core/contracts/interfaces/callback/IUniswapV3SwapCallback";

const _abi = [
  {
    inputs: [
      {
        internalType: "int256",
        name: "amount0Delta",
        type: "int256",
      },
      {
        internalType: "int256",
        name: "amount1D<PERSON><PERSON>",
        type: "int256",
      },
      {
        internalType: "bytes",
        name: "data",
        type: "bytes",
      },
    ],
    name: "uniswapV3SwapCallback",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

export class IUniswapV3SwapCallback__factory {
  static readonly abi = _abi;
  static createInterface(): IUniswapV3SwapCallbackInterface {
    return new Interface(_abi) as IUniswapV3SwapCallbackInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): IUniswapV3SwapCallback {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as IUniswapV3SwapCallback;
  }
}
