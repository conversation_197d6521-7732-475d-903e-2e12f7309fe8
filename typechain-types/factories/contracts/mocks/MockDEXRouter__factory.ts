/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type { Signer, ContractDeployTransaction, ContractRunner } from "ethers";
import type { NonPayableOverrides } from "../../../common";
import type {
  MockDEXRouter,
  MockDEXRouterInterface,
} from "../../../contracts/mocks/MockDEXRouter";

const _abi = [
  {
    inputs: [
      {
        components: [
          {
            internalType: "address",
            name: "tokenIn",
            type: "address",
          },
          {
            internalType: "address",
            name: "tokenOut",
            type: "address",
          },
          {
            internalType: "uint24",
            name: "fee",
            type: "uint24",
          },
          {
            internalType: "address",
            name: "recipient",
            type: "address",
          },
          {
            internalType: "uint256",
            name: "deadline",
            type: "uint256",
          },
          {
            internalType: "uint256",
            name: "amountIn",
            type: "uint256",
          },
          {
            internalType: "uint160",
            name: "sqrtPriceLimitX96",
            type: "uint160",
          },
          {
            internalType: "uint256",
            name: "amountOutMinimum",
            type: "uint256",
          },
        ],
        internalType: "struct MockDEXRouter.ExactInputSingleParams",
        name: "params",
        type: "tuple",
      },
    ],
    name: "exactInputSingle",
    outputs: [
      {
        internalType: "uint256",
        name: "amountOut",
        type: "uint256",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "amountIn",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "amountOutMin",
        type: "uint256",
      },
      {
        internalType: "address[]",
        name: "path",
        type: "address[]",
      },
      {
        internalType: "address",
        name: "to",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "deadline",
        type: "uint256",
      },
    ],
    name: "swapExactTokensForTokens",
    outputs: [
      {
        internalType: "uint256[]",
        name: "amounts",
        type: "uint256[]",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        internalType: "address",
        name: "recipient",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "testTransfer",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type MockDEXRouterConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: MockDEXRouterConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class MockDEXRouter__factory extends ContractFactory {
  constructor(...args: MockDEXRouterConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(overrides || {});
  }
  override deploy(overrides?: NonPayableOverrides & { from?: string }) {
    return super.deploy(overrides || {}) as Promise<
      MockDEXRouter & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(runner: ContractRunner | null): MockDEXRouter__factory {
    return super.connect(runner) as MockDEXRouter__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): MockDEXRouterInterface {
    return new Interface(_abi) as MockDEXRouterInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): MockDEXRouter {
    return new Contract(address, _abi, runner) as unknown as MockDEXRouter;
  }
}
