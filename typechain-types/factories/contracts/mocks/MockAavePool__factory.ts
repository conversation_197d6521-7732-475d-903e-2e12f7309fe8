/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type { Signer, ContractDeployTransaction, ContractRunner } from "ethers";
import type { NonPayableOverrides } from "../../../common";
import type {
  MockAavePool,
  MockAavePoolInterface,
} from "../../../contracts/mocks/MockAavePool";

const _abi = [
  {
    inputs: [
      {
        internalType: "address",
        name: "receiverAddress",
        type: "address",
      },
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
      {
        internalType: "uint16",
        name: "referralCode",
        type: "uint16",
      },
    ],
    name: "flashLoanSimple",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "0x608060405234801561001057600080fd5b5061039a806100206000396000f3fe608060405234801561001057600080fd5b506004361061002b5760003560e01c806342b0b77c14610030575b600080fd5b61004361003e366004610227565b610045565b005b60405163a9059cbb60e01b81526001600160a01b0387811660048301526024820186905286169063a9059cbb906044016020604051808303816000875af1158015610094573d6000803e3d6000fd5b505050506040513d601f19601f820116820180604052508101906100b891906102df565b50604051631b11d0ff60e01b81526000906001600160a01b03881690631b11d0ff906100f2908990899086908d908b908b90600401610308565b6020604051808303816000875af1158015610111573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061013591906102df565b9050806101885760405162461bcd60e51b815260206004820152601760248201527f657865637574654f7065726174696f6e206661696c6564000000000000000000604482015260640160405180910390fd5b6040516323b872dd60e01b81526001600160a01b038881166004830152306024830152604482018790528716906323b872dd906064016020604051808303816000875af11580156101dd573d6000803e3d6000fd5b505050506040513d601f19601f8201168201806040525081019061020191906102df565b5050505050505050565b80356001600160a01b038116811461022257600080fd5b919050565b60008060008060008060a0878903121561024057600080fd5b6102498761020b565b95506102576020880161020b565b945060408701359350606087013567ffffffffffffffff8082111561027b57600080fd5b818901915089601f83011261028f57600080fd5b81358181111561029e57600080fd5b8a60208285010111156102b057600080fd5b602083019550809450505050608087013561ffff811681146102d157600080fd5b809150509295509295509295565b6000602082840312156102f157600080fd5b8151801515811461030157600080fd5b9392505050565b6001600160a01b03878116825260208201879052604082018690528416606082015260a06080820181905281018290526000828460c0840137600060c0848401015260c0601f19601f850116830101905097965050505050505056fea264697066735822122072c8dfcff8ef7ff02b1b538b55b920b9f30deab1461af3dda09265086248bb9d64736f6c63430008140033";

type MockAavePoolConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: MockAavePoolConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class MockAavePool__factory extends ContractFactory {
  constructor(...args: MockAavePoolConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(overrides || {});
  }
  override deploy(overrides?: NonPayableOverrides & { from?: string }) {
    return super.deploy(overrides || {}) as Promise<
      MockAavePool & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(runner: ContractRunner | null): MockAavePool__factory {
    return super.connect(runner) as MockAavePool__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): MockAavePoolInterface {
    return new Interface(_abi) as MockAavePoolInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): MockAavePool {
    return new Contract(address, _abi, runner) as unknown as MockAavePool;
  }
}
