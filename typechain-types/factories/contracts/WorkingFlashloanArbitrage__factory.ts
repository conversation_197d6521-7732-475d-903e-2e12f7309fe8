/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type {
  Signer,
  AddressLike,
  ContractDeployTransaction,
  ContractRunner,
} from "ethers";
import type { NonPayableOverrides } from "../../common";
import type {
  WorkingFlashloanArbitrage,
  WorkingFlashloanArbitrageInterface,
} from "../../contracts/WorkingFlashloanArbitrage";

const _abi = [
  {
    inputs: [
      {
        internalType: "contract IPoolAddressesProvider",
        name: "_addressProvider",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
    ],
    name: "ArbitrageExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
    ],
    name: "FlashloanExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    inputs: [],
    name: "ADDRESSES_PROVIDER",
    outputs: [
      {
        internalType: "contract IPoolAddressesProvider",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "CHAIN_ID",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "POOL",
    outputs: [
      {
        internalType: "contract IPool",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "emergencyWithdraw",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        internalType: "address",
        name: "buyRouter",
        type: "address",
      },
      {
        internalType: "address",
        name: "sellRouter",
        type: "address",
      },
      {
        internalType: "bytes",
        name: "buySwapData",
        type: "bytes",
      },
      {
        internalType: "bytes",
        name: "sellSwapData",
        type: "bytes",
      },
    ],
    name: "executeFlashloanArbitrage",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "initiator",
        type: "address",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeOperation",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "getBalance",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "withdrawProfits",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type WorkingFlashloanArbitrageConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: WorkingFlashloanArbitrageConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class WorkingFlashloanArbitrage__factory extends ContractFactory {
  constructor(...args: WorkingFlashloanArbitrageConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    _addressProvider: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(_addressProvider, overrides || {});
  }
  override deploy(
    _addressProvider: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ) {
    return super.deploy(_addressProvider, overrides || {}) as Promise<
      WorkingFlashloanArbitrage & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(
    runner: ContractRunner | null
  ): WorkingFlashloanArbitrage__factory {
    return super.connect(runner) as WorkingFlashloanArbitrage__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): WorkingFlashloanArbitrageInterface {
    return new Interface(_abi) as WorkingFlashloanArbitrageInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): WorkingFlashloanArbitrage {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as WorkingFlashloanArbitrage;
  }
}
