/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type {
  Signer,
  AddressLike,
  ContractDeployTransaction,
  ContractRunner,
} from "ethers";
import type { NonPayableOverrides } from "../../../common";
import type {
  HybridFlashloanArbitrage,
  HybridFlashloanArbitrageInterface,
} from "../../../contracts/HybridFlashloanArbitrage.sol/HybridFlashloanArbitrage";

const _abi = [
  {
    inputs: [
      {
        internalType: "contract IPoolAddressesProvider",
        name: "_aaveAddressProvider",
        type: "address",
      },
      {
        internalType: "contract IVault",
        name: "_balancerVault",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
    ],
    name: "ArbitrageCompleted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "enum HybridFlashloanArbitrage.FlashloanProvider",
        name: "provider",
        type: "uint8",
      },
      {
        indexed: true,
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
    ],
    name: "FlashloanExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    inputs: [],
    name: "ADDRESSES_PROVIDER",
    outputs: [
      {
        internalType: "contract IPoolAddressesProvider",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "BALANCER_VAULT",
    outputs: [
      {
        internalType: "contract IVault",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "CHAIN_ID",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "POOL",
    outputs: [
      {
        internalType: "contract IPool",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "UNISWAP_V2_ROUTER",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "UNISWAP_V3_ROUTER",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "emergencyWithdraw",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "initiator",
        type: "address",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeOperation",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeOptimalFlashloan",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "getOptimalProvider",
    outputs: [
      {
        internalType: "enum HybridFlashloanArbitrage.FlashloanProvider",
        name: "",
        type: "uint8",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "contract IERC20[]",
        name: "tokens",
        type: "address[]",
      },
      {
        internalType: "uint256[]",
        name: "amounts",
        type: "uint256[]",
      },
      {
        internalType: "uint256[]",
        name: "feeAmounts",
        type: "uint256[]",
      },
      {
        internalType: "bytes",
        name: "userData",
        type: "bytes",
      },
    ],
    name: "receiveFlashLoan",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "withdrawProfits",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type HybridFlashloanArbitrageConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: HybridFlashloanArbitrageConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class HybridFlashloanArbitrage__factory extends ContractFactory {
  constructor(...args: HybridFlashloanArbitrageConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    _aaveAddressProvider: AddressLike,
    _balancerVault: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(
      _aaveAddressProvider,
      _balancerVault,
      overrides || {}
    );
  }
  override deploy(
    _aaveAddressProvider: AddressLike,
    _balancerVault: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ) {
    return super.deploy(
      _aaveAddressProvider,
      _balancerVault,
      overrides || {}
    ) as Promise<
      HybridFlashloanArbitrage & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(
    runner: ContractRunner | null
  ): HybridFlashloanArbitrage__factory {
    return super.connect(runner) as HybridFlashloanArbitrage__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): HybridFlashloanArbitrageInterface {
    return new Interface(_abi) as HybridFlashloanArbitrageInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): HybridFlashloanArbitrage {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as HybridFlashloanArbitrage;
  }
}
