/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type {
  Signer,
  AddressLike,
  ContractDeployTransaction,
  ContractRunner,
} from "ethers";
import type { NonPayableOverrides } from "../../common";
import type {
  SimpleFlashloanArbitrage,
  SimpleFlashloanArbitrageInterface,
} from "../../contracts/SimpleFlashloanArbitrage";

const _abi = [
  {
    inputs: [
      {
        internalType: "contract IPoolAddressesProvider",
        name: "_addressProvider",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
    ],
    name: "ArbitrageExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
    ],
    name: "FlashloanExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    inputs: [],
    name: "ADDRESSES_PROVIDER",
    outputs: [
      {
        internalType: "contract IPoolAddressesProvider",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "CHAIN_ID",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "POOL",
    outputs: [
      {
        internalType: "contract IPool",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "emergencyWithdraw",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "executeFlashloanArbitrage",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "initiator",
        type: "address",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeOperation",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "getBalance",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "withdrawProfits",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type SimpleFlashloanArbitrageConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: SimpleFlashloanArbitrageConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class SimpleFlashloanArbitrage__factory extends ContractFactory {
  constructor(...args: SimpleFlashloanArbitrageConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    _addressProvider: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(_addressProvider, overrides || {});
  }
  override deploy(
    _addressProvider: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ) {
    return super.deploy(_addressProvider, overrides || {}) as Promise<
      SimpleFlashloanArbitrage & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(
    runner: ContractRunner | null
  ): SimpleFlashloanArbitrage__factory {
    return super.connect(runner) as SimpleFlashloanArbitrage__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): SimpleFlashloanArbitrageInterface {
    return new Interface(_abi) as SimpleFlashloanArbitrageInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): SimpleFlashloanArbitrage {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as SimpleFlashloanArbitrage;
  }
}
