/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type { Signer, ContractDeployTransaction, ContractRunner } from "ethers";
import type { NonPayableOverrides } from "../../common";
import type {
  MinimalTest,
  MinimalTestInterface,
} from "../../contracts/MinimalTest";

const _abi = [
  {
    inputs: [],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "deployer",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "timestamp",
        type: "uint256",
      },
    ],
    name: "ContractDeployed",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: "string",
        name: "newMessage",
        type: "string",
      },
    ],
    name: "MessageSet",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    inputs: [],
    name: "deploymentTime",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "getDeploymentInfo",
    outputs: [
      {
        internalType: "address",
        name: "contractOwner",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "deployTime",
        type: "uint256",
      },
      {
        internalType: "string",
        name: "currentMessage",
        type: "string",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "getMessage",
    outputs: [
      {
        internalType: "string",
        name: "",
        type: "string",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "message",
    outputs: [
      {
        internalType: "string",
        name: "",
        type: "string",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "string",
        name: "newMessage",
        type: "string",
      },
    ],
    name: "setMessage",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type MinimalTestConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: MinimalTestConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class MinimalTest__factory extends ContractFactory {
  constructor(...args: MinimalTestConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(overrides || {});
  }
  override deploy(overrides?: NonPayableOverrides & { from?: string }) {
    return super.deploy(overrides || {}) as Promise<
      MinimalTest & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(runner: ContractRunner | null): MinimalTest__factory {
    return super.connect(runner) as MinimalTest__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): MinimalTestInterface {
    return new Interface(_abi) as MinimalTestInterface;
  }
  static connect(address: string, runner?: ContractRunner | null): MinimalTest {
    return new Contract(address, _abi, runner) as unknown as MinimalTest;
  }
}
