/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type { Signer, ContractDeployTransaction, ContractRunner } from "ethers";
import type { NonPayableOverrides } from "../../../common";
import type {
  TestFlashloanReceiver,
  TestFlashloanReceiverInterface,
} from "../../../contracts/TestFlashloanReceiver.sol/TestFlashloanReceiver";

const _abi = [
  {
    inputs: [],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "fee",
        type: "uint256",
      },
    ],
    name: "FlashloanReceived",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "fee",
        type: "uint256",
      },
    ],
    name: "FlashloanRepaid",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "emergencyWithdraw",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "vault",
        type: "address",
      },
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "executeTestFlashloan",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    name: "flashloanBalances",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "flashloanCount",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "getTokenBalance",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "lastFlashloanSuccess",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address[]",
        name: "tokens",
        type: "address[]",
      },
      {
        internalType: "uint256[]",
        name: "amounts",
        type: "uint256[]",
      },
      {
        internalType: "uint256[]",
        name: "feeAmounts",
        type: "uint256[]",
      },
      {
        internalType: "bytes",
        name: "userData",
        type: "bytes",
      },
    ],
    name: "receiveFlashLoan",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "withdrawETH",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    stateMutability: "payable",
    type: "receive",
  },
] as const;

const _bytecode =
  "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";

type TestFlashloanReceiverConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: TestFlashloanReceiverConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class TestFlashloanReceiver__factory extends ContractFactory {
  constructor(...args: TestFlashloanReceiverConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(overrides || {});
  }
  override deploy(overrides?: NonPayableOverrides & { from?: string }) {
    return super.deploy(overrides || {}) as Promise<
      TestFlashloanReceiver & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(
    runner: ContractRunner | null
  ): TestFlashloanReceiver__factory {
    return super.connect(runner) as TestFlashloanReceiver__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): TestFlashloanReceiverInterface {
    return new Interface(_abi) as TestFlashloanReceiverInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): TestFlashloanReceiver {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as TestFlashloanReceiver;
  }
}
