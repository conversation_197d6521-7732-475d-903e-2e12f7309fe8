/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type {
  Signer,
  AddressLike,
  ContractDeployTransaction,
  ContractRunner,
} from "ethers";
import type { NonPayableOverrides } from "../../common";
import type {
  DynamicFlashloanArbitrage,
  DynamicFlashloanArbitrageInterface,
} from "../../contracts/DynamicFlashloanArbitrage";

const _abi = [
  {
    inputs: [
      {
        internalType: "address",
        name: "_addressProvider",
        type: "address",
      },
      {
        internalType: "address",
        name: "_balancerVault",
        type: "address",
      },
      {
        internalType: "address",
        name: "_uniswapV3Router",
        type: "address",
      },
      {
        internalType: "address",
        name: "_uniswapV2Router",
        type: "address",
      },
      {
        internalType: "address",
        name: "_uniswapV3Factory",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    inputs: [],
    name: "ReentrancyGuardReentrantCall",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "tokenA",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "tokenB",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
    ],
    name: "ArbitrageCompleted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "enum DynamicFlashloanArbitrage.FlashloanProvider",
        name: "provider",
        type: "uint8",
      },
      {
        indexed: true,
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
    ],
    name: "FlashloanExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "enum DynamicFlashloanArbitrage.FlashloanProvider",
        name: "provider",
        type: "uint8",
      },
      {
        indexed: false,
        internalType: "string",
        name: "reason",
        type: "string",
      },
    ],
    name: "ProviderSelected",
    type: "event",
  },
  {
    inputs: [],
    name: "ADDRESSES_PROVIDER",
    outputs: [
      {
        internalType: "contract IPoolAddressesProvider",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "BALANCER_VAULT",
    outputs: [
      {
        internalType: "contract IVault",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "CHAIN_ID",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "POOL",
    outputs: [
      {
        internalType: "contract IPool",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "UNISWAP_V2_ROUTER",
    outputs: [
      {
        internalType: "contract IUniswapV2Router02",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "UNISWAP_V3_FACTORY",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "UNISWAP_V3_ROUTER",
    outputs: [
      {
        internalType: "contract ISwapRouter",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "emergencyWithdraw",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        components: [
          {
            internalType: "address",
            name: "tokenA",
            type: "address",
          },
          {
            internalType: "address",
            name: "tokenB",
            type: "address",
          },
          {
            internalType: "address",
            name: "buyDex",
            type: "address",
          },
          {
            internalType: "address",
            name: "sellDex",
            type: "address",
          },
          {
            internalType: "uint24",
            name: "v3Fee",
            type: "uint24",
          },
          {
            internalType: "uint256",
            name: "minProfit",
            type: "uint256",
          },
          {
            internalType: "enum DynamicFlashloanArbitrage.FlashloanProvider",
            name: "provider",
            type: "uint8",
          },
          {
            internalType: "bytes",
            name: "extraData",
            type: "bytes",
          },
        ],
        internalType: "struct DynamicFlashloanArbitrage.ArbitrageParams",
        name: "params",
        type: "tuple",
      },
    ],
    name: "executeArbitrage",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "initiator",
        type: "address",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeOperation",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "contract IERC20[]",
        name: "tokens",
        type: "address[]",
      },
      {
        internalType: "uint256[]",
        name: "amounts",
        type: "uint256[]",
      },
      {
        internalType: "uint256[]",
        name: "feeAmounts",
        type: "uint256[]",
      },
      {
        internalType: "bytes",
        name: "userData",
        type: "bytes",
      },
    ],
    name: "receiveFlashLoan",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        components: [
          {
            internalType: "address",
            name: "tokenA",
            type: "address",
          },
          {
            internalType: "address",
            name: "tokenB",
            type: "address",
          },
          {
            internalType: "address",
            name: "buyDex",
            type: "address",
          },
          {
            internalType: "address",
            name: "sellDex",
            type: "address",
          },
          {
            internalType: "uint24",
            name: "v3Fee",
            type: "uint24",
          },
          {
            internalType: "uint256",
            name: "minProfit",
            type: "uint256",
          },
          {
            internalType: "enum DynamicFlashloanArbitrage.FlashloanProvider",
            name: "provider",
            type: "uint8",
          },
          {
            internalType: "bytes",
            name: "extraData",
            type: "bytes",
          },
        ],
        internalType: "struct DynamicFlashloanArbitrage.ArbitrageParams",
        name: "params",
        type: "tuple",
      },
    ],
    name: "selectOptimalProvider",
    outputs: [
      {
        internalType: "enum DynamicFlashloanArbitrage.FlashloanProvider",
        name: "",
        type: "uint8",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "uint256",
        name: "fee0",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "fee1",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "data",
        type: "bytes",
      },
    ],
    name: "uniswapV3FlashCallback",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [],
    name: "withdrawETH",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    stateMutability: "payable",
    type: "receive",
  },
] as const;

const _bytecode =
  "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";

type DynamicFlashloanArbitrageConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: DynamicFlashloanArbitrageConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class DynamicFlashloanArbitrage__factory extends ContractFactory {
  constructor(...args: DynamicFlashloanArbitrageConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    _addressProvider: AddressLike,
    _balancerVault: AddressLike,
    _uniswapV3Router: AddressLike,
    _uniswapV2Router: AddressLike,
    _uniswapV3Factory: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(
      _addressProvider,
      _balancerVault,
      _uniswapV3Router,
      _uniswapV2Router,
      _uniswapV3Factory,
      overrides || {}
    );
  }
  override deploy(
    _addressProvider: AddressLike,
    _balancerVault: AddressLike,
    _uniswapV3Router: AddressLike,
    _uniswapV2Router: AddressLike,
    _uniswapV3Factory: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ) {
    return super.deploy(
      _addressProvider,
      _balancerVault,
      _uniswapV3Router,
      _uniswapV2Router,
      _uniswapV3Factory,
      overrides || {}
    ) as Promise<
      DynamicFlashloanArbitrage & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(
    runner: ContractRunner | null
  ): DynamicFlashloanArbitrage__factory {
    return super.connect(runner) as DynamicFlashloanArbitrage__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): DynamicFlashloanArbitrageInterface {
    return new Interface(_abi) as DynamicFlashloanArbitrageInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): DynamicFlashloanArbitrage {
    return new Contract(
      address,
      _abi,
      runner
    ) as unknown as DynamicFlashloanArbitrage;
  }
}
