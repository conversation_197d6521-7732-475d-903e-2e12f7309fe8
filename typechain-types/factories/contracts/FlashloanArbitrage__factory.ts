/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import {
  Contract,
  ContractFactory,
  ContractTransactionResponse,
  Interface,
} from "ethers";
import type {
  Signer,
  AddressLike,
  ContractDeployTransaction,
  ContractRunner,
} from "ethers";
import type { NonPayableOverrides } from "../../common";
import type {
  FlashloanArbitrage,
  FlashloanArbitrageInterface,
} from "../../contracts/FlashloanArbitrage";

const _abi = [
  {
    inputs: [
      {
        internalType: "contract IPoolAddressesProvider",
        name: "_addressProvider",
        type: "address",
      },
    ],
    stateMutability: "nonpayable",
    type: "constructor",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "owner",
        type: "address",
      },
    ],
    name: "OwnableInvalidOwner",
    type: "error",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "account",
        type: "address",
      },
    ],
    name: "OwnableUnauthorizedAccount",
    type: "error",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "profit",
        type: "uint256",
      },
    ],
    name: "ArbitrageExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
    ],
    name: "FlashloanExecuted",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "previousOwner",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "OwnershipTransferred",
    type: "event",
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: "address",
        name: "tokenIn",
        type: "address",
      },
      {
        indexed: true,
        internalType: "address",
        name: "tokenOut",
        type: "address",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amountIn",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "uint256",
        name: "amountOut",
        type: "uint256",
      },
      {
        indexed: false,
        internalType: "address",
        name: "router",
        type: "address",
      },
    ],
    name: "SwapExecuted",
    type: "event",
  },
  {
    inputs: [],
    name: "ADDRESSES_PROVIDER",
    outputs: [
      {
        internalType: "contract IPoolAddressesProvider",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "CHAIN_ID",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "POOL",
    outputs: [
      {
        internalType: "contract IPool",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
    ],
    name: "emergencyWithdraw",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeFlashloanArbitrage",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "asset",
        type: "address",
      },
      {
        internalType: "uint256",
        name: "amount",
        type: "uint256",
      },
      {
        internalType: "uint256",
        name: "premium",
        type: "uint256",
      },
      {
        internalType: "address",
        name: "initiator",
        type: "address",
      },
      {
        internalType: "bytes",
        name: "params",
        type: "bytes",
      },
    ],
    name: "executeOperation",
    outputs: [
      {
        internalType: "bool",
        name: "",
        type: "bool",
      },
    ],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "getBalance",
    outputs: [
      {
        internalType: "uint256",
        name: "",
        type: "uint256",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "owner",
    outputs: [
      {
        internalType: "address",
        name: "",
        type: "address",
      },
    ],
    stateMutability: "view",
    type: "function",
  },
  {
    inputs: [],
    name: "renounceOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "newOwner",
        type: "address",
      },
    ],
    name: "transferOwnership",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
  {
    inputs: [
      {
        internalType: "address",
        name: "token",
        type: "address",
      },
    ],
    name: "withdrawProfits",
    outputs: [],
    stateMutability: "nonpayable",
    type: "function",
  },
] as const;

const _bytecode =
  "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";

type FlashloanArbitrageConstructorParams =
  | [signer?: Signer]
  | ConstructorParameters<typeof ContractFactory>;

const isSuperArgs = (
  xs: FlashloanArbitrageConstructorParams
): xs is ConstructorParameters<typeof ContractFactory> => xs.length > 1;

export class FlashloanArbitrage__factory extends ContractFactory {
  constructor(...args: FlashloanArbitrageConstructorParams) {
    if (isSuperArgs(args)) {
      super(...args);
    } else {
      super(_abi, _bytecode, args[0]);
    }
  }

  override getDeployTransaction(
    _addressProvider: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ): Promise<ContractDeployTransaction> {
    return super.getDeployTransaction(_addressProvider, overrides || {});
  }
  override deploy(
    _addressProvider: AddressLike,
    overrides?: NonPayableOverrides & { from?: string }
  ) {
    return super.deploy(_addressProvider, overrides || {}) as Promise<
      FlashloanArbitrage & {
        deploymentTransaction(): ContractTransactionResponse;
      }
    >;
  }
  override connect(runner: ContractRunner | null): FlashloanArbitrage__factory {
    return super.connect(runner) as FlashloanArbitrage__factory;
  }

  static readonly bytecode = _bytecode;
  static readonly abi = _abi;
  static createInterface(): FlashloanArbitrageInterface {
    return new Interface(_abi) as FlashloanArbitrageInterface;
  }
  static connect(
    address: string,
    runner?: ContractRunner | null
  ): FlashloanArbitrage {
    return new Contract(address, _abi, runner) as unknown as FlashloanArbitrage;
  }
}
