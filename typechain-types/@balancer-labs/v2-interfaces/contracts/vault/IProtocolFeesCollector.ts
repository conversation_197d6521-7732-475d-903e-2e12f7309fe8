/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigN<PERSON>berish,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  AddressLike,
  ContractRunner,
  ContractMethod,
  Listener,
} from "ethers";
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from "../../../../common";

export interface IProtocolFeesCollectorInterface extends Interface {
  getFunction(
    nameOrSignature:
      | "getAuthorizer"
      | "getCollectedFeeAmounts"
      | "getFlashLoanFeePercentage"
      | "getSwapFeePercentage"
      | "setFlashLoanFeePercentage"
      | "setSwapFeePercentage"
      | "vault"
      | "withdrawCollectedFees"
  ): FunctionFragment;

  getEvent(
    nameOrSignatureOrTopic:
      | "FlashLoanFeePercentageChanged"
      | "SwapFeePercentageChanged"
  ): EventFragment;

  encodeFunctionData(
    functionFragment: "getAuthorizer",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "getCollectedFeeAmounts",
    values: [AddressLike[]]
  ): string;
  encodeFunctionData(
    functionFragment: "getFlashLoanFeePercentage",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "getSwapFeePercentage",
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: "setFlashLoanFeePercentage",
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: "setSwapFeePercentage",
    values: [BigNumberish]
  ): string;
  encodeFunctionData(functionFragment: "vault", values?: undefined): string;
  encodeFunctionData(
    functionFragment: "withdrawCollectedFees",
    values: [AddressLike[], BigNumberish[], AddressLike]
  ): string;

  decodeFunctionResult(
    functionFragment: "getAuthorizer",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "getCollectedFeeAmounts",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "getFlashLoanFeePercentage",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "getSwapFeePercentage",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "setFlashLoanFeePercentage",
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: "setSwapFeePercentage",
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: "vault", data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: "withdrawCollectedFees",
    data: BytesLike
  ): Result;
}

export namespace FlashLoanFeePercentageChangedEvent {
  export type InputTuple = [newFlashLoanFeePercentage: BigNumberish];
  export type OutputTuple = [newFlashLoanFeePercentage: bigint];
  export interface OutputObject {
    newFlashLoanFeePercentage: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export namespace SwapFeePercentageChangedEvent {
  export type InputTuple = [newSwapFeePercentage: BigNumberish];
  export type OutputTuple = [newSwapFeePercentage: bigint];
  export interface OutputObject {
    newSwapFeePercentage: bigint;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export interface IProtocolFeesCollector extends BaseContract {
  connect(runner?: ContractRunner | null): IProtocolFeesCollector;
  waitForDeployment(): Promise<this>;

  interface: IProtocolFeesCollectorInterface;

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>;
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent
  ): Promise<this>;

  getAuthorizer: TypedContractMethod<[], [string], "view">;

  getCollectedFeeAmounts: TypedContractMethod<
    [tokens: AddressLike[]],
    [bigint[]],
    "view"
  >;

  getFlashLoanFeePercentage: TypedContractMethod<[], [bigint], "view">;

  getSwapFeePercentage: TypedContractMethod<[], [bigint], "view">;

  setFlashLoanFeePercentage: TypedContractMethod<
    [newFlashLoanFeePercentage: BigNumberish],
    [void],
    "nonpayable"
  >;

  setSwapFeePercentage: TypedContractMethod<
    [newSwapFeePercentage: BigNumberish],
    [void],
    "nonpayable"
  >;

  vault: TypedContractMethod<[], [string], "view">;

  withdrawCollectedFees: TypedContractMethod<
    [tokens: AddressLike[], amounts: BigNumberish[], recipient: AddressLike],
    [void],
    "nonpayable"
  >;

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment
  ): T;

  getFunction(
    nameOrSignature: "getAuthorizer"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "getCollectedFeeAmounts"
  ): TypedContractMethod<[tokens: AddressLike[]], [bigint[]], "view">;
  getFunction(
    nameOrSignature: "getFlashLoanFeePercentage"
  ): TypedContractMethod<[], [bigint], "view">;
  getFunction(
    nameOrSignature: "getSwapFeePercentage"
  ): TypedContractMethod<[], [bigint], "view">;
  getFunction(
    nameOrSignature: "setFlashLoanFeePercentage"
  ): TypedContractMethod<
    [newFlashLoanFeePercentage: BigNumberish],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "setSwapFeePercentage"
  ): TypedContractMethod<
    [newSwapFeePercentage: BigNumberish],
    [void],
    "nonpayable"
  >;
  getFunction(
    nameOrSignature: "vault"
  ): TypedContractMethod<[], [string], "view">;
  getFunction(
    nameOrSignature: "withdrawCollectedFees"
  ): TypedContractMethod<
    [tokens: AddressLike[], amounts: BigNumberish[], recipient: AddressLike],
    [void],
    "nonpayable"
  >;

  getEvent(
    key: "FlashLoanFeePercentageChanged"
  ): TypedContractEvent<
    FlashLoanFeePercentageChangedEvent.InputTuple,
    FlashLoanFeePercentageChangedEvent.OutputTuple,
    FlashLoanFeePercentageChangedEvent.OutputObject
  >;
  getEvent(
    key: "SwapFeePercentageChanged"
  ): TypedContractEvent<
    SwapFeePercentageChangedEvent.InputTuple,
    SwapFeePercentageChangedEvent.OutputTuple,
    SwapFeePercentageChangedEvent.OutputObject
  >;

  filters: {
    "FlashLoanFeePercentageChanged(uint256)": TypedContractEvent<
      FlashLoanFeePercentageChangedEvent.InputTuple,
      FlashLoanFeePercentageChangedEvent.OutputTuple,
      FlashLoanFeePercentageChangedEvent.OutputObject
    >;
    FlashLoanFeePercentageChanged: TypedContractEvent<
      FlashLoanFeePercentageChangedEvent.InputTuple,
      FlashLoanFeePercentageChangedEvent.OutputTuple,
      FlashLoanFeePercentageChangedEvent.OutputObject
    >;

    "SwapFeePercentageChanged(uint256)": TypedContractEvent<
      SwapFeePercentageChangedEvent.InputTuple,
      SwapFeePercentageChangedEvent.OutputTuple,
      SwapFeePercentageChangedEvent.OutputObject
    >;
    SwapFeePercentageChanged: TypedContractEvent<
      SwapFeePercentageChangedEvent.InputTuple,
      SwapFeePercentageChangedEvent.OutputTuple,
      SwapFeePercentageChangedEvent.OutputObject
    >;
  };
}
