/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BytesLike,
  FunctionFragment,
  Result,
  Interface,
  EventFragment,
  ContractRunner,
  ContractMethod,
  Listener,
} from "ethers";
import type {
  TypedContractEvent,
  TypedDeferredTopicFilter,
  TypedEventLog,
  TypedLogDescription,
  TypedListener,
  TypedContractMethod,
} from "../../../../../common";

export interface ITemporarilyPausableInterface extends Interface {
  getFunction(nameOrSignature: "getPausedState"): FunctionFragment;

  getEvent(nameOrSignatureOrTopic: "PausedStateChanged"): EventFragment;

  encodeFunctionData(
    functionFragment: "getPausedState",
    values?: undefined
  ): string;

  decodeFunctionResult(
    functionFragment: "getPausedState",
    data: BytesLike
  ): Result;
}

export namespace PausedStateChangedEvent {
  export type InputTuple = [paused: boolean];
  export type OutputTuple = [paused: boolean];
  export interface OutputObject {
    paused: boolean;
  }
  export type Event = TypedContractEvent<InputTuple, OutputTuple, OutputObject>;
  export type Filter = TypedDeferredTopicFilter<Event>;
  export type Log = TypedEventLog<Event>;
  export type LogDescription = TypedLogDescription<Event>;
}

export interface ITemporarilyPausable extends BaseContract {
  connect(runner?: ContractRunner | null): ITemporarilyPausable;
  waitForDeployment(): Promise<this>;

  interface: ITemporarilyPausableInterface;

  queryFilter<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;
  queryFilter<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TypedEventLog<TCEvent>>>;

  on<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  on<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  once<TCEvent extends TypedContractEvent>(
    event: TCEvent,
    listener: TypedListener<TCEvent>
  ): Promise<this>;
  once<TCEvent extends TypedContractEvent>(
    filter: TypedDeferredTopicFilter<TCEvent>,
    listener: TypedListener<TCEvent>
  ): Promise<this>;

  listeners<TCEvent extends TypedContractEvent>(
    event: TCEvent
  ): Promise<Array<TypedListener<TCEvent>>>;
  listeners(eventName?: string): Promise<Array<Listener>>;
  removeAllListeners<TCEvent extends TypedContractEvent>(
    event?: TCEvent
  ): Promise<this>;

  getPausedState: TypedContractMethod<
    [],
    [
      [boolean, bigint, bigint] & {
        paused: boolean;
        pauseWindowEndTime: bigint;
        bufferPeriodEndTime: bigint;
      }
    ],
    "view"
  >;

  getFunction<T extends ContractMethod = ContractMethod>(
    key: string | FunctionFragment
  ): T;

  getFunction(
    nameOrSignature: "getPausedState"
  ): TypedContractMethod<
    [],
    [
      [boolean, bigint, bigint] & {
        paused: boolean;
        pauseWindowEndTime: bigint;
        bufferPeriodEndTime: bigint;
      }
    ],
    "view"
  >;

  getEvent(
    key: "PausedStateChanged"
  ): TypedContractEvent<
    PausedStateChangedEvent.InputTuple,
    PausedStateChangedEvent.OutputTuple,
    PausedStateChangedEvent.OutputObject
  >;

  filters: {
    "PausedStateChanged(bool)": TypedContractEvent<
      PausedStateChangedEvent.InputTuple,
      PausedStateChangedEvent.OutputTuple,
      PausedStateChangedEvent.OutputObject
    >;
    PausedStateChanged: TypedContractEvent<
      PausedStateChangedEvent.InputTuple,
      PausedStateChangedEvent.OutputTuple,
      PausedStateChangedEvent.OutputObject
    >;
  };
}
