// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol";
import "@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol";
import "@aave/core-v3/contracts/interfaces/IPool.sol";
import "@balancer-labs/v2-interfaces/contracts/vault/IVault.sol";
import "@balancer-labs/v2-interfaces/contracts/vault/IFlashLoanRecipient.sol";
import "@uniswap/v3-core/contracts/interfaces/IUniswapV3Pool.sol";
import "@uniswap/v3-core/contracts/interfaces/callback/IUniswapV3FlashCallback.sol";
import "@uniswap/v3-periphery/contracts/interfaces/ISwapRouter.sol";
import "@uniswap/v2-periphery/contracts/interfaces/IUniswapV2Router02.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/utils/ReentrancyGuard.sol";

/**
 * @title DynamicFlashloanArbitrage
 * @dev Advanced contract supporting Aave, Balancer, and Uniswap V3 flashloans with dynamic provider selection
 */
contract DynamicFlashloanArbitrage is
    FlashLoanSimpleReceiverBase,
    IFlashLoanRecipient,
    IUniswapV3FlashCallback,
    Ownable,
    ReentrancyGuard
{

    // Network-specific addresses
    ISwapRouter public immutable UNISWAP_V3_ROUTER;
    IUniswapV2Router02 public immutable UNISWAP_V2_ROUTER;
    IVault public immutable BALANCER_VAULT;
    address public immutable UNISWAP_V3_FACTORY;
    uint256 public immutable CHAIN_ID;
    
    enum FlashloanProvider { AAVE, BALANCER, UNISWAP_V3 }
    
    struct ArbitrageParams {
        address tokenA;
        address tokenB;
        address buyDex;
        address sellDex;
        uint24 v3Fee;
        uint256 minProfit;
        FlashloanProvider provider;
        bytes extraData;
    }

    struct FlashSwapParams {
        address tokenA;
        address tokenB;
        uint24 borrowFee;
        uint24 sellFee;
        uint256 amount;
        uint256 minProfit;
        bool isToken0;
    }
    
    // Events
    event FlashloanExecuted(
        FlashloanProvider indexed provider,
        address indexed asset,
        uint256 amount,
        uint256 premium,
        uint256 profit
    );
    
    event ArbitrageCompleted(
        address indexed tokenA,
        address indexed tokenB,
        uint256 profit
    );

    event ProviderSelected(
        FlashloanProvider indexed provider,
        string reason
    );
    
    constructor(
        address _addressProvider,
        address _balancerVault,
        address _uniswapV3Router,
        address _uniswapV2Router,
        address _uniswapV3Factory
    ) FlashLoanSimpleReceiverBase(IPoolAddressesProvider(_addressProvider)) Ownable(msg.sender) {
        BALANCER_VAULT = IVault(_balancerVault);
        UNISWAP_V3_ROUTER = ISwapRouter(_uniswapV3Router);
        UNISWAP_V2_ROUTER = IUniswapV2Router02(_uniswapV2Router);
        UNISWAP_V3_FACTORY = _uniswapV3Factory;
        CHAIN_ID = block.chainid;
    }

    /**
     * @dev Execute flashloan arbitrage with dynamic provider selection
     * @param params Arbitrage parameters including provider preference
     */
    function executeArbitrage(ArbitrageParams calldata params) external onlyOwner nonReentrant {
        require(params.tokenA != address(0) && params.tokenB != address(0), "Invalid tokens");
        require(params.minProfit > 0, "Invalid min profit");

        // Select optimal provider based on conditions
        FlashloanProvider optimalProvider = selectOptimalProvider(params);
        
        emit ProviderSelected(optimalProvider, "Dynamic selection based on market conditions");

        // Execute based on selected provider
        if (optimalProvider == FlashloanProvider.AAVE) {
            _executeAaveFlashloan(params);
        } else if (optimalProvider == FlashloanProvider.BALANCER) {
            _executeBalancerFlashloan(params);
        } else if (optimalProvider == FlashloanProvider.UNISWAP_V3) {
            _executeUniswapV3FlashSwap(params);
        }
    }

    /**
     * @dev Select optimal flashloan provider based on current conditions
     */
    function selectOptimalProvider(ArbitrageParams calldata params) public view returns (FlashloanProvider) {
        // If user specified a provider, respect it
        if (params.provider != FlashloanProvider.AAVE || 
            params.provider != FlashloanProvider.BALANCER || 
            params.provider != FlashloanProvider.UNISWAP_V3) {
            return params.provider;
        }

        // Dynamic selection logic
        uint256 flashloanAmount = _calculateOptimalAmount(params.tokenA, params.tokenB);
        
        // For large amounts, prefer Balancer (0% fees)
        if (flashloanAmount > 100000 * 10**18) { // > 100k tokens
            return FlashloanProvider.BALANCER;
        }
        
        // For medium amounts, check token availability
        if (flashloanAmount > 10000 * 10**18) { // > 10k tokens
            // Prefer Balancer if available, otherwise Aave
            return FlashloanProvider.BALANCER;
        }
        
        // For small amounts, Uniswap V3 flash swaps might be optimal
        return FlashloanProvider.UNISWAP_V3;
    }

    /**
     * @dev Execute Aave flashloan
     */
    function _executeAaveFlashloan(ArbitrageParams calldata params) internal {
        uint256 amount = _calculateOptimalAmount(params.tokenA, params.tokenB);
        
        POOL.flashLoanSimple(
            address(this),
            params.tokenA,
            amount,
            abi.encode(params),
            0
        );
    }

    /**
     * @dev Execute Balancer flashloan
     */
    function _executeBalancerFlashloan(ArbitrageParams calldata params) internal {
        uint256 amount = _calculateOptimalAmount(params.tokenA, params.tokenB);
        
        IERC20[] memory tokens = new IERC20[](1);
        tokens[0] = IERC20(params.tokenA);
        
        uint256[] memory amounts = new uint256[](1);
        amounts[0] = amount;
        
        BALANCER_VAULT.flashLoan(this, tokens, amounts, abi.encode(params));
    }

    /**
     * @dev Execute Uniswap V3 flash swap
     */
    function _executeUniswapV3FlashSwap(ArbitrageParams calldata params) internal {
        uint256 amount = _calculateOptimalAmount(params.tokenA, params.tokenB);
        
        // Get pool address
        address pool = _getUniswapV3Pool(params.tokenA, params.tokenB, params.v3Fee);
        require(pool != address(0), "Pool not found");
        
        // Determine token order
        address token0 = IUniswapV3Pool(pool).token0();
        bool isToken0 = (params.tokenA == token0);
        
        FlashSwapParams memory flashParams = FlashSwapParams({
            tokenA: params.tokenA,
            tokenB: params.tokenB,
            borrowFee: params.v3Fee,
            sellFee: 3000, // Default sell fee
            amount: amount,
            minProfit: params.minProfit,
            isToken0: isToken0
        });
        
        uint256 amount0 = isToken0 ? amount : 0;
        uint256 amount1 = isToken0 ? 0 : amount;
        
        IUniswapV3Pool(pool).flash(
            address(this),
            amount0,
            amount1,
            abi.encode(flashParams)
        );
    }

    /**
     * @dev Aave flashloan callback
     */
    function executeOperation(
        address asset,
        uint256 amount,
        uint256 premium,
        address initiator,
        bytes calldata params
    ) external override returns (bool) {
        require(initiator == address(this), "Invalid initiator");
        require(msg.sender == address(POOL), "Invalid caller");
        
        ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
        
        uint256 profit = _executeArbitrage(asset, amount, arbParams);
        
        uint256 amountToRepay = amount + premium;
        require(IERC20(asset).balanceOf(address(this)) >= amountToRepay, "Insufficient balance");
        IERC20(asset).transfer(address(POOL), amountToRepay);
        
        emit FlashloanExecuted(FlashloanProvider.AAVE, asset, amount, premium, profit);
        
        return true;
    }

    /**
     * @dev Balancer flashloan callback
     */
    function receiveFlashLoan(
        IERC20[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external override {
        require(msg.sender == address(BALANCER_VAULT), "Invalid caller");
        
        ArbitrageParams memory arbParams = abi.decode(userData, (ArbitrageParams));
        
        uint256 profit = _executeArbitrage(address(tokens[0]), amounts[0], arbParams);
        
        // Balancer has 0% fees!
        uint256 amountToRepay = amounts[0];
        require(tokens[0].balanceOf(address(this)) >= amountToRepay, "Insufficient balance");
        tokens[0].transfer(address(BALANCER_VAULT), amountToRepay);
        
        emit FlashloanExecuted(FlashloanProvider.BALANCER, address(tokens[0]), amounts[0], 0, profit);
    }

    /**
     * @dev Uniswap V3 flash callback
     */
    function uniswapV3FlashCallback(
        uint256 fee0,
        uint256 fee1,
        bytes calldata data
    ) external override {
        FlashSwapParams memory params = abi.decode(data, (FlashSwapParams));
        
        address expectedPool = _getUniswapV3Pool(params.tokenA, params.tokenB, params.borrowFee);
        require(msg.sender == expectedPool, "Invalid pool caller");
        
        // Execute arbitrage
        uint256 profit = _executeV3Arbitrage(params);
        
        // Calculate repayment
        uint256 fee = params.isToken0 ? fee0 : fee1;
        uint256 amountToRepay = params.amount + fee;
        
        require(IERC20(params.tokenA).balanceOf(address(this)) >= amountToRepay, "Insufficient balance");
        
        emit FlashloanExecuted(FlashloanProvider.UNISWAP_V3, params.tokenA, params.amount, fee, profit);
    }

    /**
     * @dev Execute arbitrage logic
     */
    function _executeArbitrage(
        address asset,
        uint256 amount,
        ArbitrageParams memory params
    ) internal returns (uint256 profit) {
        uint256 initialBalance = IERC20(asset).balanceOf(address(this));
        
        // Execute buy and sell operations
        uint256 tokenBAmount = _executeBuy(asset, amount, params);
        uint256 finalAmount = _executeSell(params.tokenB, tokenBAmount, params);
        
        uint256 finalBalance = IERC20(asset).balanceOf(address(this));
        profit = finalBalance - initialBalance;
        
        require(profit >= params.minProfit, "Insufficient profit");
        
        emit ArbitrageCompleted(params.tokenA, params.tokenB, profit);
        
        return profit;
    }

    /**
     * @dev Execute V3-specific arbitrage
     */
    function _executeV3Arbitrage(FlashSwapParams memory params) internal returns (uint256 profit) {
        uint256 initialBalance = IERC20(params.tokenA).balanceOf(address(this));

        // Approve router
        IERC20(params.tokenA).approve(address(UNISWAP_V3_ROUTER), params.amount);

        // Swap tokenA -> tokenB on sell fee tier
        ISwapRouter.ExactInputSingleParams memory swapParams = ISwapRouter.ExactInputSingleParams({
            tokenIn: params.tokenA,
            tokenOut: params.tokenB,
            fee: params.sellFee,
            recipient: address(this),
            deadline: block.timestamp + 300,
            amountIn: params.amount,
            amountOutMinimum: 0,
            sqrtPriceLimitX96: 0
        });

        uint256 tokenBReceived = UNISWAP_V3_ROUTER.exactInputSingle(swapParams);

        // Approve router for reverse swap
        IERC20(params.tokenB).approve(address(UNISWAP_V3_ROUTER), tokenBReceived);

        // Swap tokenB -> tokenA on borrow fee tier
        ISwapRouter.ExactInputSingleParams memory reverseSwapParams = ISwapRouter.ExactInputSingleParams({
            tokenIn: params.tokenB,
            tokenOut: params.tokenA,
            fee: params.borrowFee,
            recipient: address(this),
            deadline: block.timestamp + 300,
            amountIn: tokenBReceived,
            amountOutMinimum: 0,
            sqrtPriceLimitX96: 0
        });

        UNISWAP_V3_ROUTER.exactInputSingle(reverseSwapParams);

        uint256 finalBalance = IERC20(params.tokenA).balanceOf(address(this));
        profit = finalBalance - initialBalance;

        require(profit >= params.minProfit, "V3 arbitrage not profitable");

        return profit;
    }

    /**
     * @dev Execute buy operation
     */
    function _executeBuy(
        address asset,
        uint256 amount,
        ArbitrageParams memory params
    ) internal returns (uint256 tokenBAmount) {
        IERC20(asset).approve(params.buyDex, amount);

        if (params.buyDex == address(UNISWAP_V2_ROUTER)) {
            // Uniswap V2 swap
            address[] memory path = new address[](2);
            path[0] = asset;
            path[1] = params.tokenB;

            uint256[] memory amounts = UNISWAP_V2_ROUTER.swapExactTokensForTokens(
                amount,
                0,
                path,
                address(this),
                block.timestamp + 300
            );

            return amounts[1];
        } else {
            // Uniswap V3 swap
            ISwapRouter.ExactInputSingleParams memory swapParams = ISwapRouter.ExactInputSingleParams({
                tokenIn: asset,
                tokenOut: params.tokenB,
                fee: params.v3Fee,
                recipient: address(this),
                deadline: block.timestamp + 300,
                amountIn: amount,
                amountOutMinimum: 0,
                sqrtPriceLimitX96: 0
            });

            return UNISWAP_V3_ROUTER.exactInputSingle(swapParams);
        }
    }

    /**
     * @dev Execute sell operation
     */
    function _executeSell(
        address tokenB,
        uint256 amount,
        ArbitrageParams memory params
    ) internal returns (uint256 finalAmount) {
        IERC20(tokenB).approve(params.sellDex, amount);

        if (params.sellDex == address(UNISWAP_V2_ROUTER)) {
            // Uniswap V2 swap
            address[] memory path = new address[](2);
            path[0] = tokenB;
            path[1] = params.tokenA;

            uint256[] memory amounts = UNISWAP_V2_ROUTER.swapExactTokensForTokens(
                amount,
                0,
                path,
                address(this),
                block.timestamp + 300
            );

            return amounts[1];
        } else {
            // Uniswap V3 swap
            ISwapRouter.ExactInputSingleParams memory swapParams = ISwapRouter.ExactInputSingleParams({
                tokenIn: tokenB,
                tokenOut: params.tokenA,
                fee: params.v3Fee,
                recipient: address(this),
                deadline: block.timestamp + 300,
                amountIn: amount,
                amountOutMinimum: 0,
                sqrtPriceLimitX96: 0
            });

            return UNISWAP_V3_ROUTER.exactInputSingle(swapParams);
        }
    }

    /**
     * @dev Calculate optimal flashloan amount
     */
    function _calculateOptimalAmount(address tokenA, address tokenB) internal pure returns (uint256) {
        // Simplified calculation - in production, this would use more sophisticated logic
        return 1000 * 10**18; // 1000 tokens
    }

    /**
     * @dev Get Uniswap V3 pool address
     */
    function _getUniswapV3Pool(address tokenA, address tokenB, uint24 fee) internal view returns (address) {
        // This would use the factory to compute pool address
        // Simplified for this example
        return address(0); // Would be computed using CREATE2
    }

    /**
     * @dev Emergency withdrawal function
     */
    function emergencyWithdraw(address token) external onlyOwner {
        uint256 balance = IERC20(token).balanceOf(address(this));
        if (balance > 0) {
            IERC20(token).transfer(owner(), balance);
        }
    }

    /**
     * @dev Withdraw ETH
     */
    function withdrawETH() external onlyOwner {
        uint256 balance = address(this).balance;
        if (balance > 0) {
            payable(owner()).transfer(balance);
        }
    }

    receive() external payable {}
}
