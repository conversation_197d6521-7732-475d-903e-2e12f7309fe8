// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol";
import "@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "hardhat/console.sol";

/**
 * @title WorkingFlashloanArbitrage
 * @dev Working contract to execute flashloan-based arbitrage between DEXs
 */
contract WorkingFlashloanArbitrage is FlashLoanSimpleReceiverBase, Ownable {

    uint256 public immutable CHAIN_ID;
    
    event ArbitrageExecuted(address indexed asset, uint256 amount, uint256 profit);
    event FlashloanExecuted(address indexed asset, uint256 amount, uint256 premium);

    constructor(IPoolAddressesProvider _addressProvider)
        FlashLoanSimpleReceiverBase(_addressProvider)
        Ownable(msg.sender)
    {
        CHAIN_ID = block.chainid;
    }

    /**
     * @dev Execute flashloan arbitrage with real swap functionality
     */
    function executeFlashloanArbitrage(
        address asset,
        uint256 amount,
        address tokenB,
        address buyRouter,
        address sellRouter,
        bytes calldata buySwapData,
        bytes calldata sellSwapData
    ) external onlyOwner {
        console.log("Starting flashloan for asset:", asset);
        console.log("Flashloan amount:", amount);
        console.log("TokenB:", tokenB);
        console.log("Buy router:", buyRouter);
        console.log("Sell router:", sellRouter);
        
        // Encode parameters for the callback
        bytes memory params = abi.encode(asset, tokenB, buyRouter, sellRouter, buySwapData, sellSwapData);
        
        // Execute flashloan
        POOL.flashLoanSimple(
            address(this),
            asset,
            amount,
            params,
            0    // referralCode
        );
    }

    /**
     * @dev Called by Aave pool after receiving the flashloan
     */
    function executeOperation(
        address asset,
        uint256 amount,
        uint256 premium,
        address initiator,
        bytes calldata params
    ) external override returns (bool) {
        console.log("=== FLASHLOAN CALLBACK STARTED ===");
        console.log("Asset:", asset);
        console.log("Amount:", amount);
        console.log("Premium:", premium);
        console.log("Initiator:", initiator);
        
        // Ensure this is called by the Aave pool
        require(msg.sender == address(POOL), "Caller must be pool");
        require(initiator == address(this), "Invalid initiator");
        
        // Record initial balance
        console.log("Initial balance after flashloan:", IERC20(asset).balanceOf(address(this)));
        
        // Decode parameters
        (address tokenA, address tokenB, address buyRouter, address sellRouter, bytes memory buySwapData, bytes memory sellSwapData) = 
            abi.decode(params, (address, address, address, address, bytes, bytes));
        
        console.log("Decoded tokenA:", tokenA);
        console.log("Decoded tokenB:", tokenB);
        console.log("Buy swap data length:", buySwapData.length);
        console.log("Sell swap data length:", sellSwapData.length);
        
        // Execute arbitrage
        _executeArbitrage(asset, amount, tokenB, buyRouter, buySwapData, sellRouter, sellSwapData);
        
        // Calculate total amount to repay (amount + premium)
        uint256 amountToRepay = amount + premium;
        console.log("Amount to repay:", amountToRepay);
        
        // Check final balance and ensure we have enough to repay
        require(IERC20(asset).balanceOf(address(this)) >= amountToRepay, "Insufficient balance to repay flashloan");
        
        // Approve the pool to pull the repayment
        IERC20(asset).approve(address(POOL), amountToRepay);
        console.log("Approved repayment amount:", amountToRepay);
        
        emit FlashloanExecuted(asset, amount, premium);
        emit ArbitrageExecuted(asset, amount, 0);
        
        console.log("=== FLASHLOAN CALLBACK COMPLETED ===");
        return true;
    }

    /**
     * @dev Execute the arbitrage between two DEXs using provided swap data.
     */
    function _executeArbitrage(
        address asset, // tokenA
        uint256 amount, // flashloaned amount of tokenA
        address tokenB, // intermediate token
        address buyRouter,
        bytes memory buySwapData,
        address sellRouter,
        bytes memory sellSwapData
    ) internal returns (uint256 profit) {
        console.log("=== ARBITRAGE EXECUTION STARTED ===");
        
        uint256 initialBalanceA = IERC20(asset).balanceOf(address(this));
        uint256 initialBalanceB = IERC20(tokenB).balanceOf(address(this));
        
        console.log("Initial balance A:", initialBalanceA);
        console.log("Initial balance B:", initialBalanceB);

        // If no swap data provided, skip arbitrage and return 0 profit
        if (buySwapData.length == 0 && sellSwapData.length == 0) {
            console.log("No swap data provided, skipping arbitrage");
            return 0;
        }

        // Step 1: Execute buy swap (tokenA -> tokenB)
        console.log("--- STEP 1: BUY SWAP (A -> B) ---");
        
        uint256 tokenBReceived;
        if (buySwapData.length > 0) {
            IERC20(asset).approve(buyRouter, amount);
            console.log("Approved", amount, "of tokenA to buyRouter");

            (bool success,) = buyRouter.call(buySwapData);
            require(success, "Buy swap failed");
            console.log("Buy swap success:", success);

            uint256 balanceBAfterBuy = IERC20(tokenB).balanceOf(address(this));
            tokenBReceived = balanceBAfterBuy - initialBalanceB;
        } else {
            console.log("No buy swap data provided, skipping buy swap");
            tokenBReceived = 0;
        }
        
        console.log("TokenB received from buy swap:", tokenBReceived);

        // Step 2: Execute sell swap (tokenB -> tokenA)
        console.log("--- STEP 2: SELL SWAP (B -> A) ---");
        
        if (sellSwapData.length > 0 && tokenBReceived > 0) {
            IERC20(tokenB).approve(sellRouter, tokenBReceived);
            console.log("Approved", tokenBReceived, "of tokenB to sellRouter");

            (bool success,) = sellRouter.call(sellSwapData);
            require(success, "Sell swap failed");
            console.log("Sell swap success:", success);
        } else {
            console.log("No sell swap data provided or no tokenB to sell, skipping sell swap");
        }

        uint256 finalBalanceA = IERC20(asset).balanceOf(address(this));
        
        console.log("Final balance A:", finalBalanceA);
        
        // Calculate profit
        profit = finalBalanceA > initialBalanceA ? finalBalanceA - initialBalanceA : 0;
        console.log("Calculated profit:", profit);
        
        console.log("=== ARBITRAGE EXECUTION COMPLETED ===");
        return profit;
    }

    /**
     * @dev Get contract balance for a specific token
     */
    function getBalance(address token) external view returns (uint256) {
        return IERC20(token).balanceOf(address(this));
    }

    /**
     * @dev Withdraw profits to owner
     */
    function withdrawProfits(address token) external onlyOwner {
        uint256 balance = IERC20(token).balanceOf(address(this));
        require(balance > 0, "No profits to withdraw");
        IERC20(token).transfer(owner(), balance);
    }

    /**
     * @dev Emergency function to withdraw any stuck tokens
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).transfer(owner(), amount);
    }
}
