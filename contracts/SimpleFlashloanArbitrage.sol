// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol";
import "@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "hardhat/console.sol";

/**
 * @title SimpleFlashloanArbitrage
 * @dev Simplified contract to execute flashloan-based arbitrage between DEXs
 */
contract SimpleFlashloanArbitrage is FlashLoanSimpleReceiverBase, Ownable {

    uint256 public immutable CHAIN_ID;
    
    event ArbitrageExecuted(address indexed asset, uint256 amount, uint256 profit);
    event FlashloanExecuted(address indexed asset, uint256 amount, uint256 premium);

    constructor(IPoolAddressesProvider _addressProvider)
        FlashLoanSimpleReceiverBase(_addressProvider)
        Ownable(msg.sender)
    {
        CHAIN_ID = block.chainid;
    }

    /**
     * @dev Execute flashloan arbitrage
     */
    function executeFlashloanArbitrage(
        address asset,
        uint256 amount
    ) external onlyOwner {
        console.log("Starting flashloan for asset:", asset);
        console.log("Flashloan amount:", amount);
        
        // Execute flashloan
        POOL.flashLoanSimple(
            address(this),
            asset,
            amount,
            "",  // Empty params for simplicity
            0    // referralCode
        );
    }

    /**
     * @dev Called by Aave pool after receiving the flashloan
     */
    function executeOperation(
        address asset,
        uint256 amount,
        uint256 premium,
        address initiator,
        bytes calldata params
    ) external override returns (bool) {
        console.log("=== FLASHLOAN CALLBACK STARTED ===");
        console.log("Asset:", asset);
        console.log("Amount:", amount);
        console.log("Premium:", premium);
        console.log("Initiator:", initiator);
        
        // Ensure this is called by the Aave pool
        require(msg.sender == address(POOL), "Caller must be pool");
        require(initiator == address(this), "Invalid initiator");
        
        // Record initial balance
        uint256 initialBalance = IERC20(asset).balanceOf(address(this));
        console.log("Initial balance after flashloan:", initialBalance);
        
        // For testing: just simulate some work without actual swaps
        console.log("Simulating arbitrage work...");
        
        // Calculate total amount to repay (amount + premium)
        uint256 amountToRepay = amount + premium;
        console.log("Amount to repay:", amountToRepay);
        
        // Check final balance
        uint256 finalBalance = IERC20(asset).balanceOf(address(this));
        console.log("Final balance before repayment:", finalBalance);
        
        // Ensure we have enough to repay
        require(finalBalance >= amountToRepay, "Insufficient balance to repay flashloan");
        
        // Approve the pool to pull the repayment
        IERC20(asset).approve(address(POOL), amountToRepay);
        console.log("Approved repayment amount:", amountToRepay);
        
        emit FlashloanExecuted(asset, amount, premium);
        emit ArbitrageExecuted(asset, amount, 0); // No profit in this simple test
        
        console.log("=== FLASHLOAN CALLBACK COMPLETED ===");
        return true;
    }

    /**
     * @dev Get contract balance for a specific token
     */
    function getBalance(address token) external view returns (uint256) {
        return IERC20(token).balanceOf(address(this));
    }

    /**
     * @dev Withdraw profits to owner
     */
    function withdrawProfits(address token) external onlyOwner {
        uint256 balance = IERC20(token).balanceOf(address(this));
        require(balance > 0, "No profits to withdraw");
        IERC20(token).transfer(owner(), balance);
    }

    /**
     * @dev Emergency function to withdraw any stuck tokens
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).transfer(owner(), amount);
    }
}
