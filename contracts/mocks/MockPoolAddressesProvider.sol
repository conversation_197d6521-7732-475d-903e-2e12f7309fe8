// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

// Import the specific Aave V3 interface for IPoolAddressesProvider
import "@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol";
// Import the specific Aave V3 interface for IPool (needed for constructor parameter type)
import "@aave/core-v3/contracts/interfaces/IPool.sol";

contract MockPoolAddressesProvider is IPoolAddressesProvider {
    address public immutable MOCK_POOL_ADDRESS;

    constructor(address _mockPoolAddress) {
        MOCK_POOL_ADDRESS = _mockPoolAddress;
    }

    function getPool() external view override returns (address) {
        return MOCK_POOL_ADDRESS;
    }

    function getAddress(bytes32 id) external view override returns (address) {
        return address(0);
    }

    // --- Missing implementations from IPoolAddressesProvider.sol ---
    function getACLAdmin() external view override returns (address) { return address(0); }
    function getACLManager() external view override returns (address) { return address(0); }
    function getMarketId() external view override returns (string memory) { return ""; }
    function getPoolConfigurator() external view override returns (address) { return address(0); }
    function getPoolDataProvider() external view override returns (address) { return address(0); }
    function getPriceOracle() external view override returns (address) { return address(0); }
    function getPriceOracleSentinel() external view override returns (address) { return address(0); }
    function setACLAdmin(address newAclAdmin) external override {}
    function setACLManager(address newAclManager) external override {}
    function setAddress(bytes32 id, address newAddress) external override {}
    function setAddressAsProxy(bytes32 id, address newImplementationAddress) external override {}
    function setMarketId(string calldata newMarketId) external override {}
    function setPoolConfiguratorImpl(address newPoolConfiguratorImpl) external override {}
    function setPoolDataProvider(address newDataProvider) external override {}
    function setPoolImpl(address newPoolImpl) external override {}
    function setPriceOracle(address newPriceOracle) external override {}
    function setPriceOracleSentinel(address newPriceOracleSentinel) external override {}
}
