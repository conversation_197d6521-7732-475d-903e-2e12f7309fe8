// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@aave/core-v3/contracts/flashloan/interfaces/IFlashLoanSimpleReceiver.sol";

// Helper Mock Aave Pool contract for testing
// This contract simulates the Aave Pool's behavior of calling executeOperation
// on the FlashloanReceiver contract.
contract MockAavePool {
    function flashLoanSimple(
        address receiverAddress,
        address asset,
        uint256 amount,
        bytes calldata params,
        uint16 referralCode
    ) external {
        // Transfer asset to receiver
        IERC20(asset).transfer(receiverAddress, amount);

        // Call executeOperation on the receiver
        bool success = IFlashLoanSimpleReceiver(receiverAddress).executeOperation(
            asset,
            amount,
            0, // Mock premium as 0 for simplicity
            receiverAddress, // Initiator should be the receiverAddress (FlashloanArbitrage contract)
            params
        );
        
        require(success, "executeOperation failed");

        // Transfer asset back from receiver (simulate repayment)
        // The receiver should have approved this contract to spend the repayment amount
        IERC20(asset).transferFrom(receiverAddress, address(this), amount);
    }
}
