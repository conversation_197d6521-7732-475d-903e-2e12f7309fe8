// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "hardhat/console.sol"; // Import console.log

contract MockDEXRouter {
    // Define the struct for exactInputSingle parameters
    struct ExactInputSingleParams {
        address tokenIn;
        address tokenOut;
        uint24 fee;
        address recipient;
        uint256 deadline;
        uint256 amountIn;
        uint160 sqrtPriceLimitX96;
        uint256 amountOutMinimum; // Moved to end for compatibility with some interfaces
    }

    // Simple mock for swapExactTokensForTokens
    function swapExactTokensForTokens(
        uint amountIn,
        uint amountOutMin,
        address[] calldata path,
        address to,
        uint deadline
    ) external returns (uint[] memory amounts) {
        require(path.length == 2, "MockDEXRouter: Invalid path length");
        IERC20 tokenIn = IERC20(path[0]);
        IERC20 tokenOut = IERC20(path[1]);

        // Simulate a 1:1 swap for simplicity in mock
        uint simulatedAmountOut = amountIn; // Assume 1:1 for mock
        require(simulatedAmountOut >= amountOutMin, "MockDEXRouter: Slippage too high");

        console.log("MockDEXRouter: swapExactTokensForTokens called");
        console.log("  amountIn:", amountIn);
        console.log("  tokenIn:", path[0]);
        console.log("  tokenOut:", path[1]);
        console.log("  to:", to);
        console.log("  msg.sender (FlashloanArbitrage):", msg.sender);
        console.log("  MockDEXRouter balance of tokenIn before transferFrom:", tokenIn.balanceOf(address(this)));
        console.log("  FlashloanArbitrage balance of tokenIn before transferFrom:", tokenIn.balanceOf(msg.sender));

        // Transfer tokenIn from msg.sender (FlashloanArbitrage) to this router
        tokenIn.transferFrom(msg.sender, address(this), amountIn);

        console.log("  MockDEXRouter balance of tokenIn after transferFrom:", tokenIn.balanceOf(address(this)));
        console.log("  FlashloanArbitrage balance of tokenIn after transferFrom:", tokenIn.balanceOf(msg.sender));
        console.log("  MockDEXRouter balance of tokenOut before transfer:", tokenOut.balanceOf(address(this)));
        console.log("  Recipient balance of tokenOut before transfer:", tokenOut.balanceOf(to));

        // Transfer tokenOut from this router to 'to' (FlashloanArbitrage)
        tokenOut.transfer(to, simulatedAmountOut);

        console.log("  MockDEXRouter balance of tokenOut after transfer:", tokenOut.balanceOf(address(this)));
        console.log("  Recipient balance of tokenOut after transfer:", tokenOut.balanceOf(to));

        amounts = new uint[](2);
        amounts[0] = amountIn;
        amounts[1] = simulatedAmountOut;
    }

    // Simple mock for exactInputSingle (Uniswap V3 style)
    function exactInputSingle(
        ExactInputSingleParams calldata params
    ) external returns (uint256 amountOut) { // Removed payable
        uint simulatedAmountOut = params.amountIn; // Assume 1:1 for mock
        require(simulatedAmountOut >= params.amountOutMinimum, "MockDEXRouter: Slippage too high");

        console.log("MockDEXRouter: exactInputSingle called");
        console.log("  amountIn:", params.amountIn);
        console.log("  tokenIn:", params.tokenIn);
        console.log("  tokenOut:", params.tokenOut);
        console.log("  recipient:", params.recipient);
        console.log("  msg.sender (FlashloanArbitrage):", msg.sender);
        console.log("  MockDEXRouter balance of tokenIn before transferFrom:", IERC20(params.tokenIn).balanceOf(address(this)));
        console.log("  FlashloanArbitrage balance of tokenIn before transferFrom:", IERC20(params.tokenIn).balanceOf(msg.sender));

        IERC20(params.tokenIn).transferFrom(msg.sender, address(this), params.amountIn);

        console.log("  MockDEXRouter balance of tokenIn after transferFrom:", IERC20(params.tokenIn).balanceOf(address(this)));
        console.log("  FlashloanArbitrage balance of tokenIn after transferFrom:", IERC20(params.tokenIn).balanceOf(msg.sender));
        console.log("  MockDEXRouter balance of tokenOut before transfer:", IERC20(params.tokenOut).balanceOf(address(this)));
        console.log("  Recipient balance of tokenOut before transfer:", IERC20(params.tokenOut).balanceOf(params.recipient));

        IERC20(params.tokenOut).transfer(params.recipient, simulatedAmountOut);

        console.log("  MockDEXRouter balance of tokenOut after transfer:", IERC20(params.tokenOut).balanceOf(address(this)));
        console.log("  Recipient balance of tokenOut after transfer:", IERC20(params.tokenOut).balanceOf(params.recipient));

        return simulatedAmountOut;
    }

    // Simple test function to verify low-level calls
    function testTransfer(address token, address recipient, uint256 amount) public { // Changed to public
        IERC20(token).transfer(recipient, amount);
        console.log("MockDEXRouter: testTransfer called");
        console.log("  token:", token);
        console.log("  recipient:", recipient);
        console.log("  amount:", amount);
    }

    // Removed receive() external payable {}
}
