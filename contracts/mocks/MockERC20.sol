// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/ERC20.sol";

contract MockERC20 is ERC20 {
    // OpenZeppelin ERC20 constructor typically sets decimals to 18 by default.
    // If custom decimals are needed, a different approach or base contract might be required.
    constructor(string memory name, string memory symbol) ERC20(name, symbol) {
        // _setupDecimals(decimals); // This function is not directly callable
    }

    // Function to set decimals for testing purposes if needed, or rely on default 18
    function setDecimals(uint8 decimals) public {
        // This is a mock, so we can directly set the internal _decimals variable if it exists
        // In OpenZeppelin's ERC20, decimals is usually a constant or derived.
        // For a mock, we can just assume it's 18 or handle it differently.
        // For now, let's remove the problematic line and assume default 18.
    }

    function mint(address to, uint256 amount) public {
        _mint(to, amount);
    }
}
