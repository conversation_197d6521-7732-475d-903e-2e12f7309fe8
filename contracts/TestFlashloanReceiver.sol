// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

// Balancer Vault interface
interface IBalancerVault {
    function flashLoan(
        address recipient,
        address[] memory tokens,
        uint256[] memory amounts,
        bytes memory userData
    ) external;
}

/**
 * @title TestFlashloanReceiver
 * @dev Simple contract to test atomic flashloan behavior
 * This contract receives flashloans and immediately repays them to verify atomicity
 */
contract TestFlashloanReceiver is Ownable {
    
    // Events
    event FlashloanReceived(address token, uint256 amount, uint256 fee);
    event FlashloanRepaid(address token, uint256 amount, uint256 fee);
    
    // State variables
    mapping(address => uint256) public flashloanBalances;
    uint256 public flashloanCount;
    bool public lastFlashloanSuccess;
    
    constructor() Ownable(msg.sender) {}
    
    /**
     * @dev Balancer flashloan callback
     * This function is called by Balancer Vault during flashloan execution
     */
    function receiveFlashLoan(
        address[] memory tokens,
        uint256[] memory amounts,
        uint256[] memory feeAmounts,
        bytes memory userData
    ) external {
        // Verify caller is Balancer Vault
        require(msg.sender == 0xBA12222222228d8Ba445958a75a0704d566BF2C8, "Invalid caller");
        
        flashloanCount++;
        
        // Process each token
        for (uint256 i = 0; i < tokens.length; i++) {
            address token = tokens[i];
            uint256 amount = amounts[i];
            uint256 fee = feeAmounts[i];
            
            emit FlashloanReceived(token, amount, fee);
            
            // Record the flashloan balance
            flashloanBalances[token] = amount;
            
            // Verify we received the tokens
            uint256 balance = IERC20(token).balanceOf(address(this));
            require(balance >= amount, "Flashloan tokens not received");
            
            // Simulate some work (in real scenario, this would be arbitrage logic)
            // For testing, we just verify the balance and prepare repayment
            
            // Calculate total repayment (amount + fee)
            uint256 totalRepayment = amount + fee;
            
            // For Balancer, fees are typically 0, but we handle it anyway
            // In a real scenario, the arbitrage profit would cover any fees
            
            // Approve the vault to take back the tokens
            IERC20(token).approve(msg.sender, totalRepayment);
            
            emit FlashloanRepaid(token, amount, fee);
        }
        
        lastFlashloanSuccess = true;
    }
    
    /**
     * @dev Execute a test flashloan
     */
    function executeTestFlashloan(
        address vault,
        address token,
        uint256 amount
    ) external onlyOwner {
        address[] memory tokens = new address[](1);
        uint256[] memory amounts = new uint256[](1);
        
        tokens[0] = token;
        amounts[0] = amount;
        
        bytes memory userData = "";
        
        // Reset state
        lastFlashloanSuccess = false;
        
        // Execute flashloan
        IBalancerVault(vault).flashLoan(
            address(this),
            tokens,
            amounts,
            userData
        );
    }
    
    /**
     * @dev Get contract balance for a token
     */
    function getTokenBalance(address token) external view returns (uint256) {
        return IERC20(token).balanceOf(address(this));
    }
    
    /**
     * @dev Emergency withdrawal function
     */
    function emergencyWithdraw(address token) external onlyOwner {
        uint256 balance = IERC20(token).balanceOf(address(this));
        if (balance > 0) {
            IERC20(token).transfer(owner(), balance);
        }
    }
    
    /**
     * @dev Withdraw ETH
     */
    function withdrawETH() external onlyOwner {
        uint256 balance = address(this).balance;
        if (balance > 0) {
            payable(owner()).transfer(balance);
        }
    }
    
    /**
     * @dev Receive ETH
     */
    receive() external payable {}
}
