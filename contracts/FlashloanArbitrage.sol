// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@aave/core-v3/contracts/flashloan/base/FlashLoanSimpleReceiverBase.sol";
import "@aave/core-v3/contracts/interfaces/IPoolAddressesProvider.sol";
import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "hardhat/console.sol";

/**
 * @title FlashloanArbitrage
 * @dev Contract to execute flashloan-based arbitrage between DEXs
 */
contract FlashloanArbitrage is FlashLoanSimpleReceiverBase, Ownable {

    // Network detection
    uint256 public immutable CHAIN_ID;
    
    struct ArbitrageParams {
        address tokenA;
        address tokenB;
        address buyRouter;  // Router address for buying
        address sellRouter; // Router address for selling
        bytes buySwapData;  // Encoded calldata for the buy swap
        bytes sellSwapData; // Encoded calldata for the sell swap
    }
    
    event ArbitrageExecuted(
        address indexed asset,
        uint256 amount,
        uint256 profit
    );
    
    event FlashloanExecuted(
        address indexed asset,
        uint256 amount,
        uint256 premium
    );

    event SwapExecuted(
        address indexed tokenIn,
        address indexed tokenOut,
        uint256 amountIn,
        uint256 amountOut,
        address router
    );

    constructor(IPoolAddressesProvider _addressProvider)
        FlashLoanSimpleReceiverBase(_addressProvider)
        Ownable(msg.sender)
    {
        CHAIN_ID = block.chainid;
    }

    /**
     * @dev Execute flashloan arbitrage
     * @param asset The asset to flashloan
     * @param amount The amount to flashloan
     * @param params Encoded arbitrage parameters
     */
    function executeFlashloanArbitrage(
        address asset,
        uint256 amount,
        bytes calldata params
    ) external onlyOwner {
        console.log("=== STARTING FLASHLOAN ARBITRAGE ===");
        console.log("Asset:", asset);
        console.log("Amount:", amount);
        console.log("Params length:", params.length);
        
        // Basic validation first
        require(amount > 0, "Invalid amount");
        
        // Decode arbitrage parameters
        ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
        console.log("Successfully decoded params");
        console.log("TokenA:", arbParams.tokenA);
        console.log("TokenB:", arbParams.tokenB);
        
        // Validate parameters
        require(asset == arbParams.tokenA, "Asset mismatch");
        
        console.log("Starting flashloan for asset:", asset);
        console.log("Flashloan amount:", amount);
        console.log("POOL address:", address(POOL));
        console.log("Asset balance in pool:", IERC20(asset).balanceOf(address(POOL)));
        
        // Execute flashloan
        POOL.flashLoanSimple(
            address(this),
            asset,
            amount,
            params,
            0 // referralCode
        );
    }

    /**
     * @dev Called by Aave pool after receiving the flashloan
     * @param asset The asset that was borrowed
     * @param amount The amount that was borrowed
     * @param premium The fee for the flashloan
     * @param initiator The address that initiated the flashloan
     * @param params The encoded arbitrage parameters
     */
    function executeOperation(
        address asset,
        uint256 amount,
        uint256 premium,
        address initiator,
        bytes calldata params
    ) external override returns (bool) {
        console.log("=== FLASHLOAN CALLBACK STARTED ===");
        console.log("Asset:", asset);
        console.log("Amount:", amount);
        console.log("Premium:", premium);
        console.log("Initiator:", initiator);
        
        // Ensure this is called by the Aave pool
        require(msg.sender == address(POOL), "Caller must be pool");
        require(initiator == address(this), "Invalid initiator");
        
        // Record initial balance
        uint256 initialBalance = IERC20(asset).balanceOf(address(this));
        console.log("Initial balance after flashloan:", initialBalance);
        
        // Decode arbitrage parameters
        ArbitrageParams memory arbParams = abi.decode(params, (ArbitrageParams));
        console.log("Decoded params - TokenA:", arbParams.tokenA);
        console.log("Decoded params - TokenB:", arbParams.tokenB);
        console.log("Decoded params - BuyRouter:", arbParams.buyRouter);
        console.log("Decoded params - SellRouter:", arbParams.sellRouter);
        
        // Execute arbitrage
        uint256 profit = _executeArbitrage(
            asset, 
            amount, 
            arbParams.tokenB, 
            arbParams.buyRouter, 
            arbParams.buySwapData, 
            arbParams.sellRouter, 
            arbParams.sellSwapData
        );
        
        // Calculate total amount to repay (amount + premium)
        uint256 amountToRepay = amount + premium;
        console.log("Amount to repay:", amountToRepay);
        
        // Check final balance
        uint256 finalBalance = IERC20(asset).balanceOf(address(this));
        console.log("Final balance before repayment:", finalBalance);
        
        // Ensure we have enough to repay
        require(
            finalBalance >= amountToRepay,
            "Insufficient balance to repay flashloan"
        );
        
        // Approve the pool to pull the repayment
        IERC20(asset).approve(address(POOL), amountToRepay);
        console.log("Approved repayment amount:", amountToRepay);
        
        emit FlashloanExecuted(asset, amount, premium);
        emit ArbitrageExecuted(asset, amount, profit);
        
        console.log("=== FLASHLOAN CALLBACK COMPLETED ===");
        return true;
    }

    /**
     * @dev Execute the arbitrage between two DEXs using provided swap data.
     * @param asset The asset being arbitraged (flashloaned token)
     * @param amount The amount of asset flashloaned
     * @param tokenB The intermediate token in the arbitrage path
     * @param buyRouter The address of the router for the buy swap
     * @param buySwapData Encoded calldata for the buy swap
     * @param sellRouter The address of the router for the sell swap
     * @param sellSwapData Encoded calldata for the sell swap
     * @return profit The profit made from arbitrage
     */
    function _executeArbitrage(
        address asset, // tokenA
        uint256 amount, // flashloaned amount of tokenA
        address tokenB, // intermediate token
        address buyRouter,
        bytes memory buySwapData,
        address sellRouter,
        bytes memory sellSwapData
    ) internal returns (uint256 profit) {
        console.log("=== ARBITRAGE EXECUTION STARTED ===");
        
        uint256 initialBalanceA = IERC20(asset).balanceOf(address(this));
        uint256 initialBalanceB = IERC20(tokenB).balanceOf(address(this));
        
        console.log("Initial balance A:", initialBalanceA);
        console.log("Initial balance B:", initialBalanceB);

        // If no swap data provided, skip arbitrage and return 0 profit
        if (buySwapData.length == 0 && sellSwapData.length == 0) {
            console.log("No swap data provided, skipping arbitrage");
            return 0;
        }

        // Step 1: Execute buy swap (tokenA -> tokenB)
        console.log("--- STEP 1: BUY SWAP (A -> B) ---");
        
        uint256 tokenBReceived;
        if (buySwapData.length > 0) {
            IERC20(asset).approve(buyRouter, amount);
            console.log("Approved", amount, "of tokenA to buyRouter");

            (bool success,) = buyRouter.call(buySwapData);
            require(success, "Buy swap failed");
            console.log("Buy swap success:", success);

            uint256 balanceBAfterBuy = IERC20(tokenB).balanceOf(address(this));
            tokenBReceived = balanceBAfterBuy - initialBalanceB;
        } else {
            // Skip buy swap
            console.log("No buy swap data provided, skipping buy swap");
            tokenBReceived = 0;
        }
        
        console.log("TokenB received from buy swap:", tokenBReceived);

        // Step 2: Execute sell swap (tokenB -> tokenA)
        console.log("--- STEP 2: SELL SWAP (B -> A) ---");
        
        if (sellSwapData.length > 0 && tokenBReceived > 0) {
            bytes memory updatedSellSwapData = _updateSwapDataAmount(sellSwapData, tokenBReceived);
            
            IERC20(tokenB).approve(sellRouter, tokenBReceived);
            console.log("Approved", tokenBReceived, "of tokenB to sellRouter");

            (bool success,) = sellRouter.call(updatedSellSwapData);
            require(success, "Sell swap failed");
            console.log("Sell swap success:", success);
        } else {
            console.log("No sell swap data provided or no tokenB to sell, skipping sell swap");
        }

        uint256 finalBalanceA = IERC20(asset).balanceOf(address(this));
        
        console.log("Final balance A:", finalBalanceA);
        
        // Calculate profit
        profit = finalBalanceA > initialBalanceA ? finalBalanceA - initialBalanceA : 0;
        console.log("Calculated profit:", profit);
        
        if (buySwapData.length > 0) {
            emit SwapExecuted(asset, tokenB, amount, tokenBReceived, buyRouter);
        }
        if (sellSwapData.length > 0 && tokenBReceived > 0) {
            emit SwapExecuted(tokenB, asset, tokenBReceived, finalBalanceA - initialBalanceA + amount, sellRouter);
        }
        
        console.log("=== ARBITRAGE EXECUTION COMPLETED ===");
        return profit;
    }

    /**
     * @dev Update swap data with actual amount (simplified version)
     * In production, this would need more sophisticated parsing based on function signatures
     */
    function _updateSwapDataAmount(bytes memory swapData, uint256 newAmount) internal pure returns (bytes memory) {
        // For simplicity, we'll assume the amount is in the first parameter position
        // This is a basic implementation - production code would need proper ABI decoding
        if (swapData.length >= 68) { // 4 bytes selector + 32 bytes first param + 32 bytes second param
            assembly {
                // Update the first parameter (assuming it's the amount)
                mstore(add(swapData, 36), newAmount) // 4 bytes selector + 32 bytes offset
            }
        }
        return swapData;
    }

    /**
     * @dev Withdraw profits to owner
     */
    function withdrawProfits(address token) external onlyOwner {
        uint256 balance = IERC20(token).balanceOf(address(this));
        require(balance > 0, "No profits to withdraw");
        IERC20(token).transfer(owner(), balance);
    }

    /**
     * @dev Emergency function to withdraw any stuck tokens
     */
    function emergencyWithdraw(address token, uint256 amount) external onlyOwner {
        IERC20(token).transfer(owner(), amount);
    }

    /**
     * @dev Get contract balance for a specific token
     */
    function getBalance(address token) external view returns (uint256) {
        return IERC20(token).balanceOf(address(this));
    }
}
